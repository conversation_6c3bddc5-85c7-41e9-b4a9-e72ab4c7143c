# 主页面布局重设计

## 任务概述
重新设计主页面布局，实现垂直居中的主内容区域和四角按钮布局。

## 设计需求
1. **主内容区域**：搜索框+文件夹tab+文件夹内容，在可视部分垂直居中
2. **左下角按钮组**：标签页、标签（纵向排列）
3. **右下角按钮组**：主题、设置（纵向排列）
4. **滑动展开面板**：标签页列表、标签筛选、设置面板

## 技术实现方案
- 使用CSS Grid或Flexbox实现垂直居中布局
- 创建SideButton组件用于四角按钮
- 使用Vue transition实现滑动动画
- 保持响应式设计和主题适配

## 实施步骤
1. ✅ 创建任务上下文文件
2. ✅ 创建SideButton组件
3. ✅ 重构App.vue布局结构
4. ✅ 实现滑动展开面板
5. ✅ 调整响应式设计
6. ⏳ 测试和优化

## 涉及文件
- `src/App.vue` - 主布局重构
- `src/components/SideButton.vue` - 新建侧边按钮组件
- `src/components/TabsPanel.vue` - 新建标签页面板
- `src/components/TagFilterPanel.vue` - 新建标签筛选面板

## 实现详情
### 1. SideButton组件
- 圆形毛玻璃按钮设计
- 支持左右位置定位
- hover时显示标签文字
- 激活状态的视觉反馈
- 完整的主题适配

### 2. 布局重构
- 主内容区域垂直居中
- 左下角：标签页、标签按钮
- 右下角：主题、设置按钮
- 移除了Chrome风格顶部留白

### 3. 滑动面板
- TabsPanel：浏览器标签页管理
- TagFilterPanel：标签筛选功能
- 模态框样式，支持ESC关闭
- 拖拽保存标签页为书签

## 当前进度
✅ 核心功能已完成，等待测试验证
