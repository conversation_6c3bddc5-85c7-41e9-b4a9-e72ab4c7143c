<!DOCTYPE html>
<html lang="zh-CN" data-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modal Debug</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/daisyui@5.0.43/dist/full.css" rel="stylesheet" type="text/css" />
    <style>
        /* 模拟左侧抽屉 */
        .drawer-simulation {
            position: fixed;
            top: 0;
            left: 0;
            width: 360px;
            height: 100vh;
            background: #f3f4f6;
            z-index: 10100;
            border-right: 1px solid #e5e7eb;
        }
        
        /* CSS变量 */
        body {
            --drawer-offset: 90px;
            --drawer-offset-mobile: 60px;
        }
        
        /* 模态框推移样式测试 */
        .modal-with-drawer-offset-test1 {
            left: var(--drawer-offset, 0px) !important;
        }
        
        .modal-with-drawer-offset-test2 {
            transform: translateX(var(--drawer-offset, 0px)) !important;
        }
        
        .modal-with-drawer-offset-test3 {
            margin-left: var(--drawer-offset, 0px) !important;
        }
    </style>
</head>
<body>
    <div class="p-8">
        <h1 class="text-2xl font-bold mb-4">Modal Debug Test</h1>
        
        <!-- 模拟左侧抽屉 -->
        <div class="drawer-simulation">
            <div class="p-4">
                <h3 class="font-bold">左侧抽屉</h3>
                <p>宽度: 360px</p>
                <p>Z-index: 10100</p>
            </div>
        </div>
        
        <!-- 测试按钮 -->
        <div class="ml-[380px] space-y-4">
            <button class="btn btn-primary" onclick="openModal('modal1')">测试1: left偏移</button>
            <button class="btn btn-secondary" onclick="openModal('modal2')">测试2: transform偏移</button>
            <button class="btn btn-accent" onclick="openModal('modal3')">测试3: margin偏移</button>
            <button class="btn btn-neutral" onclick="openModal('modal4')">测试4: 无偏移</button>
        </div>
    </div>

    <!-- 模态框1: left偏移 -->
    <div id="modal1" class="modal modal-with-drawer-offset-test1">
        <div class="modal-box">
            <h3 class="font-bold text-lg">测试1: left偏移</h3>
            <p class="py-4">使用 left: var(--drawer-offset) 的效果</p>
            <div class="modal-action">
                <button class="btn" onclick="closeModal('modal1')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 模态框2: transform偏移 -->
    <div id="modal2" class="modal modal-with-drawer-offset-test2">
        <div class="modal-box">
            <h3 class="font-bold text-lg">测试2: transform偏移</h3>
            <p class="py-4">使用 transform: translateX(var(--drawer-offset)) 的效果</p>
            <div class="modal-action">
                <button class="btn" onclick="closeModal('modal2')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 模态框3: margin偏移 -->
    <div id="modal3" class="modal modal-with-drawer-offset-test3">
        <div class="modal-box">
            <h3 class="font-bold text-lg">测试3: margin偏移</h3>
            <p class="py-4">使用 margin-left: var(--drawer-offset) 的效果</p>
            <div class="modal-action">
                <button class="btn" onclick="closeModal('modal3')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 模态框4: 无偏移 -->
    <div id="modal4" class="modal">
        <div class="modal-box">
            <h3 class="font-bold text-lg">测试4: 无偏移</h3>
            <p class="py-4">原始daisyUI模态框效果</p>
            <div class="modal-action">
                <button class="btn" onclick="closeModal('modal4')">关闭</button>
            </div>
        </div>
    </div>

    <script>
        function openModal(id) {
            document.getElementById(id).classList.add('modal-open');
        }
        
        function closeModal(id) {
            document.getElementById(id).classList.remove('modal-open');
        }
        
        // 点击背景关闭
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    modal.classList.remove('modal-open');
                }
            });
        });
    </script>
</body>
</html>
