// background.js

chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  if (message.type === 'fetchIconBase64' && message.iconUrl) {
    fetch(message.iconUrl)
      .then(res => res.blob())
      .then(blob => {
        const reader = new FileReader()
        reader.onloadend = () => {
          console.log('[background] 图标转 base64 成功')
          sendResponse({ icon: reader.result })
        }
        reader.readAsDataURL(blob)
      })
      .catch(err => {
        console.warn('[background] 图标下载失败:', err)
        sendResponse({ icon: '' })
      })

    return true
  }

  if (message.type === 'getBookmarks') {
    chrome.bookmarks.getTree((tree) => {
      sendResponse({ bookmarks: tree })
    })
    return true
  }
})
