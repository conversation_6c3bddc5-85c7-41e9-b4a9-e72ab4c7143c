<template>
  <div
    class="app-layout"
  >
    <!-- 多选工具栏 -->
    <SelectionToolbar
      :selected-count="selectedBookmarks.length"
      @clear-selection="clearSelection"
      @select-all="selectAll"
      @invert-selection="invertSelection"
      @move-selected="moveSelected"
      @clear-icons="clearSelectedIcons"
      @delete-selected="deleteSelected"
    />

    <!-- 搜索栏 - 移到main外部，fixed定位 -->
    <SearchBar
      ref="searchBarRef"
      class="search-container"
      :class="{
        'search-hidden': isSearchHidden,
        'search-visible': isSearchVisible,
        'search-with-drawer-offset': showLeftDrawer
      }"
      v-model="searchQuery"
      :search-results="filteredBookmarks"
      @blur="handleSearchBlur"
      @search-focus="handleSearchFocus"
    />

    <!-- 主内容区域 - 垂直居中 -->
    <main
      ref="mainContentRef"
      class="main-content-centered"
      :class="{ 'content-pushed': showLeftDrawer }"
    >



        <!-- 书签轮播系统 -->
        <div v-if="isLoading" class="loading-skeleton">
          <!-- 搜索栏骨架 -->
          <div class="skeleton-search-bar"></div>

          <!-- 文件夹骨架 -->
          <div class="skeleton-folders">
            <div v-for="i in 3" :key="i" class="skeleton-folder">
              <!-- 文件夹标题骨架 -->
              <div class="skeleton-folder-title"></div>

              <!-- 书签网格骨架 -->
              <div class="skeleton-bookmarks-grid">
                <div v-for="j in 6" :key="j" class="skeleton-bookmark"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 书签内容区 -->
        <BookmarkGrid
          v-if="!isLoading && activeGroupBookmarks.length > 0"
          :bookmarks="activeGroupBookmarks"
          :view-mode="viewMode"
          :card-style="bookmarkStyle"
        >
          <template #bookmark-item="{ bookmark }">
            <BookmarkCard
              :bookmark="bookmark"
              :view-mode="viewMode"
              :is-selected="selectedBookmarks.includes(bookmark.id)"
              :is-pending-delete="pendingDeleteBookmarks.includes(bookmark.id)"
              :show-tags="isTagsEnabled"
              :has-any-selected="selectedBookmarks.length > 0"
              :card-style="bookmarkStyle"
              @edit="editBookmark"
              @delete="deleteBookmark"
              @move="moveBookmark"
              @show-qrcode="showQRCode"
              @toggle-select="toggleBookmarkSelection"
              @shift-select="handleShiftSelect"
            />
          </template>
        </BookmarkGrid>

        <!-- 空状态 -->
        <div v-else-if="!isLoading && activeGroupId" class="empty-state">
          <div class="empty-icon">📚</div>
          <h3 class="empty-title">此文件夹暂无书签</h3>
          <p class="empty-description">
            {{ searchQuery ? '尝试调整搜索条件' : '开始添加一些书签吧' }}
          </p>
        </div>

        <!-- 未选择文件夹状态 -->
        <div v-else-if="!isLoading && !activeGroupId" class="empty-state">
          <div class="empty-icon">📁</div>
          <h3 class="empty-title">选择一个文件夹</h3>
          <p class="empty-description">点击上方标签页查看文件夹中的书签</p>
        </div>

        <!-- 垃圾桶区域 -->
        <TrashArea :isDragging="isDragging" @drop="deleteBookmarkById" />

    </main>

    <!-- 所有模态框移到main外部，避免受到transform影响 -->
    <!-- 增强编辑模态框 -->
    <EnhancedEditModal
      v-if="currentEditBookmark && showEnhancedEdit"
      :is-open="showEnhancedEdit"
      :bookmark="currentEditBookmark"
      :available-tags="availableTags"
      :tags-enabled="isTagsEnabled"
      @save="handleEnhancedSave"
      @cancel="handleEnhancedCancel"
    />

    <!-- 移动模态框 -->
    <MoveModal
      :is-open="showMoveModal"
      :groups="allGroups"
      :selected-count="moveTargetBookmarks.length"
      @move="handleMove"
      @cancel="showMoveModal = false"
    />

    <!-- 移动文件夹模态框 -->
    <MoveFolderModal
      :is-open="showMoveFolderModal"
      :groups="allGroups"
      :folder-id="moveFolderData?.folderId || ''"
      :folder-title="moveFolderData?.folderTitle || ''"
      @move="handleMoveFolder"
      @sort="handleSortFolders"
      @cancel="showMoveFolderModal = false"
    />

    <!-- 删除提示 -->
    <DeleteToast
      :is-visible="showDeleteToast"
      :message="deleteToastMessage"
      :duration="5"
      @undo="handleUndoDelete"
      @timeout="handleDeleteTimeout"
    />

    <!-- 确认模态框 -->
    <ConfirmModal
      :is-open="showConfirmModal"
      :type="confirmModalData.type"
      :title="confirmModalData.title"
      :message="confirmModalData.message"
      :confirm-text="confirmModalData.confirmText"
      :cancel-text="confirmModalData.cancelText"
      @confirm="handleConfirmModalConfirm"
      @cancel="handleConfirmModalCancel"
    />

    <!-- 二维码模态框 -->
    <QRCodeModal
      :is-open="showQRCodeModal"
      :bookmark="qrCodeBookmark"
      @close="showQRCodeModal = false"
    />

    <!-- 左下角按钮组 -->
    <div class="side-buttons-left">
      <SideButton
        :icon="TabsIcon"
        label="功能面板"
        title="书签文件夹、标签筛选、浏览器标签页"
        position="left"
        :is-active="showLeftDrawer"
        @click="toggleLeftDrawer"
      />
    </div>

    <!-- 右下角按钮组 -->
    <div class="side-buttons-right">
      <SideButton
        :key="`theme-${forceRerenderKey}`"
        :icon="ThemeIcon"
        label="主题"
        title="切换主题"
        position="right"
        @click="toggleThemeQuick"
      />
      <SideButton
        :icon="SettingsIcon"
        label="设置"
        title="设置"
        position="right"
        :is-active="showRightDrawer"
        @click="toggleSettings"
      />
    </div>

    <!-- 左侧抽屉面板 -->
    <LeftDrawer
      :is-open="showLeftDrawer"
      :groups="currentGroups"
      :bookmarks="filteredBookmarks"
      :active-group-id="activeGroupId"
      :available-tags="availableTags"
      :active-tags="activeTag"
      :filter-mode="tagFilterMode"
      @close="showLeftDrawer = false"
      @group-selected="handleGroupSelected"
      @toggle-tag="selectTag"
      @clear-tags="clearTag"
      @set-filter-mode="handleFilterModeChange"
      @save-bookmark="handleSaveTabAsBookmark"
    />

    <!-- 右侧抽屉面板 -->
    <RightDrawer
      :is-open="showRightDrawer"
      @close="showRightDrawer = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch, h } from 'vue'
import { useStorage } from '@vueuse/core'
import { useBookmarks } from './composables/useBookmarks'
import { useTagSettings } from './composables/useTagSettings'
import { useBookmarkDisplay } from './composables/useBookmarkDisplay'
import { usePinyinSearch } from './composables/usePinyinSearch'
import { useBookmarkDataManager } from './composables/useBookmarkDataManager'
import type { BookmarkWithMeta } from './types'

import SearchBar from './components/SearchBar.vue'
import BookmarkCard from './components/BookmarkCard.vue'
import TrashArea from './components/TrashArea.vue'


import SelectionToolbar from './components/SelectionToolbar.vue'
import EnhancedEditModal from './components/EnhancedEditModal.vue'
import MoveModal from './components/MoveModal.vue'
import MoveFolderModal from './components/MoveFolderModal.vue'
import DeleteToast from './components/DeleteToast.vue'
import ConfirmModal from './components/ConfirmModal.vue'
import QRCodeModal from './components/QRCodeModal.vue'

// 新增组件
import SideButton from './components/SideButton.vue'
import LeftDrawer from './components/LeftDrawer.vue'
import RightDrawer from './components/RightDrawer.vue'

import BookmarkGrid from './components/BookmarkGrid.vue'

// 图标组件定义
const TabsIcon = () => h('svg', {
  xmlns: 'http://www.w3.org/2000/svg',
  fill: 'none',
  viewBox: '0 0 24 24',
  strokeWidth: '1.5',
  stroke: 'currentColor'
}, [
  h('path', {
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    d: 'M8.25 6.75h12M8.25 12h12m-12 5.25h12M3.75 6.75h.007v.008H3.75V6.75zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zM3.75 12h.007v.008H3.75V12zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0zm-.375 5.25h.007v.008H3.75v-.008zm.375 0a.375.375 0 11-.75 0 .375.375 0 01.75 0z'
  })
])



// 动态主题图标组件 - 响应式
const ThemeIcon = () => {
  // 使用响应式的theme值
  const currentTheme = theme.value

  if (currentTheme === 'light') {
    // 浅色主题 - 太阳图标
    return h('svg', {
      xmlns: 'http://www.w3.org/2000/svg',
      fill: 'none',
      viewBox: '0 0 24 24',
      strokeWidth: '1.5',
      stroke: 'currentColor'
    }, [
      h('circle', {
        cx: '12',
        cy: '12',
        r: '4'
      }),
      h('path', {
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        d: 'M12 2v2m0 16v2M4.93 4.93l1.41 1.41m11.32 11.32l1.41 1.41M2 12h2m16 0h2M6.34 17.66l-1.41 1.41M19.07 4.93l-1.41 1.41'
      })
    ])
  } else if (currentTheme === 'dark') {
    // 深色主题 - 月亮图标
    return h('svg', {
      xmlns: 'http://www.w3.org/2000/svg',
      fill: 'none',
      viewBox: '0 0 24 24',
      strokeWidth: '1.5',
      stroke: 'currentColor'
    }, [
      h('path', {
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        d: 'M21.752 15.002A9.718 9.718 0 0118 15.75c-5.385 0-9.75-4.365-9.75-9.75 0-1.33.266-2.597.748-3.752A9.753 9.753 0 003 11.25C3 16.635 7.365 21 12.75 21a9.753 9.753 0 009.002-5.998z'
      })
    ])
  } else {
    // 自动主题 - 自动切换图标
    return h('svg', {
      xmlns: 'http://www.w3.org/2000/svg',
      fill: 'none',
      viewBox: '0 0 24 24',
      strokeWidth: '1.5',
      stroke: 'currentColor'
    }, [
      h('path', {
        strokeLinecap: 'round',
        strokeLinejoin: 'round',
        d: 'M16.023 9.348h4.992v-.001M2.985 19.644v-4.992m0 0h4.992m-4.993 0l3.181 3.183a8.25 8.25 0 0013.803-3.7M4.031 9.865a8.25 8.25 0 0113.803-3.7l3.181 3.182m0-4.991v4.99'
      })
    ])
  }
}

const SettingsIcon = () => h('svg', {
  xmlns: 'http://www.w3.org/2000/svg',
  fill: 'none',
  viewBox: '0 0 24 24',
  strokeWidth: '1.5',
  stroke: 'currentColor'
}, [
  h('path', {
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    d: 'M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z'
  }),
  h('path', {
    strokeLinecap: 'round',
    strokeLinejoin: 'round',
    d: 'M15 12a3 3 0 11-6 0 3 3 0 016 0z'
  })
])

// 响应式状态
const theme = useStorage('theme', 'light')

// 强制重新渲染的 key
const forceRerenderKey = ref(0)

// 防抖：避免快速连续拖拽时的性能问题
let rerenderTimeout: number | null = null

// 迁移旧的主题设置
if (theme.value === 'auto') {
  theme.value = 'auto-normal'
}
const folderLayout = useStorage<'single' | 'double'>('folderLayout', 'single')
const autoCollapseFolders = useStorage('autoCollapseFolders', true)
const searchQuery = ref('')
const activeTag = ref<string[]>([]) // 改为数组，支持多选
const tagFilterMode = ref<'AND' | 'OR'>('OR') // 筛选模式


const isDragging = ref(false)
const selectedBookmarks = ref<string[]>([])

// 加载状态
const isLoading = ref(true)

// 新增状态
const showEnhancedEdit = ref(false)
const showMoveModal = ref(false)
const showDeleteToast = ref(false)
const deleteToastMessage = ref('')
const pendingDeleteBookmarks = ref<string[]>([])
const moveTargetBookmarks = ref<string[]>([])
const currentEditBookmark = ref<BookmarkWithMeta | null>(null)

// 智能刷新相关状态
const isInternalOperation = ref(false) // 标识是否为内部操作
const groupedBookmarksRef = ref<any>(null) // GroupedBookmarks 组件引用

// 智能刷新辅助函数
const smartRefresh = async (options: {
  type: 'internal-sort' | 'internal-move' | 'external-change'
  preserveState?: boolean
  delay?: number
} = { type: 'external-change' }) => {
  console.log('🧠 智能刷新:', options.type)

  try {
    // 如果需要保持状态，先保存展开状态
    if (options.preserveState && groupedBookmarksRef.value) {
      groupedBookmarksRef.value.saveExpandedState()
    }

    // 根据操作类型决定刷新策略
    switch (options.type) {
      case 'internal-sort':
        // 同列表内排序：只重新加载数据，不强制重新渲染
        console.log('🔄 内部排序：仅重新加载数据')
        await loadBookmarks()
        break

      case 'internal-move':
        // 跨列表移动：延迟刷新并恢复状态
        console.log('🔄 内部移动：延迟刷新')
        if (options.delay) {
          await new Promise(resolve => setTimeout(resolve, options.delay))
        }
        await loadBookmarks()
        // 只在必要时强制重新渲染（如跨文件夹移动）
        if (options.preserveState) {
          console.log('🔄 内部移动：保持状态，无需强制重新渲染')
        } else {
          forceRerenderKey.value++
        }
        break

      case 'external-change':
        // 外部变化：根据是否需要保持状态决定是否强制重新渲染
        console.log('🔄 外部变化：智能刷新')
        await loadBookmarks()
        if (options.preserveState) {
          console.log('🔄 外部变化：保持状态，无需强制重新渲染')
        } else {
          forceRerenderKey.value++
        }
        break
    }

    // 如果保存了状态，在下一个 tick 恢复
    if (options.preserveState && groupedBookmarksRef.value) {
      await nextTick()
      groupedBookmarksRef.value.restoreExpandedState()
    }

  } catch (error) {
    console.error('❌ 智能刷新失败:', error)
  }
}

// 二维码模态框状态
const showQRCodeModal = ref(false)
const qrCodeBookmark = ref<BookmarkWithMeta | null>(null)

// 移动文件夹模态框状态
const showMoveFolderModal = ref(false)
const moveFolderData = ref<{ folderId: string; folderTitle: string; parentId?: string } | null>(null)





// 确认模态框状态
const showConfirmModal = ref(false)
const confirmModalData = ref({
  type: 'info' as 'info' | 'warning' | 'danger',
  title: '',
  message: '',
  confirmText: '确定',
  cancelText: '取消',
  onConfirm: () => {}
})

// 使用书签数据
const {
  bookmarks,
  groups,
  allFolders,
  enrichedBookmarks,

  loadBookmarks,
  saveBookmarkMeta,
  saveBookmarkData,
} = useBookmarks()

// 使用双数据层管理器
const {
  uiBookmarks,
  uiGroups,
  loadFromChrome,
  moveBookmarkDualLayer,
  deleteBookmarkDualLayer,
  updateBookmarkDualLayer,
  createBookmarkDualLayer
} = useBookmarkDataManager()

// 使用标签设置
const { isTagsEnabled } = useTagSettings()

// 使用书签显示设置
const { bookmarkStyle } = useBookmarkDisplay()

// 使用拼音搜索功能
const { searchBookmarks } = usePinyinSearch()

// 双数据层模式开关
const useDualLayer = ref(true) // 启用双数据层模式

// 将bookmarkStyle映射到viewMode
const viewMode = computed<'icon' | 'card'>(() => {
  return 'icon' // 现在只使用icon模式，通过cardStyle控制布局
})

// 计算当前数据源的可用标签
const availableTags = computed(() => {
  // 根据模式选择数据源
  const sourceBookmarks = useDualLayer.value ? uiBookmarks.value : enrichedBookmarks.value

  const tagSet = new Set<string>()
  sourceBookmarks.forEach((b) => {
    // 确保 tags 是数组
    const tags = Array.isArray(b.tags) ? b.tags : []
    tags.forEach((tag) => {
      if (typeof tag === 'string' && tag.trim()) {
        tagSet.add(tag.trim())
      }
    })
  })

  return [...tagSet]
})

// 计算过滤后的书签
const filteredBookmarks = computed(() => {
  // 根据模式选择数据源
  const sourceBookmarks = useDualLayer.value ? uiBookmarks.value : enrichedBookmarks.value
  let filtered = sourceBookmarks

  // 搜索过滤（使用拼音搜索）
  if (searchQuery.value.trim()) {
    filtered = searchBookmarks(filtered, searchQuery.value, {
      enableInitials: true,
      enableFullPinyin: true,
      enableFuzzy: true,
      enableEnglish: true
    })
  }

  // 按标签过滤（仅在标签功能开启且有选中标签时）
  if (isTagsEnabled.value && activeTag.value.length > 0) {
    if (tagFilterMode.value === 'AND') {
      // AND模式：书签必须包含所有选中的标签
      filtered = filtered.filter((b) =>
        activeTag.value.every(tag => b.tags?.includes(tag))
      )
    } else {
      // OR模式：书签包含任一选中标签即可
      filtered = filtered.filter((b) =>
        activeTag.value.some(tag => b.tags?.includes(tag))
      )
    }
  }

  return filtered
})

// 计算当前使用的分组数据
const currentGroups = computed(() => {
  return useDualLayer.value ? uiGroups.value : groups.value
})



// 计算所有文件夹（包括空文件夹），用于移动书签时的选择
const allGroups = computed(() => {
  return allFolders.value // 这里返回所有文件夹，包括空文件夹
})



// 应用主题的核心逻辑
const applyTheme = (theme: string) => {
  const html = document.documentElement
  html.classList.remove('dark', 'light')

  if (theme === 'auto') {
    // 自动模式：跟随系统主题
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    html.setAttribute('data-theme', prefersDark ? 'dark' : 'light')
    if (prefersDark) html.classList.add('dark')
  } else {
    // 固定主题模式
    html.setAttribute('data-theme', theme)
    if (theme === 'dark') {
      html.classList.add('dark')
    }
  }
}

// 视图模式现在由useBookmarkDisplay管理，不需要单独的切换函数

// 标签相关函数
const selectTag = (tag: string) => {
  console.log('App.vue selectTag 被调用:', {
    tag,
    当前选中: activeTag.value
  })

  // 直接多选模式：点击切换选中状态
  const index = activeTag.value.indexOf(tag)
  if (index > -1) {
    // 取消选择
    activeTag.value = activeTag.value.filter(t => t !== tag)
    console.log('取消选择', tag, '剩余:', activeTag.value)
  } else {
    // 添加选择
    activeTag.value = [...activeTag.value, tag]
    console.log('添加选择', tag, '当前:', activeTag.value)
  }

  console.log('最终选中的标签:', activeTag.value)
}

const clearTag = () => {
  activeTag.value = []
}

// 标签颜色系统
const tagColors = [
  '#3b82f6', // 蓝色
  '#10b981', // 绿色
  '#f59e0b', // 橙色
  '#ef4444', // 红色
  '#8b5cf6', // 紫色
  '#06b6d4', // 青色
  '#84cc16', // 柠檬绿
  '#f97316', // 橙红色
  '#6366f1', // 靛蓝
  '#ec4899', // 粉色
  '#14b8a6', // 蓝绿色
  '#eab308'  // 黄色
]

const getTagColor = (tag: string) => {
  // 使用标签名的哈希值来确定颜色，确保同一标签总是相同颜色
  let hash = 0
  for (let i = 0; i < tag.length; i++) {
    hash = tag.charCodeAt(i) + ((hash << 5) - hash)
  }
  const colorIndex = Math.abs(hash) % tagColors.length
  const baseColor = tagColors[colorIndex]

  // 返回基础颜色，不添加透明度
  return baseColor
}

// 处理筛选模式切换
const handleFilterModeChange = (mode: 'AND' | 'OR') => {
  tagFilterMode.value = mode
  console.log('筛选模式切换为:', mode)
}

// 组件引用
const searchBarRef = ref<InstanceType<typeof SearchBar> | null>(null)
const mainContentRef = ref<HTMLElement | null>(null)

// 搜索框滚动隐藏状态
const isSearchHidden = ref(false) // 初始状态为显示
const isSearchVisible = ref(false) // 搜索框是否以fixed方式显示
const lastScrollY = ref(0)

// 搜索框聚焦状态
const isSearchFocused = ref(false)
const isEditingBookmark = ref(false)
const showRightDrawer = ref(false)

// 左侧抽屉状态
const showLeftDrawer = ref(false)

// 文件夹标签页状态
const activeGroupId = ref('')



// 右侧抽屉控制方法
const toggleRightDrawer = () => {
  showRightDrawer.value = !showRightDrawer.value
}

// 左侧抽屉控制方法
const toggleLeftDrawer = () => {
  showLeftDrawer.value = !showLeftDrawer.value

  // 将CSS变量设置到body上，这样Teleport的元素也能访问到
  const drawerOffset = showLeftDrawer.value ? '90px' : '0px'
  const drawerOffsetMobile = showLeftDrawer.value ? '60px' : '0px'

  document.body.style.setProperty('--drawer-offset', drawerOffset)
  document.body.style.setProperty('--drawer-offset-mobile', drawerOffsetMobile)

  console.log('📍 左侧抽屉状态变化:', {
    showLeftDrawer: showLeftDrawer.value,
    isSearchHidden: isSearchHidden.value,
    isSearchVisible: isSearchVisible.value,
    drawerOffset,
    drawerOffsetMobile
  })

  // 调试CSS变量
  setTimeout(() => {
    const computedStyle = getComputedStyle(document.body)
    console.log('📍 CSS变量值:', {
      drawerOffset: computedStyle.getPropertyValue('--drawer-offset'),
      drawerOffsetMobile: computedStyle.getPropertyValue('--drawer-offset-mobile')
    })
  }, 100)
}

const toggleThemeQuick = () => {
  // 快速主题切换：light -> dark -> auto -> light
  const currentTheme = localStorage.getItem('theme') || 'light'
  let nextTheme = 'light'

  if (currentTheme === 'light') {
    nextTheme = 'dark'
  } else if (currentTheme === 'dark') {
    nextTheme = 'auto'
  } else {
    nextTheme = 'light'
  }

  localStorage.setItem('theme', nextTheme)
  theme.value = nextTheme
  applyTheme(nextTheme)

  // 强制重新渲染以更新主题图标
  forceRerenderKey.value++
}

const toggleSettings = () => {
  toggleRightDrawer()
}

// 文件夹标签页相关方法
const handleGroupSelected = (groupId: string) => {
  activeGroupId.value = groupId
  console.log('选择文件夹:', groupId)
}

// 监听文件夹变化，自动选择第一个文件夹
watch(currentGroups, (newGroups) => {
  if (newGroups.length > 0 && !activeGroupId.value) {
    activeGroupId.value = newGroups[0].id
    console.log('自动选择第一个文件夹:', newGroups[0].title)
  }
}, { immediate: true })

// 监听标签选择变化（轮播功能已移除）
watch(activeTag, () => {
  console.log('标签筛选变化:', activeTag.value)
}, { deep: true })

// 计算当前激活文件夹的书签
const activeGroupBookmarks = computed(() => {
  // 如果选择了"显示所有书签"
  if (activeGroupId.value === 'all') {
    return filteredBookmarks.value
  }
  // 文件夹模式：显示当前文件夹的书签
  if (!activeGroupId.value) return []
  return filteredBookmarks.value.filter(bookmark => bookmark.parentId === activeGroupId.value)
})



// 处理标签页保存为书签
const handleSaveTabAsBookmark = async (tab: any) => {
  try {
    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // Chrome 环境：使用 Chrome API 创建书签
      const bookmark = await chrome.bookmarks.create({
        title: tab.title,
        url: tab.url,
        parentId: '1' // 默认保存到书签栏
      })
      console.log('标签页已保存为书签:', bookmark)
    } else {
      // 开发环境：模拟保存
      console.log('开发环境：模拟保存标签页为书签:', tab)
    }

    // 重新加载书签数据
    await loadBookmarks()

    // 关闭左侧抽屉面板
    showLeftDrawer.value = false
  } catch (error) {
    console.error('保存标签页为书签失败:', error)
  }
}

// 搜索相关方法
const handleSearchFocus = () => {
  isSearchFocused.value = true
}

const handleSearchBlur = () => {
  isSearchFocused.value = false
  // 移除强制重新聚焦逻辑，只在失焦时更新状态
}

// 滚动处理函数
const handleScroll = () => {
  if (!mainContentRef.value) return

  const currentScrollY = mainContentRef.value.scrollTop
  const scrollDelta = currentScrollY - lastScrollY.value

  console.log('📍 主内容区滚动状态:', {
    currentScrollY,
    scrollDelta,
    isSearchHidden: isSearchHidden.value,
    scrollHeight: mainContentRef.value.scrollHeight,
    clientHeight: mainContentRef.value.clientHeight
  })

  // 搜索框滚动逻辑：向下滚动隐藏，向上滚动显示
  if (Math.abs(scrollDelta) > 10) {
    if (scrollDelta > 0) {
      // 向下滚动，隐藏搜索框
      if (!isSearchHidden.value) {
        console.log('📍 向下滚动，隐藏搜索框')
        isSearchHidden.value = true
        isSearchVisible.value = false
      }
    } else {
      // 向上滚动，显示搜索框
      if (isSearchHidden.value) {
        console.log('📍 向上滚动，显示搜索框')
        isSearchHidden.value = false
        isSearchVisible.value = true
      }
    }
  }

  // 滚动到顶部时，使用正常位置
  if (currentScrollY <= 100) {
    if (isSearchVisible.value || isSearchHidden.value) {
      console.log('📍 滚动到顶部，使用正常位置')
      isSearchVisible.value = false
      isSearchHidden.value = false
    }
  }

  lastScrollY.value = currentScrollY
}

// 书签操作函数
const editBookmark = (bookmark: BookmarkWithMeta) => {
  // 检查标签数据是否有问题
  if (bookmark.tags && !Array.isArray(bookmark.tags)) {
    bookmark.tags = []
  }

  // 创建书签的深度副本，确保数据完整
  currentEditBookmark.value = {
    ...bookmark,
    tags: bookmark.tags ? [...bookmark.tags] : []
  }

  // 设置编辑状态
  isEditingBookmark.value = true
  // 确保模态框状态正确设置
  showEnhancedEdit.value = true
}





// 简化的索引处理：直接使用传入的真实索引
const processRealIndex = (sourceIndex: number, targetIndex: number): number => {
  console.log('📍 处理真实索引:', {
    sourceIndex,
    targetIndex,
    移动方向: sourceIndex < targetIndex ? '向后' : '向前'
  })

  // 对于同列表排序，Chrome API的特殊行为：
  // 向后移动时，由于先移除再插入，目标索引需要+1
  if (sourceIndex < targetIndex) {
    const finalIndex = targetIndex + 1
    console.log('📍 向后移动，索引+1:', finalIndex)
    return finalIndex
  } else {
    console.log('📍 向前移动，直接使用目标索引:', targetIndex)
    return targetIndex
  }
}

// 处理书签重新排序
const handleReorder = async (data: {
  draggedBookmarkId: string
  targetBookmarkId: string
  sourceParentId: string
  targetParentId: string
  sourceIndex: number
  targetIndex: number
  dropPosition?: 'before' | 'after'
}) => {
  console.log('🎯 App.vue - handleReorder 开始处理')
  console.log('📍 接收到的数据:', data)

  // 检查是否使用双数据层模式
  if (useDualLayer.value) {
    console.log('🚀 使用双数据层模式处理拖拽 (原生索引)')

    try {
      const success = await moveBookmarkDualLayer(
        data.draggedBookmarkId,
        data.sourceParentId,
        data.targetParentId,
        data.sourceIndex,
        data.targetIndex
      )

      if (success) {
        console.log('✅ 双数据层拖拽完成，数据已同步')
        // 清理多选状态
        if (selectedBookmarks.value.includes(data.draggedBookmarkId)) {
          console.log('🧹 清理移动书签的选择状态')
          clearSelection()
        }
      } else {
        console.error('❌ 双数据层拖拽失败')
      }
    } catch (error) {
      console.error('❌ 双数据层拖拽出错:', error)
    }

    return
  }

  // 传统模式处理（保持原有逻辑）
  // 设置内部操作标识，避免监听器重复处理
  isInternalOperation.value = true

  try {
    // 环境检测
    const hasChrome = typeof chrome !== 'undefined'
    const hasBookmarks = hasChrome && chrome.bookmarks
    console.log('🔍 环境检测:', {
      hasChrome,
      hasBookmarks,
      环境: hasBookmarks ? 'Chrome扩展环境' : '开发环境'
    })

    if (hasBookmarks) {
      // 找到被拖拽的书签
      const draggedBookmark = enrichedBookmarks.value.find(b => b.id === data.draggedBookmarkId)
      if (!draggedBookmark) {
        console.error('找不到被拖拽的书签:', data.draggedBookmarkId)
        return
      }

      console.log('📍 拖拽详情:', {
        draggedBookmark: draggedBookmark.title,
        draggedId: data.draggedBookmarkId,
        sourceParent: data.sourceParentId,
        targetParent: data.targetParentId,
        sourceIndex: data.sourceIndex,
        targetIndex: data.targetIndex
      })

      // 检查是否是跨列表移动
      if (data.sourceParentId !== data.targetParentId) {
        console.log('🔄 跨列表移动')

        const moveParams = {
          parentId: data.targetParentId || undefined,
          index: data.targetIndex  // 直接使用传入的真实索引
        }
        console.log('📤 Chrome API 移动参数:', moveParams)

        try {
          const result = await chrome.bookmarks.move(data.draggedBookmarkId, moveParams)
          console.log('✅ Chrome API 跨列表移动成功，返回结果:', result)
          console.log('📍 移动后的书签信息:', {
            id: result.id,
            title: result.title,
            index: result.index,
            parentId: result.parentId
          })
        } catch (error) {
          console.error('❌ Chrome API 跨列表移动失败:', error)
          throw error
        }

        console.log('✅ 跨列表移动完成')
      } else {
        console.log('🔄 同列表内排序')

        // 直接使用传入的真实索引进行处理
        const finalIndex = processRealIndex(data.sourceIndex, data.targetIndex)

        const moveParams = {
          index: finalIndex
        }

        console.log('📍 同列表排序索引计算:', {
          sourceIndex: data.sourceIndex,
          targetIndex: data.targetIndex,
          移动方向: data.sourceIndex < data.targetIndex ? '向后' : '向前',
          最终Chrome索引: finalIndex
        })

        console.log('📤 Chrome API 移动参数:', moveParams)

        try {
          const result = await chrome.bookmarks.move(data.draggedBookmarkId, moveParams)
          console.log('✅ Chrome API 移动成功，返回结果:', result)
          console.log('📍 移动后的书签信息:', {
            id: result.id,
            title: result.title,
            index: result.index,
            parentId: result.parentId
          })
        } catch (error) {
          console.error('❌ Chrome API 移动失败:', error)
          throw error
        }

        console.log('✅ 同列表排序完成')
      }

      // 检查是否是跨列表移动
      const isCrossListMove = data.sourceParentId !== data.targetParentId

      if (isCrossListMove) {
        // 跨列表移动：需要重新加载数据并强制重新渲染
        console.log('🔄 跨列表移动：重新加载数据...')
        await loadBookmarks()
        console.log('✅ 跨列表移动：数据重新加载完成')

        // 使用防抖机制优化重新渲染性能
        if (rerenderTimeout) {
          clearTimeout(rerenderTimeout)
        }

        rerenderTimeout = window.setTimeout(async () => {
          console.log('🔄 跨列表移动：更新列表数据')

          // 更新列表数据以反映移动后的状态，使用智能刷新保持展开状态
          await smartRefresh({
            type: 'internal-move',
            preserveState: true
          })
          console.log('✅ 跨列表移动：列表数据已更新')

          // 清理多选状态
          if (selectedBookmarks.value.includes(data.draggedBookmarkId)) {
            console.log('🧹 清理移动书签的选择状态')
            clearSelection()
          }

          rerenderTimeout = null
        }, 100) // 100ms 防抖延迟
      } else {
        // 同列表内移动：更新列表数据以反映新的排序
        console.log('🔄 同列表内排序：更新列表数据')

        // 延迟更新，确保 Chrome API 操作完成，使用智能刷新保持展开状态
        setTimeout(async () => {
          await smartRefresh({
            type: 'internal-sort',
            preserveState: true
          })
          console.log('✅ 同列表内排序：列表数据已更新')
        }, 100)

        // 清理多选状态
        if (selectedBookmarks.value.includes(data.draggedBookmarkId)) {
          console.log('🧹 清理移动书签的选择状态')
          clearSelection()
        }
      }
    } else {
      // 开发环境处理
      console.log('🛠️ 开发环境处理拖拽')

      // 在开发环境中，我们需要手动更新书签的 parentId
      const bookmarkIndex = enrichedBookmarks.value.findIndex(b => b.id === data.draggedBookmarkId)
      if (bookmarkIndex !== -1) {
        const bookmark = enrichedBookmarks.value[bookmarkIndex]
        console.log('📍 找到书签:', bookmark.title)

        // 如果是跨列表移动，更新 parentId
        if (data.sourceParentId !== data.targetParentId) {
          console.log('🔄 开发环境跨列表移动')
          const updateData = {
            ...bookmark,
            parentId: data.targetParentId || undefined
          }
          console.log('📤 更新数据:', updateData)
          saveBookmarkData(data.draggedBookmarkId, updateData)
        }

        // 重新加载书签数据
        console.log('🔄 重新加载书签数据...')
        await loadBookmarks()
        console.log('✅ 开发环境处理完成')
      } else {
        console.error('❌ 在开发环境中找不到书签:', data.draggedBookmarkId)
      }
    }
  } catch (error) {
    console.error('重新排序书签时出错:', error)
  } finally {
    // 重置内部操作标识
    setTimeout(() => {
      isInternalOperation.value = false
      console.log('🔄 重置内部操作标识')
    }, 200) // 延迟重置，确保所有相关的 Chrome API 调用完成
  }
}

const deleteBookmark = (bookmark: BookmarkWithMeta) => {
  pendingDeleteBookmarks.value = [bookmark.id]
  deleteToastMessage.value = `删除书签 "${bookmark.title}"`
  showDeleteToast.value = true
}

const deleteBookmarkById = (bookmarkId: string) => {
  const bookmark = enrichedBookmarks.value.find(b => b.id === bookmarkId)
  if (bookmark) {
    deleteBookmark(bookmark)
  }
}

const moveBookmark = (bookmark: BookmarkWithMeta) => {
  moveTargetBookmarks.value = [bookmark.id]
  showMoveModal.value = true
}

const showQRCode = (bookmark: BookmarkWithMeta) => {
  qrCodeBookmark.value = bookmark
  showQRCodeModal.value = true
}





// 监听设置面板的保存事件
const handleSettingsChanged = async (event: CustomEvent) => {
  console.log('设置已保存，使用智能刷新:', event.detail)

  // 更新本地状态
  folderLayout.value = event.detail.folderLayout
  autoCollapseFolders.value = event.detail.autoCollapseFolders

  // 检查是否有需要重新加载数据的设置变更
  const needsDataRefresh = event.detail.tagsEnabled !== undefined

  if (needsDataRefresh) {
    // 标签功能开关变更需要重新处理书签数据
    console.log('🔄 标签功能变更，使用智能刷新重新加载数据')
    await smartRefresh({
      type: 'external-change',
      preserveState: true
    })
  } else {
    // 其他设置变更只需要响应式更新，无需重新渲染
    console.log('✅ 设置变更完成，无需刷新页面')
  }
}

// 监听标签页拖拽创建书签事件
const handleBookmarkCreated = async () => {
  console.log('📍 标签页拖拽创建书签，使用智能刷新更新数据')

  // 使用智能刷新保持展开状态
  await smartRefresh({
    type: 'external-change',
    preserveState: true
  })

  console.log('✅ 标签页创建书签：数据已更新，状态已保持')
}



const deleteGroup = async (groupId: string) => {
  try {
    console.log('🗑️ 准备删除文件夹:', groupId)

    // 获取文件夹中当前筛选显示的书签（考虑搜索和标签筛选）
    const filteredFolderBookmarks = filteredBookmarks.value.filter(b => b.parentId === groupId)
    console.log('📍 筛选后文件夹中的书签数量:', filteredFolderBookmarks.length)

    if (filteredFolderBookmarks.length === 0) {
      console.log('📍 文件夹中没有书签，无需删除')
      return
    }

    // 设置待删除的书签ID列表（用于撤销功能）
    pendingDeleteBookmarks.value = filteredFolderBookmarks.map(b => b.id)
    deleteToastMessage.value = `删除文件夹中的 ${filteredFolderBookmarks.length} 个书签`
    showDeleteToast.value = true

    console.log('⏰ 开始5秒倒计时，可以撤销删除')

  } catch (error) {
    console.error('准备删除文件夹时出错:', error)
    alert('删除操作失败，请重试。')
  }
}

// Shift多选逻辑
const lastSelectedIndex = ref(-1)
const shiftBaseIndex = ref(-1) // Shift多选的基点索引

// 防抖处理，避免快速连续的选择操作
let selectionTimeout: number | null = null

const toggleBookmarkSelection = (bookmarkId: string) => {
  // 防抖处理：如果在短时间内重复操作同一个书签，忽略后续操作
  if (selectionTimeout) {
    return
  }

  selectionTimeout = window.setTimeout(() => {
    selectionTimeout = null
  }, 100) // 100ms 防抖

  console.log('🔄 切换书签选择状态:', bookmarkId)

  // 创建新的选择数组以确保响应式更新
  const newSelection = [...selectedBookmarks.value]
  const index = newSelection.indexOf(bookmarkId)

  if (index > -1) {
    newSelection.splice(index, 1)
    console.log('➖ 取消选择书签:', bookmarkId)
  } else {
    newSelection.push(bookmarkId)
    console.log('➕ 选择书签:', bookmarkId)
  }

  // 原子性更新选择状态
  selectedBookmarks.value = newSelection

  // 记录最后选择的索引
  const currentIndex = filteredBookmarks.value.findIndex(b => b.id === bookmarkId)
  lastSelectedIndex.value = currentIndex

  // 如果不是Shift多选，重置基点
  shiftBaseIndex.value = currentIndex

  console.log('✅ 选择状态更新完成，当前选择数量:', selectedBookmarks.value.length)
}

const handleShiftSelect = (bookmarkId: string) => {
  const allBookmarks = filteredBookmarks.value
  const currentIndex = allBookmarks.findIndex(b => b.id === bookmarkId)

  // 如果没有基点，设置当前点击的书签为基点
  if (shiftBaseIndex.value === -1) {
    shiftBaseIndex.value = currentIndex
    toggleBookmarkSelection(bookmarkId)
    return
  }

  // 清除所有选择
  selectedBookmarks.value = []

  // 计算选择范围（从基点到当前点击位置）
  const startIndex = Math.min(shiftBaseIndex.value, currentIndex)
  const endIndex = Math.max(shiftBaseIndex.value, currentIndex)

  // 选择范围内的所有书签
  for (let i = startIndex; i <= endIndex; i++) {
    const bookmark = allBookmarks[i]
    if (bookmark) {
      selectedBookmarks.value.push(bookmark.id)
    }
  }

  lastSelectedIndex.value = currentIndex
}

// 多选工具栏函数
const clearSelection = () => {
  selectedBookmarks.value = []
  shiftBaseIndex.value = -1
  lastSelectedIndex.value = -1
}

const selectAll = () => {
  selectedBookmarks.value = filteredBookmarks.value.map(b => b.id)
}

const invertSelection = () => {
  const allIds = filteredBookmarks.value.map(b => b.id)
  const currentSelected = selectedBookmarks.value
  selectedBookmarks.value = allIds.filter(id => !currentSelected.includes(id))
}

const moveSelected = () => {
  moveTargetBookmarks.value = [...selectedBookmarks.value]
  showMoveModal.value = true
}

// 清理图标缓存的辅助函数
const clearIconCache = async (bookmarkId: string) => {
  console.log('🗑️ 清理图标缓存:', bookmarkId)

  if (typeof chrome !== 'undefined' && chrome.storage) {
    // Chrome 环境：清理 chrome.storage.local
    await new Promise<void>((resolve) => {
      chrome.storage.local.remove(`icon_${bookmarkId}`, () => {
        console.log('✅ Chrome storage 图标缓存已清理:', bookmarkId)
        resolve()
      })
    })
  } else {
    // 开发环境：清理 localStorage
    localStorage.removeItem(`icon_${bookmarkId}`)
    console.log('✅ localStorage 图标缓存已清理:', bookmarkId)
  }
}

const clearSelectedIcons = async () => {
  try {
    console.log('🧹 开始清理选中书签的自定义图标，数量:', selectedBookmarks.value.length)

    // 使用 Promise.all 并行处理所有清理操作
    await Promise.all(
      selectedBookmarks.value.map(async (bookmarkId) => {
        console.log('🗑️ 清理书签图标和缓存:', bookmarkId)

        // 1. 清理元数据中的图标
        await saveBookmarkMeta(bookmarkId, { icon: undefined })

        // 2. 清理图标缓存
        await clearIconCache(bookmarkId)

        return Promise.resolve()
      })
    )

    console.log('✅ 所有图标和缓存清理完成')

    // 清理选择状态
    clearSelection()
    console.log('✅ 清理选择状态完成，无需刷新页面')

  } catch (error) {
    console.error('清除图标时出错:', error)
  }
}

const deleteSelected = () => {
  pendingDeleteBookmarks.value = [...selectedBookmarks.value]
  deleteToastMessage.value = `删除 ${selectedBookmarks.value.length} 个书签`
  showDeleteToast.value = true
  clearSelection()
}



// 取消编辑
const handleEnhancedCancel = () => {
  showEnhancedEdit.value = false
  currentEditBookmark.value = null
  isEditingBookmark.value = false
}

// 增强编辑保存
const handleEnhancedSave = async (bookmark: BookmarkWithMeta) => {
  try {
    // 构建完整的标题（仅在标签功能开启时包含标签）
    const separator = localStorage.getItem('tagSeparator') || '#'
    const title = bookmark.title || ''
    const tags = isTagsEnabled.value ? (bookmark.tags || []) : []
    const fullTitle = tags.length > 0 ? `${title}${separator}${tags.join(separator)}` : title

    console.log('📝 构建完整标题:', {
      原始标题: title,
      标签: tags,
      完整标题: fullTitle
    })

    // 检查是否使用双数据层模式
    if (useDualLayer.value) {
      console.log('🚀 使用双数据层模式保存书签')

      if (!bookmark.id || bookmark.id === '') {
        // 新增书签
        console.log('双数据层新增书签')
        const newBookmark = await createBookmarkDualLayer({
          title: fullTitle,
          url: bookmark.url || 'https://example.com',
          parentId: bookmark.parentId || '1',
          index: bookmark.index
        })

        if (newBookmark) {
          console.log('✅ 双数据层新增书签完成')
          // 保存图标到元数据
          if (bookmark.icon) {
            saveBookmarkMeta(newBookmark.id, { icon: bookmark.icon })
          }
        } else {
          console.error('❌ 双数据层新增书签失败')
        }
      } else {
        // 编辑现有书签
        console.log('双数据层编辑书签，ID:', bookmark.id)
        const success = await updateBookmarkDualLayer(bookmark.id, {
          title: fullTitle,
          url: bookmark.url
        })

        if (success) {
          console.log('✅ 双数据层编辑书签完成')
          // 保存图标到元数据
          saveBookmarkMeta(bookmark.id, { icon: bookmark.icon })
        } else {
          console.error('❌ 双数据层编辑书签失败')
        }
      }
    } else {
      // 传统模式处理
      // 设置内部操作标识，避免监听器重复处理
      isInternalOperation.value = true

      if (!bookmark.id || bookmark.id === '') {
        // 新增书签
        console.log('执行新增书签逻辑')
        if (typeof chrome !== 'undefined' && chrome.bookmarks) {
          const newBookmark = await chrome.bookmarks.create({
            parentId: bookmark.parentId || '1', // 默认放在书签栏
            title: fullTitle,
            url: bookmark.url || 'https://example.com'
          })

          // 只保存图标到元数据（标签已保存在标题中）
          if (newBookmark.id && bookmark.icon) {
            saveBookmarkMeta(newBookmark.id, {
              icon: bookmark.icon
            })
          }

          // 重新加载数据以获取正确的书签顺序和完整信息，使用智能刷新保持展开状态
          setTimeout(async () => {
            await smartRefresh({
              type: 'external-change',
              preserveState: true
            })
            console.log('✅ 新增书签：列表数据已更新')
          }, 100)
        } else {
          // 开发环境：生成新的书签ID并保存完整数据
          const newId = 'bm_' + Date.now() + '_' + Math.random().toString(36).substring(2, 11)
          console.log('开发环境新增书签，生成ID:', newId)

          // 保存完整的书签数据
          const newBookmarkData = {
            ...bookmark,
            id: newId,
            title: fullTitle, // 使用包含标签的完整标题
            parentId: bookmark.parentId || '1'
          }
          saveBookmarkData(newId, newBookmarkData)

          console.log('开发环境新增书签完成')
        }
      } else {
        // 编辑现有书签
        console.log('执行编辑书签逻辑，ID:', bookmark.id)
        console.log('准备保存的完整数据:', bookmark)

        // 如果是Chrome环境，更新书签标题和URL
        if (typeof chrome !== 'undefined' && chrome.bookmarks) {
          await chrome.bookmarks.update(bookmark.id, {
            title: fullTitle, // 使用包含标签的完整标题
            url: bookmark.url
          })

          // 只保存图标到元数据（标签已保存在标题中）
          saveBookmarkMeta(bookmark.id, {
            icon: bookmark.icon
          })

          // 重新加载数据以获取最新的书签信息，使用智能刷新保持展开状态
          setTimeout(async () => {
            await smartRefresh({
              type: 'external-change',
              preserveState: true
            })
            console.log('✅ 编辑书签：列表数据已更新')
          }, 100)
        } else {
          // 开发环境：保存完整的书签数据
          console.log('开发环境：保存完整书签数据')
          const dataToSave = {
            ...bookmark,
            title: fullTitle // 使用包含标签的完整标题
          }
          saveBookmarkData(bookmark.id, dataToSave)
        }
      }

      // 重置内部操作标识
      setTimeout(() => {
        isInternalOperation.value = false
        console.log('🔄 重置编辑操作的内部标识')
      }, 200)
    }

    // 关闭模态框
    console.log('关闭模态框...')
    showEnhancedEdit.value = false
    currentEditBookmark.value = null
    isEditingBookmark.value = false

    console.log('✅ 编辑完成')

  } catch (error) {
    console.error('保存书签时出错:', error)
    console.error('错误详情:', error)

    // 即使出错也要关闭模态框
    showEnhancedEdit.value = false
    currentEditBookmark.value = null
    isEditingBookmark.value = false
  }
}

// 移动处理
const handleMove = async (folderId: string) => {
  // 设置内部操作标识，避免监听器重复处理
  isInternalOperation.value = true

  try {
    console.log('📍 批量移动书签到文件夹:', folderId, '书签数量:', moveTargetBookmarks.value.length)

    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // 使用Promise.all并行处理所有移动操作
      await Promise.all(
        moveTargetBookmarks.value.map(bookmarkId =>
          chrome.bookmarks.move(bookmarkId, { parentId: folderId })
        )
      )
      console.log('✅ 批量移动完成')

      // 重新加载数据以获取正确的书签位置和顺序，使用智能刷新保持展开状态
      setTimeout(async () => {
        await smartRefresh({
          type: 'internal-move',
          preserveState: true
        })
        console.log('✅ 移动书签：列表数据已更新')
      }, 100)
    }

    showMoveModal.value = false
    moveTargetBookmarks.value = []

    // 清理多选状态
    console.log('🧹 清理多选状态')
    clearSelection()

    console.log('✅ 移动操作完成，无需刷新页面')

  } catch (error) {
    console.error('移动书签时出错:', error)
  } finally {
    // 重置内部操作标识
    setTimeout(() => {
      isInternalOperation.value = false
      console.log('🔄 重置移动操作的内部标识')
    }, 200)
  }
}

// 确认模态框处理函数
const showConfirm = (options: {
  type?: 'info' | 'warning' | 'danger'
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void
}) => {
  confirmModalData.value = {
    type: options.type || 'info',
    title: options.title,
    message: options.message,
    confirmText: options.confirmText || '确定',
    cancelText: options.cancelText || '取消',
    onConfirm: options.onConfirm
  }
  showConfirmModal.value = true
}

const handleConfirmModalConfirm = () => {
  showConfirmModal.value = false
  confirmModalData.value.onConfirm()
}

const handleConfirmModalCancel = () => {
  showConfirmModal.value = false
}

// 处理确认打开全部书签
const handleConfirmOpenAll = (data: { bookmarks: BookmarkWithMeta[]; groupTitle: string }) => {
  showConfirm({
    type: 'info',
    title: '打开全部书签',
    message: `是否打开文件夹"${data.groupTitle}"中的 ${data.bookmarks.length} 个书签？`,
    confirmText: '打开',
    cancelText: '取消',
    onConfirm: () => {
      data.bookmarks.forEach(bookmark => {
        if (bookmark.url) {
          window.open(bookmark.url, '_blank')
        }
      })
    }
  })
}

// 处理确认删除文件夹
const handleConfirmDeleteGroup = (data: { groupId: string; groupTitle: string; bookmarkCount: number }) => {
  showConfirm({
    type: 'danger',
    title: '删除文件夹书签',
    message: `确定要删除文件夹"${data.groupTitle}"中的 ${data.bookmarkCount} 个书签吗？`,
    confirmText: '删除',
    cancelText: '取消',
    onConfirm: () => {
      deleteGroup(data.groupId)
    }
  })
}

// 删除撤销
const handleUndoDelete = () => {
  showDeleteToast.value = false
  pendingDeleteBookmarks.value = []
}

// 删除确认
const handleDeleteTimeout = async () => {
  try {
    console.log('⏰ 删除倒计时结束，开始执行删除')
    console.log('📍 待删除的书签ID:', pendingDeleteBookmarks.value)

    // 检查是否使用双数据层模式
    if (useDualLayer.value) {
      console.log('🚀 使用双数据层模式批量删除书签')

      // 批量删除书签
      const deletePromises = pendingDeleteBookmarks.value.map(bookmarkId =>
        deleteBookmarkDualLayer(bookmarkId)
      )

      const results = await Promise.all(deletePromises)
      const successCount = results.filter(Boolean).length

      console.log(`✅ 双数据层批量删除完成: ${successCount}/${pendingDeleteBookmarks.value.length}`)

      // 清理多选状态
      selectedBookmarks.value = selectedBookmarks.value.filter(id =>
        !pendingDeleteBookmarks.value.includes(id)
      )
    } else {
      // 传统模式处理
      // 设置内部操作标识，避免监听器重复处理
      isInternalOperation.value = true

      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        // 检查书签是否仍然存在，然后删除
        const deletePromises = pendingDeleteBookmarks.value.map(async (bookmarkId) => {
          try {
            // 先检查书签是否存在
            const bookmarks = await new Promise<chrome.bookmarks.BookmarkTreeNode[]>((resolve) => {
              chrome.bookmarks.get([bookmarkId], (result) => {
                if (chrome.runtime.lastError) {
                  console.log(`📍 书签 ${bookmarkId} 已不存在，跳过删除`)
                  resolve([])
                } else {
                  resolve(result)
                }
              })
            })

            // 如果书签存在，则删除
            if (bookmarks.length > 0) {
              await chrome.bookmarks.remove(bookmarkId)
              console.log(`✅ 删除书签: ${bookmarkId}`)
            }
          } catch (error) {
            console.log(`⚠️ 删除书签 ${bookmarkId} 时出错:`, error)
          }
        })

        await Promise.all(deletePromises)

        // 检查是否需要删除空文件夹
        await checkAndDeleteEmptyFolders()
      } else {
        // 开发环境：从本地数据中删除书签
        console.log('🛠️ 开发环境：删除书签')

        pendingDeleteBookmarks.value.forEach(bookmarkId => {
          const bookmarkData = JSON.parse(localStorage.getItem('bookmarkData') || '[]')
          const filteredData = bookmarkData.filter((b: any) => b.id !== bookmarkId)
          localStorage.setItem('bookmarkData', JSON.stringify(filteredData))

          // 删除书签元数据
          const bookmarkMeta = JSON.parse(localStorage.getItem('bookmarkMeta') || '{}')
          delete bookmarkMeta[bookmarkId]
          localStorage.setItem('bookmarkMeta', JSON.stringify(bookmarkMeta))
        })
      }

      // 立即从本地状态中移除已删除的书签，避免页面刷新
      const deletedIds = new Set(pendingDeleteBookmarks.value)
      bookmarks.value = bookmarks.value.filter(bookmark => !deletedIds.has(bookmark.id))
      console.log('✅ 从本地状态中移除已删除的书签')

      // 重置内部操作标识
      setTimeout(() => {
        isInternalOperation.value = false
        console.log('🔄 重置删除操作的内部标识')
      }, 200)
    }

    // 清理状态
    showDeleteToast.value = false
    pendingDeleteBookmarks.value = []
    console.log('✅ 删除操作完成')

  } catch (error) {
    console.error('删除书签时出错:', error)
    // 即使出错也要清理状态
    showDeleteToast.value = false
    pendingDeleteBookmarks.value = []
  }
}

// 检查并删除空文件夹
const checkAndDeleteEmptyFolders = async () => {
  try {
    // 获取所有文件夹
    const allFolders = groups.value

    for (const folder of allFolders) {
      try {
        // 检查文件夹是否为空
        const children = await new Promise<chrome.bookmarks.BookmarkTreeNode[]>((resolve) => {
          chrome.bookmarks.getChildren(folder.id, (result) => {
            if (chrome.runtime.lastError) {
              console.warn('获取文件夹子项时出错:', chrome.runtime.lastError)
              resolve([])
            } else {
              resolve(result)
            }
          })
        })

        // 如果文件夹为空（没有书签也没有子文件夹），删除它
        if (children.length === 0) {
          try {
            await chrome.bookmarks.remove(folder.id)
            console.log(`✅ 删除空文件夹: ${folder.title}`)
          } catch (error) {
            console.warn(`⚠️ 删除空文件夹失败: ${folder.title}`, error)
          }
        }
      } catch (error) {
        console.warn(`⚠️ 检查文件夹 ${folder.title} 时出错:`, error)
      }
    }
  } catch (error) {
    console.warn('⚠️ 检查空文件夹时出错:', error)
  }
}

// 显示移动文件夹模态框
const handleShowMoveFolderModal = (data: { folderId: string; folderTitle: string; parentId?: string }) => {
  moveFolderData.value = data
  showMoveFolderModal.value = true
}

// 处理移动文件夹
const handleMoveFolder = async (targetFolderId: string) => {
  try {
    if (!moveFolderData.value) return

    console.log('📁 移动文件夹:', {
      folderId: moveFolderData.value.folderId,
      folderTitle: moveFolderData.value.folderTitle,
      targetFolderId
    })

    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      await chrome.bookmarks.move(moveFolderData.value.folderId, {
        parentId: targetFolderId
      })
      console.log('✅ 文件夹移动完成')
    }

    showMoveFolderModal.value = false
    moveFolderData.value = null

    // 使用智能刷新保持展开状态
    await smartRefresh({
      type: 'external-change',
      preserveState: true
    })
    console.log('✅ 移动文件夹：智能刷新完成')

  } catch (error) {
    console.error('移动文件夹时出错:', error)
  }
}

// 处理文件夹排序（排序操作由 FolderSortManager 直接处理，这里只需要刷新页面）
const handleSortFolders = async () => {
  try {
    console.log('📁 文件夹排序完成，使用智能刷新')
    await smartRefresh({
      type: 'external-change',
      preserveState: true
    })
    console.log('✅ 文件夹排序智能刷新完成')
  } catch (error) {
    console.error('刷新文件夹数据时出错:', error)
  }
}

// 处理文件夹重命名
const handleFolderRenamed = async (data: { folderId: string; oldTitle: string; newTitle: string }) => {
  try {
    console.log('📁 文件夹重命名完成，使用智能刷新:', data)
    await smartRefresh({
      type: 'external-change',
      preserveState: true
    })
    console.log('✅ 文件夹重命名智能刷新完成')
  } catch (error) {
    console.error('刷新文件夹数据时出错:', error)
  }
}



// 全局快捷键处理
const handleGlobalKeydown = (e: KeyboardEvent) => {
  const searchShortcut = localStorage.getItem('searchShortcut') || 'cmd+/'

  // 检查快捷键
  let shouldFocusSearch = false

  if (searchShortcut === 'cmd+/' && (e.metaKey || e.ctrlKey) && e.key === '/') {
    shouldFocusSearch = true
  } else if (searchShortcut === 'ctrl+k' && (e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'k') {
    shouldFocusSearch = true
  } else if (searchShortcut === 'ctrl+f' && (e.metaKey || e.ctrlKey) && e.key.toLowerCase() === 'f') {
    shouldFocusSearch = true
  }

  if (shouldFocusSearch) {
    e.preventDefault()
    const searchInput = document.querySelector('.search-input') as HTMLInputElement
    if (searchInput) {
      searchInput.focus()
    }
  }

  // ESC 退出搜索（只失焦，不清空内容）
  if (e.key === 'Escape') {
    const searchInput = document.querySelector('.search-input') as HTMLInputElement
    if (searchInput && document.activeElement === searchInput) {
      searchInput.blur()
      // 不清空搜索内容，只失焦
    }
  }
}

// 设置 Chrome 书签变化监听器
const setupBookmarkListeners = () => {
  if (typeof chrome !== 'undefined' && chrome.bookmarks) {
    console.log('🔗 设置 Chrome 书签变化监听器')

    // 监听书签创建
    chrome.bookmarks.onCreated.addListener(async (id, bookmark) => {
      // 如果是内部操作，跳过监听器处理
      if (isInternalOperation.value) {
        console.log('⏭️ 跳过内部操作的书签创建监听:', bookmark.title || '新文件夹')
        return
      }
      console.log('📝 检测到外部书签创建:', bookmark.title || '新文件夹')
      await smartRefresh({ type: 'external-change' })
    })

    // 监听书签删除
    chrome.bookmarks.onRemoved.addListener(async (id, removeInfo) => {
      if (isInternalOperation.value) {
        console.log('⏭️ 跳过内部操作的书签删除监听:', id)
        return
      }
      console.log('🗑️ 检测到外部书签删除:', id)
      await smartRefresh({ type: 'external-change' })
    })

    // 监听书签更新（重命名、URL变更等）
    chrome.bookmarks.onChanged.addListener(async (id, changeInfo) => {
      if (isInternalOperation.value) {
        console.log('⏭️ 跳过内部操作的书签更新监听:', changeInfo.title || id)
        return
      }
      console.log('✏️ 检测到外部书签更新:', changeInfo.title || id)
      await smartRefresh({ type: 'external-change' })
    })

    // 监听书签移动
    chrome.bookmarks.onMoved.addListener(async (id, moveInfo) => {
      if (isInternalOperation.value) {
        console.log('⏭️ 跳过内部操作的书签移动监听:', id)
        return
      }
      console.log('📁 检测到外部书签移动:', id)
      await smartRefresh({ type: 'external-change' })
    })

    // 监听书签导入完成
    if (chrome.bookmarks.onImportEnded) {
      chrome.bookmarks.onImportEnded.addListener(async () => {
        console.log('📥 检测到书签导入完成')
        await smartRefresh({ type: 'external-change' })
      })
    }
  }
}

// 设置页面可见性变化监听器
const setupVisibilityListener = () => {
  console.log('👁️ 设置页面可见性变化监听器')

  const handleVisibilityChange = async () => {
    if (!document.hidden) {
      // 页面变为可见时只更新数据，不刷新页面
      console.log('👁️ 页面重新可见，更新书签数据')
      try {
        await loadBookmarks()
        console.log('✅ 页面可见性变化后数据更新完成，无需刷新页面')
      } catch (error) {
        console.error('❌ 页面可见性变化后数据更新失败:', error)
      }
    } else {
      console.log('👁️ 页面变为隐藏')
    }
  }

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 监听窗口焦点变化（作为备用方案）
  const handleFocus = async () => {
    console.log('🔍 窗口重新获得焦点，更新书签数据')
    try {
      await loadBookmarks()
      console.log('✅ 窗口焦点变化后数据更新完成，无需刷新页面')
    } catch (error) {
      console.error('❌ 窗口焦点变化后数据更新失败:', error)
    }
  }

  window.addEventListener('focus', handleFocus)
}

// 初始化
onMounted(async () => {
  try {
    if (useDualLayer.value) {
      console.log('🚀 初始化双数据层模式')
      await loadFromChrome()
    } else {
      console.log('🔄 使用传统模式')
      await loadBookmarks()
    }
  } finally {
    // 无论成功还是失败，都结束加载状态
    isLoading.value = false
  }

  // 添加全局快捷键监听
  document.addEventListener('keydown', handleGlobalKeydown)

  // 添加主内容区域滚动监听
  if (mainContentRef.value) {
    mainContentRef.value.addEventListener('scroll', handleScroll, { passive: true })
  }

  // 初始化CSS变量到body上，确保Teleport的元素能访问到
  document.body.style.setProperty('--drawer-offset', '0px')
  document.body.style.setProperty('--drawer-offset-mobile', '0px')

  // 设置主题
  applyTheme(theme.value)

  // 监听系统主题变化
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  const handleThemeChange = () => {
    if (theme.value === 'auto') {
      applyTheme(theme.value)
    }
  }

  mediaQuery.addEventListener('change', handleThemeChange)

  // 监听设置变化，实时更新界面（只在必要时刷新）
  watch(folderLayout, () => {
    console.log('文件夹布局变更:', folderLayout.value, '- 无需刷新页面')
    // 布局变更通过 CSS 响应式处理，无需强制重新渲染
  })

  watch(autoCollapseFolders, () => {
    console.log('自动折叠设置变更:', autoCollapseFolders.value, '- 无需刷新页面')
    // 折叠设置通过响应式状态处理，无需强制重新渲染
  })

  window.addEventListener('settings-changed', handleSettingsChanged as unknown as EventListener)

  // 监听设置面板状态（已移除，使用新的抽屉系统）

  // 设置书签变化监听器
  setupBookmarkListeners()

  // 设置页面可见性变化监听器
  setupVisibilityListener()

  // 监听标签页拖拽创建书签事件
  window.addEventListener('bookmark-created', handleBookmarkCreated as EventListener)

  // 初始聚焦搜索框
  setTimeout(() => {
    searchBarRef.value?.focusInput()
  }, 500)
})

// 清理
onUnmounted(() => {
  // 清理CSS变量
  document.body.style.removeProperty('--drawer-offset')
  document.body.style.removeProperty('--drawer-offset-mobile')

  document.removeEventListener('keydown', handleGlobalKeydown)
  if (mainContentRef.value) {
    mainContentRef.value.removeEventListener('scroll', handleScroll)
  }
  window.removeEventListener('settings-changed', handleSettingsChanged as unknown as EventListener)
  window.removeEventListener('bookmark-created', handleBookmarkCreated as EventListener)
  window.removeEventListener('settings-opened', () => {})
  window.removeEventListener('settings-closed', () => {})

  // 清理页面可见性监听器
  document.removeEventListener('visibilitychange', () => {})
  window.removeEventListener('focus', () => {})

  // 清理 Chrome 书签监听器
  if (typeof chrome !== 'undefined' && chrome.bookmarks) {
    // 注意：Chrome API 的监听器清理需要传入相同的函数引用
    // 由于我们使用的是匿名函数，这里只是示意性的清理
    console.log('🧹 清理 Chrome 书签监听器')
  }

  // 清理主题变化监听器
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
  mediaQuery.removeEventListener('change', () => {
    if (theme.value === 'auto') {
      applyTheme(theme.value)
    }
  })
})
</script>

<style scoped>
/* 骨架屏加载动画 */
.loading-skeleton {
  padding: 2rem;
  margin: 0 auto;
  animation: fadeIn 0.3s ease-out;
}

.skeleton-search-bar {
  height: 3rem;
  background: var(--bg-elevated);
  border-radius: 1.5rem;
  margin-bottom: 2rem;
  position: relative;
  overflow: hidden;
}

.skeleton-folders {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.skeleton-folder {
  background: var(--bg-surface);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: var(--shadow-card);
}

.skeleton-folder-title {
  height: 1.5rem;
  width: 8rem;
  background: var(--bg-elevated);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  position: relative;
  overflow: hidden;
}

.skeleton-bookmarks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
}

.skeleton-bookmark {
  aspect-ratio: 1;
  background: var(--bg-elevated);
  border-radius: 1rem;
  position: relative;
  overflow: hidden;
}

/* 骨架屏闪烁动画 */
.skeleton-search-bar::before,
.skeleton-folder-title::before,
.skeleton-bookmark::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.4),
    transparent
  );
  animation: shimmer 1.5s infinite;
}

/* 暗色主题下的骨架屏 */
[data-theme="dark"] .skeleton-search-bar::before,
[data-theme="dark"] .skeleton-folder-title::before,
[data-theme="dark"] .skeleton-bookmark::before {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .skeleton-bookmarks-grid {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
    gap: 0.75rem;
  }

  .loading-skeleton {
    padding: 1rem;
  }

  .skeleton-folder {
    padding: 1rem;
  }
}

/* 新的应用布局 */
.app-layout {
  position: relative;
  overflow-x: hidden;
}

/* 主内容区域 - 垂直居中 */
.main-content-centered {
  height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 8rem 2rem 1rem 2rem; /* 增加顶部padding为搜索框留出空间 */
  box-sizing: border-box;
  overflow-y: auto;
  overflow-x: hidden;
}

.content-container {
  width: 100%;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* 侧边按钮组 */
.side-buttons-left {
  position: fixed;
  left: 24px;
  bottom: 120px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 40;
}

.side-buttons-right {
  position: fixed;
  right: 24px;
  bottom: 120px;
  display: flex;
  flex-direction: column;
  gap: 16px;
  z-index: 40;
}

/* 移动端响应式调整 */
@media (max-width: 768px) {
  .main-content-centered {
    padding: 1rem 1rem 0.5rem 1rem;
    align-items: flex-start;
    min-height: 100vh;
  }

  .content-container {
    gap: 0.75rem;
  }

  .side-buttons-left {
    left: 16px;
    bottom: 60px;
    gap: 12px;
  }

  .side-buttons-right {
    right: 16px;
    bottom: 60px;
    gap: 12px;
  }

  /* 移动端减少推移距离 */
  .content-pushed {
    transform: translateX(120px);
  }
}

/* 设置面板打开时的左移效果 */
.main-content {
  transition: margin-left 0.3s ease-in-out;
}

body.settings-open .main-content {
  margin-left: var(--settings-offset, -120px);
}

/* 确保设置面板不受影响 */
body.settings-open .settings-panel {
  transform: translateX(0) !important;
}

/* 搜索框样式 - 50%宽度 */
.search-container {
  width: 50%;
  margin: 0 auto 0.75rem auto;
}

/* 书签内容容器 - 固定尺寸 */
.bookmark-content-container {
  margin: 0 auto;
}

.bookmark-content-wrapper {
  height: 400px;
  overflow: hidden;
  border-radius: 1rem;
  background: rgba(var(--bg-base-100, 255 255 255), 0.8);
  backdrop-filter: blur(8px);
  padding: 0;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.empty-state {
  min-height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 1rem;
  background: rgba(var(--bg-base-100, 255 255 255), 0.5);
  backdrop-filter: blur(8px);
}

/* 内容推移效果 */
.content-pushed {
  transform: translateX(180px);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .search-container {
    width: 90%;
  }

  .bookmark-content-wrapper {
    min-height: 300px;
    max-height: 400px;
    padding: 1rem;
  }

  .empty-state {
    min-height: 300px;
  }
}

@media (max-width: 480px) {
  .search-container {
    width: 95%;
  }

  .bookmark-content-wrapper {
    min-height: 250px;
    max-height: 350px;
    padding: 0.75rem;
  }

  .empty-state {
    min-height: 250px;
  }
}

/* 标签卡片样式 */
.tag-card {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 0.5rem;
  background: rgba(var(--bg-base-100, 255 255 255), 1);
  border: 1px solid rgba(var(--border-base-300, 209 213 219), 0.5);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-width: 80px;
  max-width: 200px;
}

.tag-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tag-card-content {
  padding: 0.5rem 0.75rem;
  position: relative;
  overflow: hidden;
}

.tag-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: rgba(var(--text-base-content, 31 41 55), 1);
  display: block;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.tag-accent-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  transition: all 0.2s ease;
}

.tag-card-active .tag-accent-line {
  height: 4px;
}

.tag-card-active {
  background: rgba(var(--bg-primary, 59 130 246), 0.1);
  border-color: rgba(var(--bg-primary, 59 130 246), 0.5);
}

.tag-card-active .tag-accent-line {
  bottom: 5px;
  width: 70%;
  left: 15%;
  border-radius: 0.5rem;
  height: 10px;
  opacity: 0.4;
}

.tag-card-clear {
  background: rgba(var(--bg-error, 239 68 68), 0.1);
  border-color: rgba(var(--bg-error, 239 68 68), 0.3);
}

.tag-card-clear .tag-accent-line {
  background: rgba(var(--bg-error, 239 68 68), 0.5);
}

.tag-card-clear:hover .tag-accent-line {
  background: rgba(var(--bg-error, 239 68 68), 1);
}

/* 暗色主题适配 */
[data-theme="dark"] .tag-card {
  background: rgba(30, 41, 59, 1);
  border-color: rgba(71, 85, 105, 0.5);
}

[data-theme="dark"] .tag-text {
  color: rgba(248, 250, 252, 1);
}

[data-theme="dark"] .tag-accent-line {
  background: rgba(147, 197, 253, 0.3);
}

[data-theme="dark"] .tag-card-active .tag-accent-line {
  background: rgba(147, 197, 253, 1);
}

[data-theme="dark"] .tag-card-active {
  background: rgba(147, 197, 253, 0.1);
  border-color: rgba(147, 197, 253, 0.5);
}
</style>
