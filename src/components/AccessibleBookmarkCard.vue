<!--
  可访问性增强的书签卡片组件
  支持键盘导航、屏幕阅读器、ARIA属性
-->

<template>
  <div
    ref="cardRef"
    class="accessible-bookmark-card"
    :class="{
      'card-selected': isSelected,
      'card-focused': isFocused,
      'card-dragging': isDragging
    }"
    :tabindex="tabIndex"
    :role="role"
    :aria-label="ariaLabel"
    :aria-describedby="ariaDescribedBy"
    :aria-selected="isSelected"
    :aria-expanded="isExpanded"
    @click="handleClick"
    @keydown="handleKeydown"
    @focus="handleFocus"
    @blur="handleBlur"
    @contextmenu="handleContextMenu"
  >
    <!-- 书签图标 -->
    <div class="bookmark-icon" :aria-hidden="true">
      <BookmarkIcon
        :url="bookmark.url"
        :alt="bookmark.title"
        size="md"
      />
    </div>

    <!-- 书签信息 -->
    <div class="bookmark-info">
      <div class="bookmark-title">
        {{ bookmark.title }}
      </div>
      
      <div v-if="showUrl" class="bookmark-url">
        {{ displayUrl }}
      </div>
      
      <div v-if="bookmark.tags && bookmark.tags.length > 0" class="bookmark-tags">
        <span
          v-for="tag in bookmark.tags"
          :key="tag"
          class="tag"
          :style="{ backgroundColor: getTagColor(tag) }"
          :aria-label="`标签: ${tag}`"
        >
          {{ tag }}
        </span>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div v-if="showActions" class="bookmark-actions" :aria-label="actionsAriaLabel">
      <button
        class="action-btn"
        :aria-label="`编辑书签 ${bookmark.title}`"
        @click.stop="handleEdit"
        @keydown.stop="handleActionKeydown($event, 'edit')"
      >
        ✏️
      </button>
      
      <button
        class="action-btn"
        :aria-label="`删除书签 ${bookmark.title}`"
        @click.stop="handleDelete"
        @keydown.stop="handleActionKeydown($event, 'delete')"
      >
        🗑️
      </button>
      
      <button
        class="action-btn"
        :aria-label="`移动书签 ${bookmark.title}`"
        @click.stop="handleMove"
        @keydown.stop="handleActionKeydown($event, 'move')"
      >
        📁
      </button>
    </div>

    <!-- 选择指示器 -->
    <div
      v-if="isSelected"
      class="selection-indicator"
      :aria-hidden="true"
    >
      ✓
    </div>

    <!-- 焦点指示器 -->
    <div
      v-if="isFocused"
      class="focus-indicator"
      :aria-hidden="true"
    ></div>

    <!-- 屏幕阅读器专用描述 -->
    <div
      :id="descriptionId"
      class="sr-only"
    >
      书签：{{ bookmark.title }}。
      网址：{{ bookmark.url }}。
      <span v-if="bookmark.tags && bookmark.tags.length > 0">
        标签：{{ bookmark.tags.join('、') }}。
      </span>
      <span v-if="isSelected">已选中。</span>
      按回车键打开，按空格键选择，按菜单键或右键查看更多选项。
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAccessibility } from '../composables/useAccessibility'
import { useTagColors } from '../composables/useTagColors'
import BookmarkIcon from './BookmarkIcon.vue'
import type { BookmarkWithMeta } from '../types'

// Props
interface Props {
  bookmark: BookmarkWithMeta
  isSelected?: boolean
  isDragging?: boolean
  showUrl?: boolean
  showActions?: boolean
  tabIndex?: number
  role?: string
}

const props = withDefaults(defineProps<Props>(), {
  isSelected: false,
  isDragging: false,
  showUrl: true,
  showActions: true,
  tabIndex: 0,
  role: 'button'
})

// Emits
const emit = defineEmits<{
  'click': [bookmark: BookmarkWithMeta, event: MouseEvent]
  'context-menu': [bookmark: BookmarkWithMeta, event: MouseEvent]
  'edit': [bookmark: BookmarkWithMeta]
  'delete': [bookmark: BookmarkWithMeta]
  'move': [bookmark: BookmarkWithMeta]
  'select': [bookmark: BookmarkWithMeta]
  'focus': [bookmark: BookmarkWithMeta]
  'blur': [bookmark: BookmarkWithMeta]
}>()

// Composables
const { announce, setAriaLabel, setAriaDescribedBy } = useAccessibility()
const { getTagColor } = useTagColors()

// 响应式状态
const cardRef = ref<HTMLElement>()
const isFocused = ref(false)
const isExpanded = ref(false)

// 计算属性
const descriptionId = computed(() => `bookmark-desc-${props.bookmark.id}`)

const ariaLabel = computed(() => {
  return `书签 ${props.bookmark.title}`
})

const ariaDescribedBy = computed(() => {
  return descriptionId.value
})

const actionsAriaLabel = computed(() => {
  return `${props.bookmark.title} 的操作`
})

const displayUrl = computed(() => {
  try {
    const url = new URL(props.bookmark.url)
    return url.hostname
  } catch {
    return props.bookmark.url
  }
})

// 方法
const handleClick = (event: MouseEvent) => {
  emit('click', props.bookmark, event)
  
  // 宣布操作
  announce(`打开书签 ${props.bookmark.title}`, 'default-live-region', 'polite')
}

const handleKeydown = (event: KeyboardEvent) => {
  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      handleClick(event as any)
      break
      
    case ' ':
      event.preventDefault()
      emit('select', props.bookmark)
      announce(
        props.isSelected ? `取消选择 ${props.bookmark.title}` : `选择 ${props.bookmark.title}`,
        'default-live-region',
        'polite'
      )
      break
      
    case 'ContextMenu':
    case 'F10':
      if (event.shiftKey || event.key === 'ContextMenu') {
        event.preventDefault()
        handleContextMenu(event as any)
      }
      break
      
    case 'Delete':
      event.preventDefault()
      handleDelete()
      break
      
    case 'F2':
      event.preventDefault()
      handleEdit()
      break
  }
}

const handleFocus = () => {
  isFocused.value = true
  emit('focus', props.bookmark)
}

const handleBlur = () => {
  isFocused.value = false
  emit('blur', props.bookmark)
}

const handleContextMenu = (event: MouseEvent) => {
  event.preventDefault()
  emit('context-menu', props.bookmark, event)
  
  announce(`打开 ${props.bookmark.title} 的上下文菜单`, 'default-live-region', 'assertive')
}

const handleEdit = () => {
  emit('edit', props.bookmark)
  announce(`编辑书签 ${props.bookmark.title}`, 'default-live-region', 'polite')
}

const handleDelete = () => {
  emit('delete', props.bookmark)
  announce(`删除书签 ${props.bookmark.title}`, 'default-live-region', 'assertive')
}

const handleMove = () => {
  emit('move', props.bookmark)
  announce(`移动书签 ${props.bookmark.title}`, 'default-live-region', 'polite')
}

const handleActionKeydown = (event: KeyboardEvent, action: string) => {
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    
    switch (action) {
      case 'edit':
        handleEdit()
        break
      case 'delete':
        handleDelete()
        break
      case 'move':
        handleMove()
        break
    }
  }
}

// 生命周期
onMounted(() => {
  if (cardRef.value) {
    // 设置ARIA属性
    setAriaLabel(cardRef.value, ariaLabel.value)
    setAriaDescribedBy(cardRef.value, ariaDescribedBy.value)
  }
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.accessible-bookmark-card {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.accessible-bookmark-card:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.accessible-bookmark-card:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.card-selected {
  border-color: #3b82f6;
  background: #eff6ff;
}

.card-focused {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.card-dragging {
  opacity: 0.5;
  transform: rotate(2deg) scale(1.02);
}

.bookmark-icon {
  flex-shrink: 0;
}

.bookmark-info {
  flex: 1;
  min-width: 0;
}

.bookmark-title {
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-url {
  font-size: 0.75rem;
  color: #64748b;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.bookmark-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.tag {
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  color: white;
  line-height: 1;
}

.bookmark-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.accessible-bookmark-card:hover .bookmark-actions,
.accessible-bookmark-card:focus .bookmark-actions {
  opacity: 1;
}

.action-btn {
  padding: 0.25rem;
  border: none;
  background: #f1f5f9;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.75rem;
  line-height: 1;
}

.action-btn:hover {
  background: #e2e8f0;
}

.action-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 1px;
}

.selection-indicator {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  width: 1.25rem;
  height: 1.25rem;
  background: #3b82f6;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 700;
}

.focus-indicator {
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border: 2px solid #3b82f6;
  border-radius: 0.5rem;
  pointer-events: none;
}

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .accessible-bookmark-card {
    border-width: 3px;
  }
  
  .accessible-bookmark-card:focus {
    border-color: #000;
    box-shadow: 0 0 0 3px #000;
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  .accessible-bookmark-card {
    transition: none;
  }
  
  .bookmark-actions {
    transition: none;
  }
  
  .action-btn {
    transition: none;
  }
}

/* 暗色主题 */
[data-theme="dark"] .accessible-bookmark-card {
  background: #1e293b;
  border-color: #475569;
}

[data-theme="dark"] .bookmark-title {
  color: #f1f5f9;
}

[data-theme="dark"] .bookmark-url {
  color: #94a3b8;
}

[data-theme="dark"] .action-btn {
  background: #334155;
  color: #e2e8f0;
}

[data-theme="dark"] .action-btn:hover {
  background: #475569;
}

[data-theme="dark"] .card-selected {
  background: #1e40af;
  border-color: #3b82f6;
}
</style>
