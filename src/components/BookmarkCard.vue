<template>
  <div
    ref="bookmarkElement"
    class="relative"
    :data-bookmark-id="bookmark.id"
    @click="handleClick"
    @contextmenu.prevent="showContextMenu"
    @mouseover="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <!-- 图标模式 - 使用 daisyUI card 组件 -->
    <div
      v-if="viewMode === 'icon'"
      :title="bookmark.url"
      class="card bookmark-card-glass shadow-md hover:shadow-lg transition-all duration-300 cursor-pointer relative"
      :class="{
        'ring-2 ring-primary': isSelected,
        'ring-2 ring-warning': showContextMenuState,
        'opacity-50 bg-error/10': isPendingDelete,
        'bookmark-card-square': cardStyle === 'square',
        'bookmark-card-rectangle': cardStyle === 'rectangle'
      }"
    >
      <div
        class="card-body p-4 min-w-0"
        :class="{
          'items-center text-center': cardStyle === 'square',
          'flex-row items-center text-left': cardStyle === 'rectangle'
        }"
      >
        <div
          class="avatar"
          :class="{
            'mb-2': cardStyle === 'square',
            'mr-3 flex-shrink-0': cardStyle === 'rectangle'
          }"
        >
          <BookmarkIcon
            :url="bookmark.url || ''"
            :alt="bookmark.title || ''"
            :size="cardStyle === 'square' ? 'lg' : 'md'"
            :custom-icon="bookmark.icon"
            class="rounded-xl"
          />
        </div>
        <div
          class="flex-1 min-w-0"
          :class="{
            'max-w-full': cardStyle === 'square',
            'max-w-[80px]': cardStyle === 'rectangle'
          }"
        >
          <h3
            class="card-title text-xs bookmark-title-icon"
            :class="{
              'justify-center': cardStyle === 'square',
              'justify-start': cardStyle === 'rectangle'
            }"
          >
            {{ bookmark.displayTitle || bookmark.title }}
          </h3>
        </div>
      </div>

      <!-- 标签颜色底部线条 -->
      <div
        v-if="bookmark.tags?.length"
        class="absolute bottom-0 left-0 right-0 flex pointer-events-none tag-underlines"
      >
        <div
          v-for="(tag, index) in bookmark.tags.slice(0, 5)"
          :key="tag"
          class="flex-1 h-1 transition-all duration-300"
          :style="getTagUnderlineStyle(tag, index)"
        ></div>
      </div>
    </div>

    <!-- 卡片模式 - 使用 daisyUI card 组件 -->
    <div
      v-else
      class="card card-side bookmark-card-glass shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer"
      :class="{
        'ring-2 ring-primary': isSelected,
        'ring-2 ring-warning': showContextMenuState,
        'opacity-50 bg-error/10': isPendingDelete
      }"
      :title="bookmark.url"
    >
      <div class="avatar p-4">
        <BookmarkIcon
          :url="bookmark.url || ''"
          :alt="bookmark.title || ''"
          size="md"
          :custom-icon="bookmark.icon"
          class="rounded-lg"
        />
      </div>
      <div class="card-body py-4 pr-4 pl-0 min-w-0 flex-1">
        <h3 class="card-title text-base bookmark-title-card">
          {{ bookmark.displayTitle || bookmark.title }}
        </h3>

      </div>

      <!-- 标签颜色底部线条 -->
      <div
        v-if="bookmark.tags?.length"
        class="absolute bottom-0 left-0 right-0 flex pointer-events-none tag-underlines"
      >
        <div
          v-for="(tag, index) in bookmark.tags.slice(0, 5)"
          :key="tag"
          class="flex-1 h-1 transition-all duration-300"
          :style="getTagUnderlineStyle(tag, index)"
        ></div>
      </div>
    </div>




  </div>

  <!-- 右键菜单 - 使用 daisyUI menu 组件 -->
  <teleport to="body">
    <div
      v-if="contextMenuVisible"
      class="fixed z-[99999]"
      :style="contextMenuStyle"
      @click.stop
    >
      <ul class="menu bg-base-100 rounded-box shadow-lg w-48">
        <li>
          <a @click="handleMenuClick('select')" class="flex items-center gap-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            选择
          </a>
        </li>
        <li>
          <a @click="handleMenuClick('edit')" class="flex items-center gap-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            编辑
          </a>
        </li>
        <li>
          <a @click="handleMenuClick('move')" class="flex items-center gap-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M7 7h10v10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M7 17 17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            移动
          </a>
        </li>
        <li>
          <a @click="handleMenuClick('qrcode')" class="flex items-center gap-2">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <rect x="3" y="3" width="5" height="5" stroke="currentColor" stroke-width="2"/>
              <rect x="16" y="3" width="5" height="5" stroke="currentColor" stroke-width="2"/>
              <rect x="3" y="16" width="5" height="5" stroke="currentColor" stroke-width="2"/>
              <path d="M13 13h3v3h-3z" stroke="currentColor" stroke-width="2"/>
            </svg>
            二维码
          </a>
        </li>
        <div class="divider my-0"></div>
        <li>
          <a @click="handleMenuClick('delete')" class="flex items-center gap-2 text-error hover:text-error">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path d="M3 6h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
            删除
          </a>
        </li>
      </ul>
    </div>
  </teleport>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref, watch, onMounted, onUnmounted, computed } from 'vue'
import type { BookmarkWithMeta } from '../types'
import { useTagColors } from '../composables/useTagColors'
import { useTagDots } from '../composables/useTagDots'
import { useBookmarkDisplay } from '../composables/useBookmarkDisplay'
import BookmarkIcon from './BookmarkIcon.vue'

// 标签颜色管理
const { getTagColor } = useTagColors()

// 书签显示设置
const { iconShape } = useBookmarkDisplay()

const props = defineProps<{
  bookmark: BookmarkWithMeta
  viewMode: 'icon' | 'card'
  isSelected: boolean
  showTags: boolean
  hasAnySelected: boolean
  isPendingDelete: boolean
  cardStyle?: 'square' | 'rectangle'
}>()

const emit = defineEmits<{
  (e: 'toggle-select', id: string): void
  (e: 'shift-select', id: string): void
  (e: 'edit', bookmark: BookmarkWithMeta): void
  (e: 'delete', bookmark: BookmarkWithMeta): void
  (e: 'move', bookmark: BookmarkWithMeta): void
  (e: 'pin', bookmark: BookmarkWithMeta): void
  (e: 'clear-icon', bookmark: BookmarkWithMeta): void
  (e: 'show-qrcode', bookmark: BookmarkWithMeta): void
  (e: 'reorder', data: {
    draggedBookmarkId: string
    targetBookmarkId: string
    sourceParentId: string
    targetParentId: string
    sourceIndex: number
    targetIndex: number
    dropPosition?: 'before' | 'after'
  }): void
}>()



const hovered = ref(false)
const contextMenuVisible = ref(false)
const contextMenuStyle = ref({})
const showTooltip = ref(false)
const tooltipStyle = ref({})
const showContextMenuState = ref(false) // 右键菜单状态，用于样式控制
const bookmarkElement = ref<HTMLElement | null>(null) // 书签元素引用
const mousePosition = ref({ x: 0, y: 0 }) // 存储鼠标位置




// 截断标题函数已移除，现在使用CSS实现标题截断

// 获取标签底部线条样式
function getTagUnderlineStyle(tag: string, index: number) {
  const tagColor = getTagColor(tag)
  const tags = props.bookmark.tags || []
  const isFirst = index === 0
  const isLast = index === tags.length - 1

  let borderRadius = '0'
  if (isFirst && isLast) {
    // 只有一个标签
    borderRadius = '0 0 0.75rem 0.75rem'
  } else if (isFirst) {
    // 第一个标签
    borderRadius = '0 0 0 0.75rem'
  } else if (isLast) {
    // 最后一个标签
    borderRadius = '0 0 0.75rem 0'
  }

  return {
    backgroundColor: tagColor,
    opacity: 0.8,
    borderRadius: borderRadius
  }
}

// 鼠标进入事件
function onMouseEnter(e: MouseEvent) {
  hovered.value = true

  // 显示悬浮提示
  if (props.bookmark.url) {
    showTooltip.value = true
    const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
    tooltipStyle.value = {
      left: `${rect.left}px`,
      top: `${rect.bottom + 5}px`,
      maxWidth: '300px',
      whiteSpace: 'nowrap',
      overflow: 'hidden',
      textOverflow: 'ellipsis'
    }
  }
}

// 鼠标离开事件
function onMouseLeave() {
  hovered.value = false
  showTooltip.value = false
}

// 切换选择状态
function toggleSelect() {
  emit('toggle-select', props.bookmark.id)
}

// 防抖处理，避免快速连续点击
let clickTimeout: number | null = null

// 点击书签的处理逻辑
function handleClick(e: MouseEvent) {
  // 如果点击的是选择框，不执行任何操作
  if ((e.target as HTMLElement).closest('.checkbox-area')) return

  e.preventDefault()
  e.stopPropagation()

  // 防抖处理：如果在短时间内重复点击，忽略后续点击
  if (clickTimeout) {
    return
  }

  clickTimeout = window.setTimeout(() => {
    clickTimeout = null
  }, 200) // 200ms 防抖

  // 如果有任何书签被选中，点击书签就是切换选择状态
  if (props.hasAnySelected) {
    // 检查是否按住Shift键
    if (e.shiftKey) {
      emit('shift-select', props.bookmark.id)
    } else {
      toggleSelect()
    }
  } else {
    // 没有书签被选中时，点击打开书签
    if (props.bookmark.url) {
      window.open(props.bookmark.url, '_blank')
    }
  }
}

// 更新菜单位置
function updateMenuPosition() {
  if (!contextMenuVisible.value) return

  const menuWidth = 120 // 减少菜单宽度
  const menuHeight = 160 // 减少菜单高度

  // 使用鼠标位置作为起点
  let left = mousePosition.value.x
  let top = mousePosition.value.y

  // 防止菜单超出右边界
  if (left + menuWidth > window.innerWidth) {
    left = mousePosition.value.x - menuWidth
  }

  // 防止菜单超出底部边界
  if (top + menuHeight > window.innerHeight) {
    top = mousePosition.value.y - menuHeight
  }

  // 防止菜单超出左边界
  if (left < 10) {
    left = 10
  }

  // 防止菜单超出顶部边界
  if (top < 10) {
    top = 10
  }

  contextMenuStyle.value = {
    left: `${left}px`,
    top: `${top}px`
  }
}

// 显示右键菜单
function showContextMenu(e: MouseEvent) {
  e.preventDefault()

  // 记录鼠标位置
  mousePosition.value = {
    x: e.clientX,
    y: e.clientY
  }

  // 关闭所有其他右键菜单
  document.dispatchEvent(new CustomEvent('close-all-context-menus'))

  contextMenuVisible.value = true
  showContextMenuState.value = true // 设置右键状态

  updateMenuPosition()

  // 监听窗口滚动和大小变化
  window.addEventListener('scroll', updateMenuPosition, true)
  window.addEventListener('resize', updateMenuPosition)

  // 点击其他地方关闭菜单
  setTimeout(() => {
    document.addEventListener('click', hideContextMenu)
  }, 0)
}

// 隐藏右键菜单
function hideContextMenu() {
  contextMenuVisible.value = false
  showContextMenuState.value = false // 清除右键状态
  document.removeEventListener('click', hideContextMenu)
  window.removeEventListener('scroll', updateMenuPosition, true)
  window.removeEventListener('resize', updateMenuPosition)
}

// 处理菜单点击
function handleMenuClick(action: string) {
  hideContextMenu()

  switch (action) {
    case 'select':
      emit('toggle-select', props.bookmark.id)
      break
    case 'edit':
      emit('edit', props.bookmark)
      break
    case 'move':
      emit('move', props.bookmark)
      break
    case 'qrcode':
      emit('show-qrcode', props.bookmark)
      break
    case 'delete':
      emit('delete', props.bookmark)
      break
  }
}





const resolvedIconUrl = ref(fallbackIcon)

async function extractBestIconFromHtml(html: string, baseUrl: string): Promise<string | null> {
  try {
    const doc = new DOMParser().parseFromString(html, 'text/html')
    const links = Array.from(
      doc.querySelectorAll('link[rel*="icon"], meta[property*="image"], meta[name="msapplication-TileImage"]')
    )
    const parsed = links
      .map(el => {
        const href = el.getAttribute('href') || el.getAttribute('content')
        if (!href) return null
        try {
          const fullUrl = new URL(href, baseUrl).href
          const sizes = el.getAttribute('sizes') || ''
          const sizeVal = parseInt(sizes.split('x')[0]) || 0
          return {
            href: fullUrl,
            ext: href.split('.').pop()?.toLowerCase(),
            size: sizeVal
          }
        } catch {
          return null
        }
      })
      .filter(Boolean) as { href: string; ext?: string; size: number }[]

    const svg = parsed.find(p => p.ext === 'svg')
    if (svg) return svg.href

    const sorted = parsed
      .filter(p => ['png', 'ico', 'jpg', 'jpeg', 'webp'].includes(p.ext || ''))
      .sort((a, b) => b.size - a.size)

    return sorted[0]?.href || null
  } catch {
    return null
  }
}



function tryLoadFromStorage(id: string): Promise<string | null> {
  return new Promise(resolve => {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      chrome.storage.local.get(`icon_${id}`, (result) => {
        resolve(result[`icon_${id}`] || null)
      })
    } else {
      // 开发环境使用 localStorage
      const cached = localStorage.getItem(`icon_${id}`)
      resolve(cached)
    }
  })
}

function cacheIcon(id: string, icon: string) {
  if (typeof chrome !== 'undefined' && chrome.storage) {
    chrome.storage.local.set({ [`icon_${id}`]: icon })
  } else {
    // 开发环境使用 localStorage
    localStorage.setItem(`icon_${id}`, icon)
  }
}

// 全局菜单关闭监听
function handleGlobalMenuClose() {
  contextMenuVisible.value = false
  showContextMenuState.value = false
  window.removeEventListener('scroll', updateMenuPosition, true)
  window.removeEventListener('resize', updateMenuPosition)
}

// 加载图标的函数
const loadIcon = async () => {
  const customDefault = localStorage.getItem('defaultIcon') || fallbackIcon
  resolvedIconUrl.value = customDefault

  // 优先使用自定义图标
  if (props.bookmark.icon) {
    if (props.bookmark.icon.startsWith('data:')) {
      // 如果是 base64 数据，直接使用
      resolvedIconUrl.value = props.bookmark.icon
      cacheIcon(props.bookmark.id, props.bookmark.icon)
      return
    } else {
      // 如果是 URL，尝试获取
      try {
        const response = await fetch(props.bookmark.icon)
        const blob = await response.blob()
        const reader = new FileReader()
        reader.onloadend = () => {
          if (reader.result?.toString().startsWith('data:')) {
            resolvedIconUrl.value = reader.result as string
            cacheIcon(props.bookmark.id, reader.result as string)
          }
        }
        reader.readAsDataURL(blob)
        return
      } catch (error: any) {
        // 静默处理图标加载失败
      }
    }
  }

  // 检查缓存
  const stored = await tryLoadFromStorage(props.bookmark.id)
  if (stored) {
    resolvedIconUrl.value = stored
    return
  }

  // 尝试从网站获取高质量图标
  const url = props.bookmark.url ?? ''
  if (!url) return

  try {
    // 使用 AbortController 设置超时
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000) // 5秒超时

    const response = await fetch(url, {
      signal: controller.signal,
      mode: 'no-cors' // 避免 CORS 错误
    })
    clearTimeout(timeoutId)

    const html = await response.text()
    const iconUrl = await extractBestIconFromHtml(html, url)

    if (iconUrl && typeof chrome !== 'undefined' && chrome.runtime) {
      chrome.runtime.sendMessage({ type: 'fetchIconBase64', iconUrl }, (response) => {
        if (chrome.runtime.lastError) {
          // 处理 Chrome runtime 错误
          return
        }
        if (response?.icon?.startsWith('data:')) {
          resolvedIconUrl.value = response.icon
          cacheIcon(props.bookmark.id, response.icon)
        }
      })
    }
  } catch (error: any) {
    // 网络错误时使用默认图标
  }
}

// 监听书签变化
watch(() => props.bookmark, () => {
  loadIcon()
}, { immediate: true })

onMounted(async () => {
  // 监听全局菜单关闭事件
  document.addEventListener('close-all-context-menus', handleGlobalMenuClose)

  // 添加全局错误处理器，静默处理网络错误
  window.addEventListener('error', (event) => {
    const message = event.message || ''
    if (message.includes('ERR_NAME_NOT_RESOLVED') ||
        message.includes('ERR_BLOCKED_BY_RESPONSE') ||
        message.includes('ERR_CERT_AUTHORITY_INVALID') ||
        message.includes('404') ||
        message.includes('403') ||
        message.includes('net::')) {
      event.preventDefault()
      event.stopPropagation()
    }
  })

  // 初始加载图标
  await loadIcon()
})

onUnmounted(() => {
  document.removeEventListener('click', hideContextMenu)
  document.removeEventListener('close-all-context-menus', handleGlobalMenuClose)
})
</script>

<style scoped>
/* 使用全局主题样式，这里只定义特殊的样式 */

/* 毛玻璃卡片背景 */
.bookmark-card-glass {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 暗色主题下的毛玻璃效果 */
[data-theme="dark"] .bookmark-card-glass {
  background: rgba(30, 41, 59, 0.85);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 标签底部线条样式 */
.tag-underlines {
  z-index: 1;
  border-radius: 0 0 0.75rem 0.75rem;
  overflow: hidden;
  height: 4px;
}

/* 确保卡片内容在标签线条之上 */
.bookmark-card-glass .card-body,
.bookmark-card-glass .avatar {
  position: relative;
  z-index: 2;
}

/* 右键菜单激活状态 */
.context-menu-active {
  transform: scale(1.02);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15), 0 0 0 2px hsl(var(--p));
  transition: all 0.2s ease;
}

/* 删除倒计时状态 */
.pending-delete {
  opacity: 0.6;
  transform: scale(0.98);
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1), 0 0 0 2px hsl(var(--er));
  transition: all 0.3s ease;
}

.pending-delete::after {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, transparent 40%, rgba(239, 68, 68, 0.1) 50%, transparent 60%);
  border-radius: inherit;
  pointer-events: none;
}

/* 右键菜单样式 - 使用daisyUI样式，无需自定义样式 */

/* 书签卡片样式 - 使用daisyUI card样式 */
.bookmark-card-icon {
  /* daisyUI card样式已经提供了基础样式 */
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.bookmark-card-icon:hover {
  transform: translateY(-2px) scale(1.05);
}

.bookmark-card-list {
  /* daisyUI card样式已经提供了基础样式 */
  position: relative;
  overflow: hidden;
  transition: all 0.2s ease;
}

.bookmark-card-list:hover {
  transform: translateY(-1px) scale(1.02);
}

/* 选中状态样式 */
.bookmark-card-icon.selected,
.bookmark-card-list.selected {
  background-color: hsl(var(--p));
  border-color: hsl(var(--p));
  color: hsl(var(--pc));
}

.bookmark-card-icon.selected .text-theme,
.bookmark-card-list.selected .text-theme {
  color: hsl(var(--pc));
}

/* 右键菜单激活状态 */
.bookmark-card-icon.context-menu-active,
.bookmark-card-list.context-menu-active {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: hsl(var(--s));
}

/* 待删除状态 */
.bookmark-card-icon.pending-delete,
.bookmark-card-list.pending-delete {
  background-color: hsl(var(--er));
  border-color: hsl(var(--er));
  color: hsl(var(--erc));
  animation: pulse-delete 1s infinite;
}

@keyframes pulse-delete {
  0%, 100% {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: scale(0.98);
  }
}

/* 书签标题样式优化 */
.bookmark-title-icon {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 100%;
  line-height: 1.2;
  display: block;
}

.bookmark-title-card {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
  max-width: 100%;
  line-height: 1.3;
  display: block;
  flex: 1;
  min-width: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .bookmark-card-icon:hover,
  .bookmark-card-list:hover {
    transform: scale(1.02);
  }

  /* 移动端标题样式调整 */
  .bookmark-title-icon {
    font-size: 0.7rem;
    line-height: 1.1;
  }

  .bookmark-title-card {
    font-size: 0.875rem;
    line-height: 1.2;
  }
}

/* 固定尺寸书签卡片样式 */
.bookmark-card-square {
  width: 100px;
  height: 100px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  flex-shrink: 0;
  flex-grow: 0;
}

.bookmark-card-square .card-body {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
  overflow: hidden;
}

.bookmark-card-square .avatar {
  margin-bottom: 0.25rem;
  flex-shrink: 0;
}

.bookmark-card-square .card-title {
  margin-top: 0.25rem;
  text-align: center;
  font-size: 0.7rem;
  line-height: 1.1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
  flex-shrink: 0;
}

/* 矩形卡片样式 - 3:2 宽高比 */
.bookmark-card-rectangle {
  width: 180px;
  height: 100px;
  display: flex;
  flex-direction: row;
  overflow: hidden;
  flex-shrink: 0;
  flex-grow: 0;
}

.bookmark-card-rectangle .card-body {
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 0.75rem;
  overflow: hidden;
  min-width: 0;
}

.bookmark-card-rectangle .avatar {
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.bookmark-card-rectangle .card-title {
  text-align: left;
  font-size: 0.875rem;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .bookmark-card-square {
    width: 85px;
    height: 85px;
  }

  .bookmark-card-square .card-body {
    padding: 0.375rem;
  }

  .bookmark-card-square .card-title {
    font-size: 0.65rem;
  }

  .bookmark-card-rectangle {
    width: 160px;
    height: 110px;
  }

  .bookmark-card-rectangle .card-body {
    padding: 0.5rem;
  }

  .bookmark-card-rectangle .card-title {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .bookmark-card-square {
    width: 75px;
    height: 75px;
  }

  .bookmark-card-square .card-body {
    padding: 0.25rem;
  }

  .bookmark-card-square .card-title {
    font-size: 0.6rem;
  }

  .bookmark-card-rectangle {
    width: 140px;
    height: 100px;
  }

  .bookmark-card-rectangle .card-body {
    padding: 0.375rem;
  }

  .bookmark-card-rectangle .card-title {
    font-size: 0.75rem;
  }
}
</style>
