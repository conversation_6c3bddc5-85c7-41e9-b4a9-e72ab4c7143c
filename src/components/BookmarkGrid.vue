<template>
  <!-- 书签网格 -->
  <div
    v-if="bookmarks.length > 0"
    class="bookmark-grid"
    :class="{ 'bookmark-grid-square': cardStyle === 'square', 'bookmark-grid-rectangle': cardStyle === 'rectangle' }"
  >
    <div
      v-for="bookmark in bookmarks"
      :key="bookmark.id"
      class="bookmark-item"
    >
      <slot name="bookmark-item" :bookmark="bookmark" />
    </div>
  </div>

  <!-- 空状态 -->
  <div v-else class="empty-state text-center py-16">
    <div class="text-5xl mb-4">📚</div>
    <h3 class="text-lg font-semibold mb-2">此文件夹暂无书签</h3>
    <p class="text-sm text-base-content/70">开始添加一些书签吧</p>
  </div>
</template>

<script setup lang="ts">
import type { BookmarkWithMeta } from '../types'

interface Props {
  bookmarks: BookmarkWithMeta[]
  viewMode?: 'icon' | 'card'
  cardStyle?: 'square' | 'rectangle'
}

const props = withDefaults(defineProps<Props>(), {
  viewMode: 'icon',
  cardStyle: 'square'
})
</script>

<style scoped>
/* 书签网格 - 基础样式 */
.bookmark-grid {
  display: grid;
  gap: 1.25rem;
  width: fit-content;
  height: fit-content;
  justify-content: center;
  align-content: center;
  margin: 0 auto;
}

/* 方形卡片布局 - 6列，自动行数 */
.bookmark-grid-square {
  grid-template-columns: repeat(7, 100px);
  gap: 20px; /* 行间距 列间距 */
}

.bookmark-grid-square .bookmark-item {
  width: 100px;
  height: 100px;
  min-height: 100px;
  max-height: 100px;
  min-width: 100px;
  max-width: 100px;
}

/* 矩形卡片布局 - 4列，自动行数 */
.bookmark-grid-rectangle {
  grid-template-columns: repeat(5, 185px);
  gap: 20px; /* 行间距适中 列间距较大 */
}

.bookmark-grid-rectangle .bookmark-item {
  width: 185px;
  height: 100px;
  min-height: 100px;
  max-height: 100px;
  min-width: 185px;
  max-width: 185px;
}

/* 默认书签项样式 */
.bookmark-item {
  width: 80px;
  height: 80px;
  min-height: 80px;
  max-height: 80px;
  min-width: 80px;
  max-width: 80px;
}

/* 空状态 */
.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bookmark-grid-square {
    grid-template-columns: repeat(4, 90px);
    gap: 1.5rem;
  }

  .bookmark-grid-square .bookmark-item {
    width: 90px;
    height: 90px;
    min-height: 90px;
    max-height: 90px;
    min-width: 90px;
    max-width: 90px;
  }

  .bookmark-grid-rectangle {
    grid-template-columns: repeat(2, 160px);
    gap: 1rem 1.5rem;
  }

  .bookmark-grid-rectangle .bookmark-item {
    width: 160px;
    height: 110px;
    min-height: 110px;
    max-height: 110px;
    min-width: 160px;
    max-width: 160px;
  }
}

@media (max-width: 480px) {
  .bookmark-grid-square {
    grid-template-columns: repeat(3, 80px);
    gap: 1rem;
  }

  .bookmark-grid-square .bookmark-item {
    width: 80px;
    height: 80px;
    min-height: 80px;
    max-height: 80px;
    min-width: 80px;
    max-width: 80px;
  }

  .bookmark-grid-rectangle {
    grid-template-columns: repeat(1, 140px);
    gap: 0.75rem 1rem;
  }

  .bookmark-grid-rectangle .bookmark-item {
    width: 140px;
    height: 100px;
    min-height: 100px;
    max-height: 100px;
    min-width: 140px;
    max-width: 140px;
  }
}
</style>
