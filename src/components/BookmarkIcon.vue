<!--
  书签图标组件
  支持多级fallback策略、缓存、加载状态
-->

<template>
  <div 
    class="bookmark-icon"
    :class="[sizeClass, { 'icon-loading': isLoading }]"
  >
    <!-- 加载状态 -->
    <div v-if="isLoading" class="icon-skeleton">
      <div class="skeleton-animation"></div>
    </div>
    
    <!-- 图标显示 -->
    <img
      v-else-if="iconUrl"
      :src="iconUrl"
      :alt="alt"
      class="icon-image"
      @load="handleLoad"
      @error="handleError"
    />
    
    <!-- 自定义图标数据 -->
    <img
      v-else-if="iconData"
      :src="iconData"
      :alt="alt"
      class="icon-image"
      @load="handleLoad"
      @error="handleError"
    />
    
    <!-- 默认图标 -->
    <div v-else class="icon-fallback">
      <span class="fallback-text">{{ fallbackText }}</span>
    </div>
    
    <!-- 来源标识 -->
    <div v-if="showSource && iconSource" class="icon-source">
      <span class="source-badge" :class="`source-${iconSource}`">
        {{ sourceLabels[iconSource] }}
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { IconService, type IconResult } from '../services/iconService'

// Props
interface Props {
  url: string
  alt?: string
  size?: 'sm' | 'md' | 'lg' | 'xl'
  showSource?: boolean
  customIcon?: string
  lazy?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  alt: '书签图标',
  size: 'md',
  showSource: false,
  lazy: false
})

// 响应式状态
const isLoading = ref(false)
const iconUrl = ref<string>()
const iconData = ref<string>()
const iconSource = ref<IconResult['source']>()
const hasError = ref(false)

// 计算属性
const sizeClass = computed(() => `icon-${props.size}`)

const fallbackText = computed(() => {
  try {
    const domain = new URL(props.url).hostname.replace(/^www\./, '')
    return domain.charAt(0).toUpperCase()
  } catch {
    return '🔖'
  }
})

const sourceLabels: Record<IconResult['source'], string> = {
  cache: '缓存',
  favicon: '网站',
  google: 'Google',
  duckduckgo: 'DDG',
  fallback: '默认'
}

// 方法
const loadIcon = async () => {
  if (!props.url || hasError.value) return

  // 如果有自定义图标，优先使用
  if (props.customIcon) {
    if (props.customIcon.startsWith('data:')) {
      iconData.value = props.customIcon
    } else {
      iconUrl.value = props.customIcon
    }
    iconSource.value = 'cache'
    return
  }

  isLoading.value = true
  
  try {
    const result = await IconService.getIcon(props.url)
    
    if (result.data) {
      iconData.value = result.data
    } else if (result.url) {
      iconUrl.value = result.url
    }
    
    iconSource.value = result.source
    
  } catch (error) {
    console.warn('图标加载失败:', error)
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

const handleLoad = () => {
  // 图标加载成功
}

const handleError = () => {
  // 图标加载失败，清除当前图标
  iconUrl.value = undefined
  iconData.value = undefined
  hasError.value = true
}

const retryLoad = () => {
  hasError.value = false
  iconUrl.value = undefined
  iconData.value = undefined
  loadIcon()
}

// 监听URL变化
watch(() => props.url, () => {
  hasError.value = false
  iconUrl.value = undefined
  iconData.value = undefined
  iconSource.value = undefined
  loadIcon()
}, { immediate: !props.lazy })

// 监听自定义图标变化
watch(() => props.customIcon, () => {
  if (props.customIcon) {
    hasError.value = false
    loadIcon()
  }
})

// 生命周期
onMounted(() => {
  if (!props.lazy) {
    loadIcon()
  }
})

// 暴露方法给父组件
defineExpose({
  retryLoad,
  loadIcon
})
</script>

<style scoped>
.bookmark-icon {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  overflow: hidden;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
}

/* 尺寸变体 */
.icon-sm {
  width: 1rem;
  height: 1rem;
}

.icon-md {
  width: 2rem;
  height: 2rem;
}

.icon-lg {
  width: 3rem;
  height: 3rem;
}

.icon-xl {
  width: 4rem;
  height: 4rem;
}

/* 图标图片 */
.icon-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
}

/* 默认图标 */
.icon-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.fallback-text {
  font-size: 0.75em;
  line-height: 1;
}

/* 加载骨架屏 */
.icon-skeleton {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background: #f1f5f9;
}

.skeleton-animation {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.6),
    transparent
  );
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

/* 来源标识 */
.icon-source {
  position: absolute;
  bottom: -2px;
  right: -2px;
  z-index: 1;
}

.source-badge {
  display: inline-block;
  padding: 0.125rem 0.25rem;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 0.25rem;
  color: white;
  line-height: 1;
}

.source-cache {
  background: #10b981;
}

.source-favicon {
  background: #3b82f6;
}

.source-google {
  background: #f59e0b;
}

.source-duckduckgo {
  background: #8b5cf6;
}

.source-fallback {
  background: #6b7280;
}

/* 加载状态 */
.icon-loading {
  pointer-events: none;
}

/* 悬停效果 */
.bookmark-icon:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

/* 暗色主题 */
[data-theme="dark"] .bookmark-icon {
  background: #334155;
  border-color: #475569;
}

[data-theme="dark"] .icon-skeleton {
  background: #475569;
}

[data-theme="dark"] .skeleton-animation {
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
}

/* 响应式调整 */
@media (max-width: 768px) {
  .icon-source {
    display: none;
  }
}
</style>
