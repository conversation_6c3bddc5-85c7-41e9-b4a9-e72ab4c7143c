<template>
  <!-- 模态框背景 -->
  <div
    v-if="isOpen"
    class="modal"
    @click="cancel"
  >
    <div class="modal-box" style="width: 24rem; max-width: 20rem;" @click.stop>
      <!-- 关闭按钮 -->
      <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" @click="cancel">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>

      <!-- 标题 -->
      <h3 class="font-bold text-lg mb-4 text-gray-900">{{ title }}</h3>

      <!-- 图标 -->
      <div class="flex justify-center mb-6">
        <div
          class="w-16 h-16 rounded-full flex items-center justify-center"
          :class="iconClass"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
            <path v-if="type === 'warning'" stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z" />
            <path v-else-if="type === 'danger'" stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
            <path v-else stroke-linecap="round" stroke-linejoin="round" d="M9.879 7.519c1.171-1.025 3.071-1.025 4.242 0 1.172 1.025 1.172 2.687 0 3.712-.203.179-.43.326-.67.442-.745.361-1.45.999-1.45 1.827v.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 5.25h.008v.008H12v-.008Z" />
          </svg>
        </div>
      </div>

      <!-- 内容 -->
      <p class="text-center mb-6 text-gray-700">{{ message }}</p>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <button class="btn" @click="cancel">
          {{ cancelText }}
        </button>
        <button
          class="btn"
          :class="confirmButtonClass"
          @click="confirm"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
  isOpen: boolean
  type?: 'info' | 'warning' | 'danger'
  title: string
  message: string
  confirmText?: string
  cancelText?: string
}>()

const emit = defineEmits<{
  (e: 'confirm'): void
  (e: 'cancel'): void
}>()

// 图标样式
const iconClass = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'bg-yellow-100 text-yellow-600'
    case 'danger':
      return 'bg-red-100 text-red-600'
    default:
      return 'bg-blue-100 text-blue-600'
  }
})

// 确认按钮样式
const confirmButtonClass = computed(() => {
  switch (props.type) {
    case 'warning':
      return 'btn-secondary'
    case 'danger':
      return 'btn-secondary'
    default:
      return 'btn-primary'
  }
})

const confirm = () => {
  emit('confirm')
}

const cancel = () => {
  emit('cancel')
}
</script>


