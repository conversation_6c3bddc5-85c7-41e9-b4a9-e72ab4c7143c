<!--
  数据管理组件
  支持书签数据的导入导出、备份恢复
-->

<template>
  <div class="data-manager">
    <!-- 数据统计 -->
    <div class="stats-section">
      <h3 class="section-title">数据统计</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value">{{ stats.bookmarksCount }}</div>
          <div class="stat-label">书签总数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.foldersCount }}</div>
          <div class="stat-label">文件夹数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ stats.tagsCount }}</div>
          <div class="stat-label">标签数</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ formatSize(stats.dataSize) }}</div>
          <div class="stat-label">数据大小</div>
        </div>
      </div>
    </div>

    <!-- 导出功能 -->
    <div class="export-section">
      <h3 class="section-title">数据导出</h3>
      <div class="export-options">
        <div class="option-group">
          <h4 class="option-title">导出格式</h4>
          <div class="radio-group">
            <label class="radio-option">
              <input
                v-model="exportFormat"
                type="radio"
                value="json"
                class="radio-input"
              />
              <span class="radio-label">JSON 格式</span>
              <span class="radio-description">包含完整的书签数据和元数据</span>
            </label>
            
            <label class="radio-option">
              <input
                v-model="exportFormat"
                type="radio"
                value="html"
                class="radio-input"
              />
              <span class="radio-label">HTML 格式</span>
              <span class="radio-description">标准浏览器书签格式</span>
            </label>
            
            <label class="radio-option">
              <input
                v-model="exportFormat"
                type="radio"
                value="csv"
                class="radio-input"
              />
              <span class="radio-label">CSV 格式</span>
              <span class="radio-description">表格数据，便于分析</span>
            </label>
          </div>
        </div>

        <div class="option-group">
          <h4 class="option-title">导出内容</h4>
          <div class="checkbox-group">
            <label class="checkbox-option">
              <input
                v-model="exportOptions.includeBookmarks"
                type="checkbox"
                class="checkbox-input"
              />
              <span class="checkbox-label">书签数据</span>
            </label>
            
            <label class="checkbox-option">
              <input
                v-model="exportOptions.includeTags"
                type="checkbox"
                class="checkbox-input"
              />
              <span class="checkbox-label">标签数据</span>
            </label>
            
            <label class="checkbox-option">
              <input
                v-model="exportOptions.includeSettings"
                type="checkbox"
                class="checkbox-input"
              />
              <span class="checkbox-label">应用设置</span>
            </label>
            
            <label class="checkbox-option">
              <input
                v-model="exportOptions.includeIcons"
                type="checkbox"
                class="checkbox-input"
              />
              <span class="checkbox-label">图标缓存</span>
            </label>
          </div>
        </div>

        <div class="export-actions">
          <button
            class="btn btn-primary"
            @click="exportData"
            :disabled="isExporting"
          >
            {{ isExporting ? '导出中...' : '导出数据' }}
          </button>
        </div>
      </div>
    </div>

    <!-- 导入功能 -->
    <div class="import-section">
      <h3 class="section-title">数据导入</h3>
      <div class="import-options">
        <div class="file-upload">
          <input
            ref="fileInputRef"
            type="file"
            accept=".json,.html,.csv"
            @change="handleFileSelect"
            class="file-input"
          />
          
          <div
            class="upload-area"
            :class="{ 'upload-dragover': isDragOver }"
            @click="triggerFileSelect"
            @dragover.prevent="handleDragOver"
            @dragleave.prevent="handleDragLeave"
            @drop.prevent="handleFileDrop"
          >
            <div class="upload-icon">📁</div>
            <div class="upload-text">
              <div class="upload-title">选择文件或拖拽到此处</div>
              <div class="upload-description">
                支持 JSON、HTML、CSV 格式
              </div>
            </div>
          </div>
        </div>

        <div v-if="selectedFile" class="file-info">
          <div class="file-details">
            <div class="file-name">{{ selectedFile.name }}</div>
            <div class="file-size">{{ formatSize(selectedFile.size) }}</div>
            <div class="file-type">{{ getFileType(selectedFile) }}</div>
          </div>
          
          <div class="import-options-group">
            <h4 class="option-title">导入选项</h4>
            <div class="checkbox-group">
              <label class="checkbox-option">
                <input
                  v-model="importOptions.mergeMode"
                  type="checkbox"
                  class="checkbox-input"
                />
                <span class="checkbox-label">合并模式</span>
                <span class="checkbox-description">保留现有数据，仅添加新数据</span>
              </label>
              
              <label class="checkbox-option">
                <input
                  v-model="importOptions.createBackup"
                  type="checkbox"
                  class="checkbox-input"
                />
                <span class="checkbox-label">创建备份</span>
                <span class="checkbox-description">导入前自动备份当前数据</span>
              </label>
            </div>
          </div>

          <div class="import-actions">
            <button
              class="btn btn-outline"
              @click="clearSelectedFile"
            >
              取消
            </button>
            
            <button
              class="btn btn-primary"
              @click="importData"
              :disabled="isImporting"
            >
              {{ isImporting ? '导入中...' : '开始导入' }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 备份管理 -->
    <div class="backup-section">
      <h3 class="section-title">自动备份</h3>
      <div class="backup-list">
        <div
          v-for="backup in backups"
          :key="backup.id"
          class="backup-item"
        >
          <div class="backup-info">
            <div class="backup-name">{{ backup.name }}</div>
            <div class="backup-date">{{ formatDate(backup.createdAt) }}</div>
            <div class="backup-size">{{ formatSize(backup.size) }}</div>
          </div>
          
          <div class="backup-actions">
            <button
              class="btn btn-sm btn-outline"
              @click="restoreBackup(backup)"
            >
              恢复
            </button>
            
            <button
              class="btn btn-sm btn-ghost"
              @click="downloadBackup(backup)"
            >
              下载
            </button>
            
            <button
              class="btn btn-sm btn-ghost text-red-500"
              @click="deleteBackup(backup)"
            >
              删除
            </button>
          </div>
        </div>
        
        <div v-if="backups.length === 0" class="empty-backups">
          <div class="empty-icon">💾</div>
          <div class="empty-text">暂无自动备份</div>
        </div>
      </div>
    </div>

    <!-- 进度指示器 -->
    <div v-if="showProgress" class="progress-overlay">
      <div class="progress-modal">
        <div class="progress-content">
          <div class="progress-title">{{ progressTitle }}</div>
          <div class="progress-bar">
            <div
              class="progress-fill"
              :style="{ width: `${progressPercentage}%` }"
            ></div>
          </div>
          <div class="progress-text">{{ progressText }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { StorageService } from '../services/storageService'

// 响应式状态
const stats = ref({
  bookmarksCount: 0,
  foldersCount: 0,
  tagsCount: 0,
  dataSize: 0
})

const exportFormat = ref<'json' | 'html' | 'csv'>('json')
const exportOptions = ref({
  includeBookmarks: true,
  includeTags: true,
  includeSettings: true,
  includeIcons: false
})

const isExporting = ref(false)
const isImporting = ref(false)
const isDragOver = ref(false)

const selectedFile = ref<File | null>(null)
const fileInputRef = ref<HTMLInputElement>()

const importOptions = ref({
  mergeMode: true,
  createBackup: true
})

const backups = ref<Array<{
  id: string
  name: string
  createdAt: number
  size: number
  data: any
}>>([])

const showProgress = ref(false)
const progressTitle = ref('')
const progressText = ref('')
const progressPercentage = ref(0)

// 方法
const formatSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const formatDate = (timestamp: number): string => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

const getFileType = (file: File): string => {
  const extension = file.name.split('.').pop()?.toLowerCase()
  switch (extension) {
    case 'json':
      return 'JSON 数据'
    case 'html':
      return 'HTML 书签'
    case 'csv':
      return 'CSV 表格'
    default:
      return '未知格式'
  }
}

const updateStats = async () => {
  try {
    const storageStats = await StorageService.getStorageStats()
    stats.value = {
      bookmarksCount: storageStats.databaseStats.bookmarkMetaCount,
      foldersCount: 0, // 需要从书签数据中计算
      tagsCount: storageStats.databaseStats.tagsCount,
      dataSize: storageStats.syncSize + storageStats.databaseStats.dbSize
    }
  } catch (error) {
    console.error('获取数据统计失败:', error)
  }
}

const exportData = async () => {
  if (isExporting.value) return
  
  isExporting.value = true
  showProgress.value = true
  progressTitle.value = '导出数据'
  progressText.value = '正在收集数据...'
  progressPercentage.value = 0
  
  try {
    // 获取数据
    progressPercentage.value = 25
    progressText.value = '正在导出书签数据...'
    
    const data = await StorageService.exportAllData()
    
    progressPercentage.value = 50
    progressText.value = '正在格式化数据...'
    
    let exportContent: string
    let filename: string
    let mimeType: string
    
    switch (exportFormat.value) {
      case 'json':
        exportContent = JSON.stringify(data, null, 2)
        filename = `bookmarks-${Date.now()}.json`
        mimeType = 'application/json'
        break
        
      case 'html':
        exportContent = generateHTMLBookmarks(data)
        filename = `bookmarks-${Date.now()}.html`
        mimeType = 'text/html'
        break
        
      case 'csv':
        exportContent = generateCSVBookmarks(data)
        filename = `bookmarks-${Date.now()}.csv`
        mimeType = 'text/csv'
        break
        
      default:
        throw new Error('不支持的导出格式')
    }
    
    progressPercentage.value = 75
    progressText.value = '正在生成文件...'
    
    // 下载文件
    const blob = new Blob([exportContent], { type: mimeType })
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    link.click()
    
    URL.revokeObjectURL(url)
    
    progressPercentage.value = 100
    progressText.value = '导出完成！'
    
    setTimeout(() => {
      showProgress.value = false
    }, 1000)
    
  } catch (error) {
    console.error('导出数据失败:', error)
    alert('导出失败：' + (error as Error).message)
    showProgress.value = false
  } finally {
    isExporting.value = false
  }
}

const generateHTMLBookmarks = (data: any): string => {
  // 生成标准HTML书签格式
  return `<!DOCTYPE NETSCAPE-Bookmark-file-1>
<META HTTP-EQUIV="Content-Type" CONTENT="text/html; charset=UTF-8">
<TITLE>Bookmarks</TITLE>
<H1>Bookmarks</H1>
<DL><p>
<!-- 这里需要实现HTML书签格式生成 -->
</DL><p>`
}

const generateCSVBookmarks = (data: any): string => {
  // 生成CSV格式
  const headers = ['标题', 'URL', '标签', '创建时间']
  const rows = [headers.join(',')]
  
  // 这里需要实现CSV数据生成
  
  return rows.join('\n')
}

const triggerFileSelect = () => {
  fileInputRef.value?.click()
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  if (file) {
    selectedFile.value = file
  }
}

const handleDragOver = () => {
  isDragOver.value = true
}

const handleDragLeave = () => {
  isDragOver.value = false
}

const handleFileDrop = (event: DragEvent) => {
  isDragOver.value = false
  const file = event.dataTransfer?.files[0]
  if (file) {
    selectedFile.value = file
  }
}

const clearSelectedFile = () => {
  selectedFile.value = null
  if (fileInputRef.value) {
    fileInputRef.value.value = ''
  }
}

const importData = async () => {
  if (!selectedFile.value || isImporting.value) return
  
  isImporting.value = true
  showProgress.value = true
  progressTitle.value = '导入数据'
  progressText.value = '正在读取文件...'
  progressPercentage.value = 0
  
  try {
    // 创建备份
    if (importOptions.value.createBackup) {
      progressText.value = '正在创建备份...'
      await createBackup()
      progressPercentage.value = 25
    }
    
    // 读取文件
    progressText.value = '正在解析文件...'
    const content = await readFile(selectedFile.value)
    progressPercentage.value = 50
    
    // 解析数据
    progressText.value = '正在导入数据...'
    const data = parseImportData(content, selectedFile.value)
    progressPercentage.value = 75
    
    // 导入数据
    await StorageService.importAllData(data)
    progressPercentage.value = 100
    progressText.value = '导入完成！'
    
    // 刷新统计
    await updateStats()
    
    setTimeout(() => {
      showProgress.value = false
      clearSelectedFile()
    }, 1000)
    
  } catch (error) {
    console.error('导入数据失败:', error)
    alert('导入失败：' + (error as Error).message)
    showProgress.value = false
  } finally {
    isImporting.value = false
  }
}

const readFile = (file: File): Promise<string> => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = () => resolve(reader.result as string)
    reader.onerror = () => reject(new Error('文件读取失败'))
    reader.readAsText(file)
  })
}

const parseImportData = (content: string, file: File): any => {
  const extension = file.name.split('.').pop()?.toLowerCase()
  
  switch (extension) {
    case 'json':
      return JSON.parse(content)
    case 'html':
      // 解析HTML书签格式
      throw new Error('HTML格式导入暂未实现')
    case 'csv':
      // 解析CSV格式
      throw new Error('CSV格式导入暂未实现')
    default:
      throw new Error('不支持的文件格式')
  }
}

const createBackup = async () => {
  const data = await StorageService.exportAllData()
  const backup = {
    id: Date.now().toString(),
    name: `自动备份 ${formatDate(Date.now())}`,
    createdAt: Date.now(),
    size: JSON.stringify(data).length,
    data
  }
  
  backups.value.unshift(backup)
  
  // 限制备份数量
  if (backups.value.length > 10) {
    backups.value = backups.value.slice(0, 10)
  }
}

const restoreBackup = async (backup: any) => {
  if (confirm(`确定要恢复备份"${backup.name}"吗？这将覆盖当前所有数据。`)) {
    try {
      await StorageService.importAllData(backup.data)
      await updateStats()
      alert('备份恢复成功！')
    } catch (error) {
      console.error('恢复备份失败:', error)
      alert('恢复失败：' + (error as Error).message)
    }
  }
}

const downloadBackup = (backup: any) => {
  const content = JSON.stringify(backup.data, null, 2)
  const blob = new Blob([content], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${backup.name}.json`
  link.click()
  URL.revokeObjectURL(url)
}

const deleteBackup = (backup: any) => {
  if (confirm(`确定要删除备份"${backup.name}"吗？`)) {
    const index = backups.value.findIndex(b => b.id === backup.id)
    if (index !== -1) {
      backups.value.splice(index, 1)
    }
  }
}

// 生命周期
onMounted(() => {
  updateStats()
})
</script>

<style scoped>
/* 样式实现省略，与之前的组件样式类似 */
.data-manager {
  padding: 1.5rem;
  max-width: 1000px;
  margin: 0 auto;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
}

/* 其他样式... */
</style>
