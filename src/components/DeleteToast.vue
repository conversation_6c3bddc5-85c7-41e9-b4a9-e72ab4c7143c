<template>
  <!-- 使用 daisyUI toast 和 alert 组件 -->
  <div v-if="isVisible" class="toast toast-bottom toast-center z-50">
    <div class="alert alert-warning shadow-lg">
    <div class="flex items-center gap-3">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5 h-5">
        <path stroke-linecap="round" stroke-linejoin="round" d="M14.74 9l-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 01-2.244 2.077H8.084a2.25 2.25 0 01-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 00-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 013.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 00-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 00-7.5 0" />
      </svg>
      <span>{{ message }}</span>
    </div>
    
    <div class="flex items-center gap-3">
      <!-- 倒计时 - 使用 daisyUI radial-progress -->
      <div class="radial-progress text-primary" :style="`--value:${(countdown / (duration || 5)) * 100}; --size:2rem; --thickness: 2px;`">
        <span class="text-xs font-bold">{{ countdown }}</span>
      </div>
      
      <!-- 撤销按钮 -->
      <button class="btn btn-primary btn-sm" @click="undo">
        撤销
      </button>
    </div>
  </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps<{
  isVisible: boolean
  message: string
  duration?: number
}>()

const emit = defineEmits<{
  (e: 'undo'): void
  (e: 'timeout'): void
}>()

const countdown = ref(props.duration || 5)

let timer: NodeJS.Timeout | null = null

const startCountdown = () => {
  if (timer) clearInterval(timer)
  
  countdown.value = props.duration || 5
  timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      emit('timeout')
      if (timer) clearInterval(timer)
    }
  }, 1000)
}

const undo = () => {
  if (timer) clearInterval(timer)
  emit('undo')
}

onMounted(() => {
  if (props.isVisible) {
    startCountdown()
  }
})

onUnmounted(() => {
  if (timer) clearInterval(timer)
})

// 监听可见性变化
watch(() => props.isVisible, (visible) => {
  if (visible) {
    startCountdown()
  } else {
    if (timer) clearInterval(timer)
  }
})
</script>

<style scoped>
/* 使用daisyUI样式，保留必要的动画 */
.toast {
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
