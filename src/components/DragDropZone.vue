<template>
  <div
    class="drag-drop-zone"
    @dragover="handleDragOver"
    @drop="handleDrop"
    @dragenter="handleDragEnter"
    @dragleave="handleDragLeave"
  >

    
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  groupId: string
  bookmarks: any[]
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'drop-in-zone', data: {
    draggedBookmarkId: string
    targetGroupId: string
    targetIndex: number
    dropPosition: 'before' | 'after' | 'first' | 'last'
  }): void
}>()

const isDragOver = ref(false)

function handleDragOver(e: DragEvent) {
  e.preventDefault()
  e.dataTransfer!.dropEffect = 'move'
  isDragOver.value = true
}

function handleDragEnter(e: DragEvent) {
  e.preventDefault()
  isDragOver.value = true
}

function handleDragLeave(e: DragEvent) {
  e.preventDefault()

  // 检查是否真的离开了容器
  const rect = (e.currentTarget as HTMLElement).getBoundingClientRect()
  const x = e.clientX
  const y = e.clientY

  if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
    isDragOver.value = false
  }
}

function handleDrop(e: DragEvent) {
  e.preventDefault()
  isDragOver.value = false

  const draggedBookmarkId = e.dataTransfer!.getData('bookmarkId')
  if (!draggedBookmarkId) return

  // 智能计算插入位置
  const dropPosition = calculateDropPosition(e)

  emit('drop-in-zone', {
    draggedBookmarkId,
    targetGroupId: props.groupId,
    targetIndex: dropPosition.index,
    dropPosition: dropPosition.position
  })
}

// 计算拖拽位置
function calculateDropPosition(e: DragEvent) {
  const container = e.currentTarget as HTMLElement
  const bookmarkElements = container.querySelectorAll('[data-bookmark-id]')

  if (bookmarkElements.length === 0) {
    return { index: 0, position: 'first' as const }
  }

  const mouseY = e.clientY
  let insertIndex = props.bookmarks.length
  let insertPosition: 'before' | 'after' | 'first' | 'last' = 'last'

  // 遍历所有书签元素，找到最接近的插入位置
  for (let i = 0; i < bookmarkElements.length; i++) {
    const element = bookmarkElements[i] as HTMLElement
    const rect = element.getBoundingClientRect()
    const centerY = rect.top + rect.height / 2

    if (mouseY < centerY) {
      // 鼠标在当前书签上方，插入到这个书签之前
      insertIndex = i
      insertPosition = 'before'
      break
    } else if (i === bookmarkElements.length - 1) {
      // 这是最后一个书签，且鼠标在其下方，插入到末尾
      insertIndex = i + 1
      insertPosition = 'after'
    }
  }

  return { index: insertIndex, position: insertPosition }
}


</script>

<style scoped>
.drag-drop-zone {
  position: relative;
  min-height: 100px;
}
</style>
