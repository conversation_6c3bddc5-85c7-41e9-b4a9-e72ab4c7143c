<!--
  可拖拽书签网格组件
  支持书签拖拽排序和文件夹移动
-->

<template>
  <div class="draggable-bookmark-grid">
    <!-- 拖拽容器 -->
    <VueDraggable
      v-model="localBookmarks"
      :animation="200"
      :ghost-class="'bookmark-ghost'"
      :chosen-class="'bookmark-chosen'"
      :drag-class="'bookmark-drag'"
      :group="{ name: 'bookmarks', pull: true, put: true }"
      :sort="true"
      :disabled="isDragDisabled"
      @start="onDragStart"
      @end="onDragEnd"
      @add="onDragAdd"
      @update="onDragUpdate"
      @remove="onDragRemove"
      class="bookmark-grid"
      :class="[`grid-${viewMode}`, { 'grid-dragging': isDragging }]"
    >
      <template #item="{ element: bookmark, index }">
        <div
          class="bookmark-wrapper"
          :class="{
            'bookmark-selected': selectedBookmarkIds.includes(bookmark.id),
            'bookmark-dragging': draggedBookmark?.id === bookmark.id
          }"
          @click="handleBookmarkClick(bookmark, $event)"
          @contextmenu="handleBookmarkRightClick($event, bookmark)"
        >
          <!-- 书签卡片 -->
          <BookmarkCard
            :bookmark="bookmark"
            :view-mode="viewMode"
            :is-selected="selectedBookmarkIds.includes(bookmark.id)"
            :is-dragging="draggedBookmark?.id === bookmark.id"
            @select="handleBookmarkSelect"
            @edit="handleBookmarkEdit"
            @delete="handleBookmarkDelete"
          />

          <!-- 拖拽指示器 -->
          <div v-if="showDropIndicator && dropPosition.index === index" class="drop-indicator">
            <div class="drop-line" :class="`drop-${dropPosition.position}`"></div>
          </div>
        </div>
      </template>
    </VueDraggable>

    <!-- 拖拽预览 -->
    <div v-if="isDragging && draggedBookmark" class="drag-preview">
      <BookmarkCard
        :bookmark="draggedBookmark"
        :view-mode="viewMode"
        :is-dragging="true"
      />
    </div>

    <!-- 文件夹拖拽目标 -->
    <div v-if="isDragging" class="folder-drop-zones">
      <div
        v-for="folder in availableFolders"
        :key="folder.id"
        class="folder-drop-zone"
        :class="{ 'drop-zone-active': dropTargetFolder?.id === folder.id }"
        @dragover.prevent="handleFolderDragOver(folder)"
        @dragleave="handleFolderDragLeave"
        @drop="handleFolderDrop(folder)"
      >
        <div class="folder-icon">📁</div>
        <div class="folder-name">{{ folder.title }}</div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { VueDraggable } from 'vue-draggable-plus'
import BookmarkCard from './BookmarkCard.vue'
import type { BookmarkWithMeta, BookmarkGroup } from '../types'

// Props
interface Props {
  bookmarks: BookmarkWithMeta[]
  viewMode: 'card' | 'list'
  selectedBookmarkIds: string[]
  availableFolders: BookmarkGroup[]
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false
})

// Emits
const emit = defineEmits<{
  'update:bookmarks': [bookmarks: BookmarkWithMeta[]]
  'bookmark-click': [bookmark: BookmarkWithMeta, event: MouseEvent]
  'bookmark-right-click': [event: MouseEvent, bookmark: BookmarkWithMeta]
  'bookmark-select': [bookmark: BookmarkWithMeta]
  'bookmark-edit': [bookmark: BookmarkWithMeta]
  'bookmark-delete': [bookmark: BookmarkWithMeta]
  'bookmark-move': [bookmark: BookmarkWithMeta, targetFolderId: string]
  'bookmarks-reorder': [bookmarks: BookmarkWithMeta[]]
}>()

// 响应式状态
const isDragging = ref(false)
const isDragDisabled = computed(() => props.disabled || props.selectedBookmarkIds.length > 1)
const draggedBookmark = ref<BookmarkWithMeta | null>(null)
const dropTargetFolder = ref<BookmarkGroup | null>(null)
const showDropIndicator = ref(false)
const dropPosition = ref({ index: -1, position: 'before' as 'before' | 'after' })

// 本地书签数据
const localBookmarks = computed({
  get: () => props.bookmarks,
  set: (value) => emit('update:bookmarks', value)
})

// 拖拽事件处理
const onDragStart = (event: any) => {
  isDragging.value = true
  draggedBookmark.value = event.item._underlying_vm_
  
  // 添加拖拽样式
  document.body.classList.add('dragging-active')
  
  // 设置拖拽数据
  if (event.originalEvent?.dataTransfer) {
    event.originalEvent.dataTransfer.effectAllowed = 'move'
    event.originalEvent.dataTransfer.setData('text/plain', draggedBookmark.value?.title || '')
  }
}

const onDragEnd = (event: any) => {
  isDragging.value = false
  draggedBookmark.value = null
  dropTargetFolder.value = null
  showDropIndicator.value = false
  
  // 移除拖拽样式
  document.body.classList.remove('dragging-active')
  
  // 清理拖拽状态
  nextTick(() => {
    dropPosition.value = { index: -1, position: 'before' }
  })
}

const onDragAdd = (event: any) => {
  // 处理从其他容器拖入的书签
  console.log('书签拖入:', event)
}

const onDragUpdate = (event: any) => {
  // 处理书签重新排序
  emit('bookmarks-reorder', localBookmarks.value)
}

const onDragRemove = (event: any) => {
  // 处理书签被拖出
  console.log('书签拖出:', event)
}

// 文件夹拖拽处理
const handleFolderDragOver = (folder: BookmarkGroup) => {
  if (draggedBookmark.value) {
    dropTargetFolder.value = folder
  }
}

const handleFolderDragLeave = () => {
  dropTargetFolder.value = null
}

const handleFolderDrop = (folder: BookmarkGroup) => {
  if (draggedBookmark.value) {
    emit('bookmark-move', draggedBookmark.value, folder.id)
    dropTargetFolder.value = null
  }
}

// 书签事件处理
const handleBookmarkClick = (bookmark: BookmarkWithMeta, event: MouseEvent) => {
  emit('bookmark-click', bookmark, event)
}

const handleBookmarkRightClick = (event: MouseEvent, bookmark: BookmarkWithMeta) => {
  emit('bookmark-right-click', event, bookmark)
}

const handleBookmarkSelect = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-select', bookmark)
}

const handleBookmarkEdit = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-edit', bookmark)
}

const handleBookmarkDelete = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-delete', bookmark)
}

// 监听拖拽状态变化
watch(isDragging, (dragging) => {
  if (dragging) {
    // 显示文件夹拖拽目标
    showDropIndicator.value = true
  } else {
    // 隐藏拖拽指示器
    setTimeout(() => {
      showDropIndicator.value = false
    }, 200)
  }
})
</script>

<style scoped>
.draggable-bookmark-grid {
  position: relative;
  height: 100%;
}

.bookmark-grid {
  display: grid;
  gap: 1rem;
  padding: 1rem;
  min-height: 100%;
}

.grid-card {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
}

.grid-list {
  grid-template-columns: 1fr;
  gap: 0.5rem;
}

.grid-dragging {
  user-select: none;
}

.bookmark-wrapper {
  position: relative;
  transition: all 0.2s ease;
}

.bookmark-wrapper:not(.bookmark-dragging):hover {
  transform: translateY(-2px);
}

.bookmark-selected {
  ring: 2px solid #3b82f6;
  ring-opacity: 50%;
}

.bookmark-dragging {
  opacity: 0.5;
  transform: rotate(5deg) scale(1.05);
}

/* 拖拽状态样式 */
.bookmark-ghost {
  opacity: 0.3;
  background: #e5e7eb;
  border: 2px dashed #9ca3af;
}

.bookmark-chosen {
  transform: rotate(5deg) scale(1.05);
  z-index: 1000;
}

.bookmark-drag {
  transform: rotate(10deg) scale(1.1);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  z-index: 1001;
}

/* 拖拽指示器 */
.drop-indicator {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.drop-line {
  position: absolute;
  background: #3b82f6;
  border-radius: 2px;
}

.drop-before {
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
}

.drop-after {
  bottom: -2px;
  left: 0;
  right: 0;
  height: 4px;
}

/* 拖拽预览 */
.drag-preview {
  position: fixed;
  top: -1000px;
  left: -1000px;
  pointer-events: none;
  z-index: 9999;
  opacity: 0.8;
  transform: rotate(5deg) scale(0.9);
}

/* 文件夹拖拽目标 */
.folder-drop-zones {
  position: fixed;
  top: 50%;
  right: 2rem;
  transform: translateY(-50%);
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  z-index: 1000;
}

.folder-drop-zone {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: white;
  border: 2px dashed #d1d5db;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
  min-width: 150px;
}

.folder-drop-zone:hover,
.drop-zone-active {
  border-color: #3b82f6;
  background: #eff6ff;
  transform: scale(1.05);
}

.folder-icon {
  font-size: 1.25rem;
}

.folder-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 全局拖拽状态 */
:global(.dragging-active) {
  cursor: grabbing !important;
}

:global(.dragging-active *) {
  cursor: grabbing !important;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .grid-card {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .grid-card {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }
  
  .folder-drop-zones {
    right: 1rem;
  }
  
  .folder-drop-zone {
    min-width: 120px;
    padding: 0.5rem 0.75rem;
  }
}

@media (max-width: 480px) {
  .grid-card {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
  
  .bookmark-grid {
    padding: 0.75rem;
    gap: 0.75rem;
  }
}

/* 暗色主题 */
[data-theme="dark"] .folder-drop-zone {
  background: #1f2937;
  border-color: #4b5563;
}

[data-theme="dark"] .folder-drop-zone:hover,
[data-theme="dark"] .drop-zone-active {
  border-color: #3b82f6;
  background: #1e40af;
}

[data-theme="dark"] .folder-name {
  color: #e5e7eb;
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .bookmark-wrapper,
  .folder-drop-zone {
    transition: none;
  }
  
  .bookmark-wrapper:not(.bookmark-dragging):hover {
    transform: none;
  }
}
</style>
