<template>
  <div 
    class="draggable-container"
    :class="containerClass"
    @dragover="handleDragOver"
    @drop="handleDrop"
  >
    <div
      v-for="(item, index) in localItems"
      :key="item.id"
      class="draggable-item"
      :class="{ 'dragging': draggedIndex === index }"
      :draggable="true"
      @dragstart="handleDragStart($event, index)"
      @dragend="handleDragEnd"
    >
      <!-- 拖拽指示器 - 前面 -->
      <div
        v-if="dropIndicator.show && dropIndicator.index === index && dropIndicator.position === 'before'"
        class="drop-indicator before"
      ></div>
      
      <!-- 书签内容插槽 -->
      <slot :item="item" :index="index"></slot>
      
      <!-- 拖拽指示器 - 后面 -->
      <div
        v-if="dropIndicator.show && dropIndicator.index === index && dropIndicator.position === 'after'"
        class="drop-indicator after"
      ></div>
    </div>
    
    <!-- 末尾指示器 -->
    <div
      v-if="dropIndicator.show && dropIndicator.index === localItems.length"
      class="drop-indicator end"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

interface DraggableItem {
  id: string
  [key: string]: any
}

interface Props {
  items: DraggableItem[]
  containerClass?: string
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'update:items', items: DraggableItem[]): void
  (e: 'change', data: { oldIndex: number, newIndex: number, item: DraggableItem }): void
}>()

// 本地数据副本
const localItems = ref([...props.items])

// 监听外部数据变化
watch(() => props.items, (newItems) => {
  localItems.value = [...newItems]
}, { deep: true })

// 拖拽状态
const draggedIndex = ref<number | null>(null)
const draggedItem = ref<DraggableItem | null>(null)

// 拖拽指示器
const dropIndicator = ref({
  show: false,
  index: -1,
  position: 'before' as 'before' | 'after'
})

// 拖拽开始
function handleDragStart(event: DragEvent, index: number) {
  draggedIndex.value = index
  draggedItem.value = localItems.value[index]
  
  // 设置拖拽数据
  event.dataTransfer!.setData('text/plain', index.toString())
  event.dataTransfer!.effectAllowed = 'move'
  
  // 添加拖拽样式
  setTimeout(() => {
    if (event.target instanceof HTMLElement) {
      event.target.classList.add('dragging')
    }
  }, 0)
}

// 拖拽结束
function handleDragEnd(event: DragEvent) {
  draggedIndex.value = null
  draggedItem.value = null
  dropIndicator.value.show = false
  
  // 移除拖拽样式
  if (event.target instanceof HTMLElement) {
    event.target.classList.remove('dragging')
  }
}

// 拖拽悬停
function handleDragOver(event: DragEvent) {
  event.preventDefault()
  event.dataTransfer!.dropEffect = 'move'
  
  if (draggedIndex.value === null) return
  
  // 计算拖拽位置
  const container = event.currentTarget as HTMLElement
  const items = Array.from(container.querySelectorAll('.draggable-item'))
  const mouseY = event.clientY
  
  let targetIndex = localItems.value.length
  let position: 'before' | 'after' = 'after'
  
  for (let i = 0; i < items.length; i++) {
    const item = items[i] as HTMLElement
    const rect = item.getBoundingClientRect()
    const centerY = rect.top + rect.height / 2
    
    if (mouseY < centerY) {
      targetIndex = i
      position = 'before'
      break
    } else if (i === items.length - 1) {
      targetIndex = i
      position = 'after'
    }
  }
  
  // 更新指示器
  dropIndicator.value = {
    show: true,
    index: targetIndex,
    position
  }
}

// 拖拽放置
function handleDrop(event: DragEvent) {
  event.preventDefault()
  
  if (draggedIndex.value === null || draggedItem.value === null) return
  
  const oldIndex = draggedIndex.value
  let newIndex = dropIndicator.value.index
  
  // 调整索引
  if (dropIndicator.value.position === 'after') {
    newIndex += 1
  }
  
  // 如果拖拽的项目在目标位置之前，需要调整索引
  if (oldIndex < newIndex) {
    newIndex -= 1
  }
  
  // 确保索引有效
  newIndex = Math.max(0, Math.min(newIndex, localItems.value.length - 1))
  
  // 如果位置没有变化，不执行移动
  if (oldIndex === newIndex) {
    dropIndicator.value.show = false
    return
  }
  
  // 执行移动
  const newItems = [...localItems.value]
  const [movedItem] = newItems.splice(oldIndex, 1)
  newItems.splice(newIndex, 0, movedItem)
  
  localItems.value = newItems
  
  // 发射事件
  emit('update:items', newItems)
  emit('change', {
    oldIndex,
    newIndex,
    item: draggedItem.value
  })
  
  // 清除状态
  dropIndicator.value.show = false
}
</script>

<style scoped>
.draggable-container {
  position: relative;
}

.draggable-item {
  position: relative;
  transition: all 0.2s ease;
  cursor: grab;
}

.draggable-item:active {
  cursor: grabbing;
}

.draggable-item.dragging {
  opacity: 0.5;
  transform: rotate(2deg);
}

.drop-indicator {
  position: absolute;
  background: #3b82f6;
  border-radius: 2px;
  z-index: 10;
  animation: pulse 1s infinite;
}

.drop-indicator.before {
  top: -2px;
  left: 0;
  right: 0;
  height: 4px;
}

.drop-indicator.after {
  bottom: -2px;
  left: 0;
  right: 0;
  height: 4px;
}

.drop-indicator.end {
  bottom: -2px;
  left: 0;
  right: 0;
  height: 4px;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scaleY(1);
  }
  50% {
    opacity: 0.7;
    transform: scaleY(0.8);
  }
}
</style>
