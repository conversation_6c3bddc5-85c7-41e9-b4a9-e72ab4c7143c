<template>
  <!-- 模态框背景 - 适配主题 -->
  <div
    v-if="isOpen"
    class="modal modal-open modal-with-drawer-offset"
    @click="$emit('cancel')"
  >
    <div class="modal-box w-11/12 max-w-2xl" @click.stop>
      <!-- 关闭按钮 -->
      <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" @click="$emit('cancel')">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>

      <!-- 标题 -->
      <h3 class="font-bold text-lg mb-4">{{ modalTitle }}</h3>

      <!-- 表单内容 -->
      <div class="space-y-6">
        <!-- 名称和标签 -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">{{ tagsEnabled !== false ? '名称和标签' : '名称' }}</span>
          </label>
          <div class="relative">
            <input
              ref="titleInput"
              v-model="titleWithTags"
              type="text"
              :placeholder="tagsEnabled !== false ? '书签名称#标签1#标签2' : '书签名称'"
              class="input input-bordered w-full pr-10"
              @input="handleTitleWithTagsInput"
              @keydown="handleTitleKeydown"
              @click="updateCursorPosition"
              @keyup="updateCursorPosition"
            />
            <button
              v-if="titleWithTags.trim()"
              @click="titleWithTags = ''"
              class="btn btn-ghost btn-sm btn-circle absolute right-2 top-1/2 transform -translate-y-1/2"
              title="清除"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>

            <!-- 智能标签建议（仅在标签功能开启时显示） -->
            <div
              v-if="tagsEnabled !== false && smartSuggestions.length > 0 && showSmartSuggestions"
              class="dropdown dropdown-open absolute top-full left-0 right-0 z-10"
            >
              <div class="dropdown-content menu p-2 shadow bg-base-100 rounded-box w-full max-h-40 overflow-y-auto">
                <li
                  v-for="suggestion in smartSuggestions"
                  :key="suggestion"
                >
                  <a @click="applySuggestion(suggestion)">{{ suggestion }}</a>
                </li>
              </div>
            </div>
          </div>
          <div v-if="tagsEnabled !== false" class="label">
            <span class="label-text-alt">格式：名称{{ tagSeparator }}标签1{{ tagSeparator }}标签2，输入{{ tagSeparator }}时会显示标签建议</span>
          </div>
        </div>

        <!-- 网址 -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">网址</span>
          </label>
          <div class="relative">
            <input
              v-model="formData.url"
              type="url"
              placeholder="https://example.com"
              class="input input-bordered w-full pr-10"
            />
            <button
              v-if="formData.url && formData.url.trim()"
              @click="formData.url = ''"
              class="btn btn-ghost btn-sm btn-circle absolute right-2 top-1/2 transform -translate-y-1/2"
              title="清除"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
              </svg>
            </button>
          </div>
        </div>

        <!-- 自定义图标 -->
        <div class="form-control">
          <label class="label">
            <span class="label-text font-medium">自定义图标</span>
          </label>
          
          <!-- 图标选择方式 -->
          <TabToggle
            :tabs="iconTabs"
            v-model:activeTab="iconType"
            class="mb-4"
          />

          <!-- 上传图片 -->
          <div v-if="iconType === 'upload'" class="space-y-3">
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              @change="handleFileUpload"
              class="file-input file-input-bordered w-full"
            />
            <div v-if="formData.customIcon" class="flex items-center gap-3">
              <img :src="formData.customIcon" alt="图标预览" class="w-12 h-12 object-cover rounded-lg border">
              <button
                @click="removeIcon"
                class="btn btn-error btn-sm"
              >
                移除
              </button>
            </div>
          </div>

          <!-- 图片链接 -->
          <div v-if="iconType === 'url'" class="space-y-3">
            <div class="relative">
              <input
                v-model="formData.customIcon"
                type="url"
                placeholder="图片链接 https://..."
                class="input input-bordered w-full pr-10"
              />
              <button
                v-if="formData.customIcon && formData.customIcon.trim()"
                @click="formData.customIcon = ''"
                class="btn btn-ghost btn-sm btn-circle absolute right-2 top-1/2 transform -translate-y-1/2"
                title="清除"
              >
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                  <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                </svg>
              </button>
            </div>
            <div v-if="formData.customIcon" class="flex items-center gap-3">
              <img :src="formData.customIcon" alt="图标预览" class="w-12 h-12 object-cover rounded-lg border" @error="iconError = true">
              <div v-if="iconError" class="text-error text-sm">图片加载失败</div>
            </div>
          </div>
        </div>

        <!-- 当前标签显示（仅在标签功能开启时显示） -->
        <div v-if="tagsEnabled !== false && formData.tags && formData.tags.length > 0" class="form-control">
          <label class="label">
            <span class="label-text font-medium">当前标签</span>
          </label>
          <div class="flex flex-wrap gap-2">
            <div
              v-for="tag in formData.tags"
              :key="tag"
              class="badge badge-lg gap-2"
              :style="{ backgroundColor: getTagColor(tag), color: 'white' }"
            >
              {{ tag }}
              <button @click="removeTag(tag)" class="text-white hover:text-red-200">
                ✕
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <button class="btn" @click="cancel">取消</button>
        <button class="btn btn-primary" @click="save">保存</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import type { BookmarkWithMeta } from '../types'
import TabToggle from './TabToggle.vue'
import { useTagColors } from '../composables/useTagColors'

const props = defineProps<{
  isOpen: boolean
  bookmark?: BookmarkWithMeta
  availableTags: string[]
  tagsEnabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'save', bookmark: BookmarkWithMeta): void
  (e: 'cancel'): void
}>()

const formData = ref<Partial<BookmarkWithMeta>>({})
const iconType = ref<'default' | 'upload' | 'url'>('default')
const titleWithTags = ref('')
const showSmartSuggestions = ref(false)
const cursorPosition = ref(0)
const iconError = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)
const titleInput = ref<HTMLInputElement | null>(null)

// 图标选择Tab配置
const iconTabs = [
  { id: 'default', label: '默认图标' },
  { id: 'upload', label: '上传图片' },
  { id: 'url', label: '图片链接' }
]

// 标签颜色管理
const { getTagColor } = useTagColors()

// 模态框标题
const modalTitle = computed(() => {
  return props.bookmark?.id ? '编辑书签' : '添加书签'
})

// 获取分隔符
const tagSeparator = computed(() => {
  return localStorage.getItem('tagSeparator') || '#'
})

// 获取光标位置的当前标签内容
const getCurrentTagAtCursor = () => {
  const separator = tagSeparator.value
  const input = titleWithTags.value
  const cursor = cursorPosition.value

  console.log('检查光标位置:', cursor, '输入内容:', input)

  // 如果没有分隔符，返回空
  if (!input.includes(separator)) {
    console.log('没有分隔符，不在标签区域')
    return { searchText: '', isInTag: false }
  }

  // 找到光标前最近的分隔符位置
  let lastSeparatorIndex = -1
  for (let i = cursor - 1; i >= 0; i--) {
    if (input[i] === separator) {
      lastSeparatorIndex = i
      break
    }
  }

  console.log('光标前最近的分隔符位置:', lastSeparatorIndex)

  // 如果光标在第一个分隔符之前（在书签名称部分），不显示建议
  if (lastSeparatorIndex === -1) {
    console.log('光标在书签名称部分，不显示建议')
    return { searchText: '', isInTag: false }
  }

  // 找到光标后最近的分隔符位置（或字符串结尾）
  let nextSeparatorIndex = input.length
  for (let i = cursor; i < input.length; i++) {
    if (input[i] === separator) {
      nextSeparatorIndex = i
      break
    }
  }

  console.log('光标后最近的分隔符位置:', nextSeparatorIndex)

  // 提取当前标签的搜索文本（从分隔符后到光标位置）
  const searchText = input.substring(lastSeparatorIndex + 1, cursor)

  console.log('提取的搜索文本:', searchText)

  return { searchText, isInTag: true }
}

// 智能标签建议
const smartSuggestions = computed(() => {
  const { searchText, isInTag } = getCurrentTagAtCursor()

  if (!isInTag) return []

  // 获取已存在的标签
  const separator = tagSeparator.value
  const input = titleWithTags.value
  const parts = input.split(separator)
  const existingTags = parts.slice(1).map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0)

  // 过滤建议
  return props.availableTags.filter(tag =>
    tag.toLowerCase().includes(searchText.toLowerCase()) &&
    !existingTags.includes(tag)
  ).slice(0, 5)
})

// 监听bookmark变化
watch(() => props.bookmark, (bookmark) => {
  if (bookmark) {
    const originalTitle = bookmark.title || ''
    const separator = tagSeparator.value

    // 解析原始标题，提取名称和标签
    let displayTitle = originalTitle
    let extractedTags: string[] = []

    if (originalTitle.includes(separator)) {
      const parts = originalTitle.split(separator)
      displayTitle = parts[0].trim()
      extractedTags = parts.slice(1).map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0)
    }

    // 确定最终的标签列表
    const finalTags = extractedTags.length > 0 ? extractedTags : (bookmark.tags ? [...bookmark.tags] : [])

    // 使用解析后的数据初始化 formData
    formData.value = {
      ...bookmark,
      title: displayTitle, // 使用解析后的显示标题
      tags: finalTags,
      customIcon: bookmark.icon || ''
    }
    iconType.value = bookmark.icon ? 'url' : 'default'

    // 初始化 titleWithTags
    // 如果原始标题包含标签，使用原始标题
    // 如果原始标题不包含标签但有标签数据，组合显示
    if (originalTitle.includes(separator)) {
      titleWithTags.value = originalTitle
    } else if (finalTags.length > 0) {
      titleWithTags.value = `${displayTitle}${separator}${finalTags.join(separator)}`
    } else {
      titleWithTags.value = displayTitle
    }

    console.log('初始化formData:', formData.value)
    console.log('初始化tags:', formData.value.tags)
    console.log('初始化titleWithTags:', titleWithTags.value)
  }
}, { immediate: true })

// 处理文件上传
const handleFileUpload = (event: Event) => {
  const file = (event.target as HTMLInputElement).files?.[0]
  if (file) {
    const reader = new FileReader()
    reader.onload = (e) => {
      formData.value.customIcon = e.target?.result as string
    }
    reader.readAsDataURL(file)
  }
}

// 移除图标
const removeIcon = () => {
  formData.value.customIcon = ''
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

// 更新光标位置
const updateCursorPosition = () => {
  if (titleInput.value) {
    cursorPosition.value = titleInput.value.selectionStart || 0
    updateSuggestionVisibility()
  }
}

// 更新建议显示状态
const updateSuggestionVisibility = () => {
  const { isInTag } = getCurrentTagAtCursor()
  showSmartSuggestions.value = isInTag
}

// 处理标题和标签输入
const handleTitleWithTagsInput = () => {
  const separator = tagSeparator.value
  const input = titleWithTags.value

  // 解析输入内容
  if (input.includes(separator)) {
    const parts = input.split(separator)
    formData.value.title = parts[0].trim()
    const tags = parts.slice(1).map((tag: string) => tag.trim()).filter((tag: string) => tag.length > 0)
    formData.value.tags = [...new Set(tags)]
  } else {
    formData.value.title = input.trim()
    formData.value.tags = []
  }

  // 更新光标位置和建议显示
  setTimeout(() => {
    updateCursorPosition()
  }, 0)
}

// 处理键盘事件
const handleTitleKeydown = (event: KeyboardEvent) => {
  // 更新光标位置
  setTimeout(() => {
    updateCursorPosition()
  }, 0)

  if (event.key === 'Tab' && smartSuggestions.value.length > 0 && showSmartSuggestions.value) {
    event.preventDefault()
    applySuggestion(smartSuggestions.value[0])
  } else if (event.key === 'Escape') {
    showSmartSuggestions.value = false
  } else if (event.key === 'ArrowLeft' || event.key === 'ArrowRight') {
    // 箭头键移动光标时更新建议
    setTimeout(() => {
      updateCursorPosition()
    }, 0)
  }
}

// 应用建议
const applySuggestion = (suggestion: string) => {
  const separator = tagSeparator.value
  const input = titleWithTags.value
  const cursor = cursorPosition.value

  console.log('应用建议:', suggestion, '当前光标位置:', cursor)

  // 找到光标前最近的分隔符位置
  let lastSeparatorIndex = -1
  for (let i = cursor - 1; i >= 0; i--) {
    if (input[i] === separator) {
      lastSeparatorIndex = i
      break
    }
  }

  // 找到光标后最近的分隔符位置（或字符串结尾）
  let nextSeparatorIndex = input.length
  for (let i = cursor; i < input.length; i++) {
    if (input[i] === separator) {
      nextSeparatorIndex = i
      break
    }
  }

  console.log('替换范围:', lastSeparatorIndex + 1, '到', nextSeparatorIndex)

  // 构建新的输入内容
  const beforeTag = input.substring(0, lastSeparatorIndex + 1)
  const afterTag = input.substring(nextSeparatorIndex)
  const newInput = beforeTag + suggestion + afterTag

  console.log('新的输入内容:', newInput)

  titleWithTags.value = newInput

  // 更新数据
  handleTitleWithTagsInput()

  showSmartSuggestions.value = false

  // 设置新的光标位置（在建议文本之后）
  const newCursorPosition = lastSeparatorIndex + 1 + suggestion.length
  setTimeout(() => {
    if (titleInput.value) {
      titleInput.value.focus()
      titleInput.value.setSelectionRange(newCursorPosition, newCursorPosition)
      cursorPosition.value = newCursorPosition
    }
  }, 0)
}

// 移除标签
const removeTag = (tag: string) => {
  if (formData.value.tags) {
    // 从标签数组中移除
    formData.value.tags = formData.value.tags.filter(t => t !== tag)

    // 同步更新 titleWithTags 输入框
    const separator = tagSeparator.value
    const title = formData.value.title || ''
    const remainingTags = formData.value.tags

    if (remainingTags.length > 0) {
      titleWithTags.value = `${title}${separator}${remainingTags.join(separator)}`
    } else {
      titleWithTags.value = title
    }

    console.log('删除标签后更新 titleWithTags:', titleWithTags.value)
  }
}

// 保存
const save = () => {
  console.log('=== EnhancedEditModal save 函数被调用 ===')

  // 确保最新的输入被处理
  handleTitleWithTagsInput()

  // 处理图标
  if (iconType.value === 'default') {
    formData.value.icon = undefined
  } else if (iconType.value === 'upload' || iconType.value === 'url') {
    formData.value.icon = formData.value.customIcon
  }

  // 确保标签数据正确传递
  if (!formData.value.tags) {
    formData.value.tags = []
  }

  // 调试信息
  console.log('准备发送的书签数据:', formData.value)
  console.log('准备发送的标签数据:', formData.value.tags)
  console.log('即将触发 save 事件')

  emit('save', formData.value as BookmarkWithMeta)
  console.log('save 事件已触发')
}

// 取消
const cancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.modal-box {
  max-height: 90vh;
  overflow-y: auto;
}
</style>
