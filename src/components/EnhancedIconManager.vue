<!--
  增强版图标管理器
  基于 /icon-system 的设计，提供批量图标管理功能
-->

<template>
  <div class="enhanced-icon-manager">
    <!-- 标题栏 -->
    <div class="manager-header">
      <div class="header-info">
        <h3 class="manager-title">图标管理器</h3>
        <p class="manager-subtitle">批量管理书签图标，提升加载性能</p>
      </div>
      <div class="header-actions">
        <button 
          class="action-btn refresh-btn"
          @click="refreshStats"
          :disabled="isLoading"
        >
          <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📊</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalBookmarks }}</div>
          <div class="stat-label">总书签数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.cachedIcons }}</div>
          <div class="stat-label">已缓存图标</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">⚡</div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.cacheHitRate }}%</div>
          <div class="stat-label">缓存命中率</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon">💾</div>
        <div class="stat-content">
          <div class="stat-value">{{ formatSize(stats.cacheSize) }}</div>
          <div class="stat-label">缓存大小</div>
        </div>
      </div>
    </div>

    <!-- 操作面板 -->
    <div class="action-panel">
      <div class="panel-section">
        <h4 class="section-title">批量操作</h4>
        <div class="action-buttons">
          <button 
            class="action-btn primary-btn"
            @click="preloadAllIcons"
            :disabled="isLoading"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
            </svg>
            预加载所有图标
          </button>
          
          <button 
            class="action-btn secondary-btn"
            @click="refreshFailedIcons"
            :disabled="isLoading"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            重试失败图标
          </button>
          
          <button 
            class="action-btn danger-btn"
            @click="clearAllCache"
            :disabled="isLoading"
          >
            <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            清空缓存
          </button>
        </div>
      </div>

      <div class="panel-section">
        <h4 class="section-title">图标源配置</h4>
        <div class="source-list">
          <div 
            v-for="source in iconSources" 
            :key="source.name"
            class="source-item"
          >
            <div class="source-info">
              <div class="source-name">{{ source.name }}</div>
              <div class="source-desc">{{ source.description }}</div>
              <div class="source-features">
                <span 
                  v-for="feature in source.features" 
                  :key="feature"
                  class="feature-tag"
                >
                  {{ feature }}
                </span>
              </div>
            </div>
            <div class="source-status">
              <div class="status-indicator" :class="getSourceStatus(source.name)"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 进度指示器 -->
    <div v-if="isLoading" class="progress-overlay">
      <div class="progress-content">
        <div class="progress-spinner"></div>
        <div class="progress-text">{{ loadingText }}</div>
        <div v-if="progress.total > 0" class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${(progress.current / progress.total) * 100}%` }"
          ></div>
        </div>
        <div v-if="progress.total > 0" class="progress-stats">
          {{ progress.current }} / {{ progress.total }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { enhancedIconService } from '../services/enhancedIconService'
import { useBookmarkStore } from '../stores/bookmarks'

// Store
const bookmarksStore = useBookmarkStore()

// 响应式状态
const isLoading = ref(false)
const loadingText = ref('')
const progress = reactive({
  current: 0,
  total: 0
})

const stats = reactive({
  totalBookmarks: 0,
  cachedIcons: 0,
  cacheHitRate: 0,
  cacheSize: 0
})

// 图标源配置（从增强服务获取）
const iconSources = ref([
  {
    name: 'Logo.dev API',
    description: '现代化的Logo API服务，高质量图标',
    features: ['modern', 'high-quality', 'reliable']
  },
  {
    name: 'Clearbit Logo API', 
    description: '高质量商业图标，彩色支持，响应快速',
    features: ['color', 'high-quality', 'commercial']
  },
  {
    name: 'Unavatar API',
    description: '通用头像和图标服务，支持多种源',
    features: ['universal', 'reliable', 'fast']
  },
  {
    name: 'DuckDuckGo Favicon API',
    description: '隐私友好的图标服务，无跟踪',
    features: ['privacy', 'no-tracking', 'stable']
  },
  {
    name: 'Direct Favicon',
    description: '直接获取网站favicon',
    features: ['direct', 'standard', 'simple']
  }
])

// 计算属性
const allBookmarks = computed(() => {
  return bookmarksStore.enrichedBookmarks.filter(bookmark => bookmark.url)
})

// 方法
const refreshStats = async () => {
  const cacheStats = enhancedIconService.getCacheStats()
  
  stats.totalBookmarks = allBookmarks.value.length
  stats.cachedIcons = cacheStats.size
  stats.cacheHitRate = stats.totalBookmarks > 0 
    ? Math.round((stats.cachedIcons / stats.totalBookmarks) * 100) 
    : 0
  stats.cacheSize = estimateCacheSize(cacheStats.size)
}

const estimateCacheSize = (iconCount: number): number => {
  // 估算缓存大小（每个图标约10KB）
  return iconCount * 10 * 1024
}

const formatSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const preloadAllIcons = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  loadingText.value = '正在预加载图标...'
  
  const urls = allBookmarks.value.map(bookmark => bookmark.url)
  progress.total = urls.length
  progress.current = 0
  
  try {
    // 分批处理，避免过多并发请求
    const batchSize = 5
    for (let i = 0; i < urls.length; i += batchSize) {
      const batch = urls.slice(i, i + batchSize)
      await Promise.allSettled(
        batch.map(async (url) => {
          await enhancedIconService.getWebsiteIcon(url)
          progress.current++
        })
      )
    }
    
    await refreshStats()
    alert('图标预加载完成！')
  } catch (error) {
    console.error('预加载图标失败:', error)
    alert('预加载过程中出现错误，请重试')
  } finally {
    isLoading.value = false
    progress.current = 0
    progress.total = 0
  }
}

const refreshFailedIcons = async () => {
  if (isLoading.value) return
  
  isLoading.value = true
  loadingText.value = '正在重试失败的图标...'
  
  try {
    // 清理缓存，强制重新获取
    enhancedIconService.clearExpiredCache()
    await preloadAllIcons()
  } finally {
    isLoading.value = false
  }
}

const clearAllCache = async () => {
  if (!confirm('确定要清空所有图标缓存吗？这将导致下次访问时重新加载所有图标。')) {
    return
  }
  
  isLoading.value = true
  loadingText.value = '正在清空缓存...'
  
  try {
    enhancedIconService.clearExpiredCache()
    await refreshStats()
    alert('缓存已清空！')
  } catch (error) {
    console.error('清空缓存失败:', error)
    alert('清空缓存时出现错误')
  } finally {
    isLoading.value = false
  }
}

const getSourceStatus = (sourceName: string): string => {
  // 简化实现，实际应该检查各个源的可用性
  return 'status-active'
}

// 生命周期
onMounted(() => {
  refreshStats()
})
</script>

<style scoped>
.enhanced-icon-manager {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.manager-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.header-info {
  flex: 1;
}

.manager-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 0.5rem 0;
}

.manager-subtitle {
  color: #6b7280;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 0.75rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #1f2937;
  line-height: 1;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.action-panel {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  overflow: hidden;
}

.panel-section {
  padding: 1.5rem;
}

.panel-section + .panel-section {
  border-top: 1px solid #e5e7eb;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin: 0 0 1rem 0;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
}

.action-btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  background: white;
  color: #374151;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  background: #f9fafb;
  border-color: #9ca3af;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.primary-btn {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.primary-btn:hover:not(:disabled) {
  background: #2563eb;
  border-color: #2563eb;
}

.secondary-btn {
  background: #6b7280;
  border-color: #6b7280;
  color: white;
}

.secondary-btn:hover:not(:disabled) {
  background: #4b5563;
  border-color: #4b5563;
}

.danger-btn {
  background: #ef4444;
  border-color: #ef4444;
  color: white;
}

.danger-btn:hover:not(:disabled) {
  background: #dc2626;
  border-color: #dc2626;
}

.source-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.source-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background: #f9fafb;
  border-radius: 0.375rem;
}

.source-info {
  flex: 1;
}

.source-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.25rem;
}

.source-desc {
  font-size: 0.875rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.source-features {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.feature-tag {
  padding: 0.125rem 0.375rem;
  background: #e5e7eb;
  color: #374151;
  font-size: 0.75rem;
  border-radius: 0.25rem;
}

.source-status {
  flex-shrink: 0;
}

.status-indicator {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: #10b981;
}

.progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.progress-content {
  background: white;
  padding: 2rem;
  border-radius: 0.5rem;
  text-align: center;
  min-width: 300px;
}

.progress-spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid #e5e7eb;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 1rem;
}

.progress-text {
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 1rem;
}

.progress-bar {
  width: 100%;
  height: 0.5rem;
  background: #e5e7eb;
  border-radius: 0.25rem;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: #3b82f6;
  transition: width 0.3s ease;
}

.progress-stats {
  font-size: 0.875rem;
  color: #6b7280;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* 暗色主题 */
[data-theme="dark"] .enhanced-icon-manager {
  color: #f1f5f9;
}

[data-theme="dark"] .manager-title {
  color: #f1f5f9;
}

[data-theme="dark"] .stat-card {
  background: #1e293b;
  border-color: #334155;
}

[data-theme="dark"] .stat-value {
  color: #f1f5f9;
}

[data-theme="dark"] .action-panel {
  background: #1e293b;
  border-color: #334155;
}

[data-theme="dark"] .panel-section + .panel-section {
  border-color: #334155;
}

[data-theme="dark"] .section-title {
  color: #f1f5f9;
}

[data-theme="dark"] .source-item {
  background: #334155;
}

[data-theme="dark"] .source-name {
  color: #f1f5f9;
}

[data-theme="dark"] .progress-content {
  background: #1e293b;
  color: #f1f5f9;
}
</style>
