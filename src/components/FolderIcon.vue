<template>
  <!-- 空心文件夹：没有子文件夹 -->
  <svg 
    v-if="type === 'empty'"
    xmlns="http://www.w3.org/2000/svg" 
    fill="none" 
    viewBox="0 0 24 24" 
    stroke="currentColor" 
    stroke-width="2"
    class="w-4 h-4 mr-2 flex-shrink-0"
  >
    <path stroke-linecap="round" stroke-linejoin="round" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
  </svg>
  
  <!-- 实心文件夹：有子文件夹但未展开 -->
  <svg
    v-else-if="type === 'closed'"
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    viewBox="0 0 24 24"
    class="w-4 h-4 mr-2 flex-shrink-0 folder-icon"
  >
    <path d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z" />
  </svg>

  <!-- 打开的文件夹：已展开子文件夹 -->
  <svg
    v-else
    xmlns="http://www.w3.org/2000/svg"
    fill="currentColor"
    viewBox="0 0 24 24"
    class="w-4 h-4 mr-2 flex-shrink-0 folder-icon"
  >
    <path d="M19 20H4a2 2 0 01-2-2V6a2 2 0 012-2h6l2 2h6a2 2 0 012 2v1H8a2 2 0 00-2 2v7l1.1-4h13.9v3a2 2 0 01-2 2z"/>
  </svg>
</template>

<script setup lang="ts">
defineProps<{
  type: 'empty' | 'closed' | 'open'
}>()
</script>
