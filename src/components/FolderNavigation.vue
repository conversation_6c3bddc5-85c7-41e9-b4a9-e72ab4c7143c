<template>
  <!-- 文件夹导航 - 移到body下避免定位问题 -->
  <Teleport to="body">
    <!-- 模糊背景层 -->
    <div
      v-if="isTabsListVisible"
      class="tabs-overlay"
      @click="toggleTabsList"
    ></div>

    <!-- 标签页列表面板 -->
    <div
      class="tabs-list-panel"
      :class="{ 'visible': isTabsListVisible }"
      v-if="isTabsListVisible || isTabsListAnimating"
    >
      <div class="tabs-panel-header">
        <h3>其他标签页</h3>
        <button class="refresh-btn" @click="refreshTabs" :disabled="isLoading">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <polyline points="23 4 23 10 17 10"/>
            <polyline points="1 20 1 14 7 14"/>
            <path d="m3.51 9a9 9 0 0 1 14.85-3.36L23 10M1 14l4.64 4.36A9 9 0 0 0 20.49 15"/>
          </svg>
        </button>
      </div>

      <div class="tabs-list-content">
        <div v-if="isLoading" class="loading-state">
          <div class="loading-spinner"></div>
          <span>加载中...</span>
        </div>

        <div v-else-if="error" class="error-state">
          <span>{{ error }}</span>
        </div>

        <div v-else-if="tabs.length === 0" class="empty-state">
          <span>没有其他标签页</span>
        </div>

        <div v-else class="tabs-list">
          <div
            v-for="tab in tabs"
            :key="tab.id"
            class="tab-item"
            draggable="true"
            @dragstart="handleTabDragStart($event, tab)"
            @click="switchToTab(tab.id)"
          >
            <div class="tab-favicon">
              <img
                v-if="tab.favIconUrl"
                :src="tab.favIconUrl"
                :alt="tab.title"
                @error="handleFaviconError"
              />
              <div v-else class="default-favicon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                  <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.72"/>
                  <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.72-1.72"/>
                </svg>
              </div>
            </div>

            <div class="tab-info">
              <div class="tab-title" :title="tab.title">{{ tab.title }}</div>
              <div class="tab-url" :title="tab.url">{{ formatUrl(tab.url) }}</div>
            </div>

            <button
              class="close-tab-btn"
              @click.stop="closeTab(tab.id)"
              :title="'关闭标签页'"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="18" y1="6" x2="6" y2="18"/>
                <line x1="6" y1="6" x2="18" y2="18"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 文件夹导航 -->
    <div class="folder-navigation" v-if="allFolders.length > 0" :class="{ 'tabs-open': isTabsListVisible }">
      <!-- 标签页列表按钮 -->
      <div class="tabs-list-button">
        <button
          class="tabs-toggle-btn"
          @click="toggleTabsList"
          :title="isTabsListVisible ? '收起标签页' : '显示其他标签页'"
        >
          <!-- 使用更合适的标签页图标 -->
          <svg v-if="!isTabsListVisible" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
            <line x1="9" y1="9" x2="15" y2="9"/>
            <line x1="9" y1="12" x2="15" y2="12"/>
            <line x1="9" y1="15" x2="15" y2="15"/>
          </svg>
          <svg v-else width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>

      <div
        class="navigation-container"
        :class="{ 'expanded': isTabsListVisible }"
        :data-folder-count="isTabsListVisible ? allFolders.length : null"
      >
        <!-- 标签页展开时的提示文字 -->
        <div v-if="isTabsListVisible" class="drop-hint">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
            <polyline points="7,10 12,15 17,10"/>
            <line x1="12" y1="15" x2="12" y2="3"/>
          </svg>
          <span>拖拽标签页到文件夹</span>
        </div>

        <!-- 文件夹导航项 -->
        <div
          v-for="folder in allFolders"
          :key="folder.id"
          class="nav-item"
          :class="{
            'active': activeFolder === folder.id || clickedFolder === folder.id,
            'drag-over': dragOverFolder === folder.id,
            'expanded': isTabsListVisible
          }"
          @click="scrollToFolder(folder.id)"
          @mouseenter="handleMouseEnter(folder)"
          @mouseleave="handleMouseLeave"
          @dragover="handleDragOver($event, folder.id)"
          @dragleave="handleDragLeave"
          @drop="handleDrop($event, folder.id)"
        >
          <!-- 普通模式：横线 -->
          <div v-if="!isTabsListVisible" class="nav-line"></div>

          <!-- 展开模式：文件夹图标 -->
          <div v-if="isTabsListVisible" class="folder-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="1.5">
              <path d="M22 19a2 2 0 0 1-2 2H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h5l2 3h9a2 2 0 0 1 2 2z"/>
            </svg>
          </div>

          <!-- 文件夹名称 -->
          <div
            class="folder-label"
            :class="{
              'visible': hoveredFolder === folder.id || isTabsListVisible,
              'expanded': isTabsListVisible
            }"
          >
            {{ folder.title }}
          </div>

          <!-- 展开模式：书签数量 -->
          <div v-if="isTabsListVisible" class="bookmark-count" :class="{ 'empty': folder.bookmarkCount === 0 }">
            {{ folder.bookmarkCount === 0 ? '空文件夹' : `${folder.bookmarkCount} 个书签` }}
          </div>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import type { BookmarkGroup, BookmarkWithMeta } from '../types'
import { useTabsList } from '../composables/useTabsList'

const props = defineProps<{
  groups: BookmarkGroup[]
  bookmarks: BookmarkWithMeta[]
}>()

// 响应式状态
const activeFolder = ref<string>('')
const clickedFolder = ref<string>('') // 新增：记录被点击的文件夹
const hoveredFolder = ref<string>('') // 新增：记录鼠标悬停的文件夹

// 标签页列表相关状态
const isTabsListVisible = ref(false)
const isTabsListAnimating = ref(false)
const draggedTab = ref<any>(null)
const dragOverFolder = ref<string>('')

// 使用标签页列表功能
const { tabs, isLoading, error, loadTabs, createBookmarkFromTab, switchToTab, closeTab } = useTabsList()

// 计算所有文件夹（包括空文件夹，用于标签页拖拽）
const allFolders = computed(() => {
  // 根据书签数据计算每个文件夹的书签数量
  const allFoldersWithCount = props.groups.map(group => ({
    ...group,
    bookmarkCount: props.bookmarks.filter(bookmark => bookmark.parentId === group.id).length
  }))

  console.log('FolderNavigation allFolders (包括空文件夹):', allFoldersWithCount)
  return allFoldersWithCount
})

// 计算有书签的文件夹（用于滚动导航）
const foldersWithBookmarks = computed(() => {
  return allFolders.value.filter(group => group.bookmarkCount > 0)
})

// 滚动到指定文件夹
const scrollToFolder = (folderId: string) => {
  // 记录被点击的文件夹，用于保持激活状态
  clickedFolder.value = folderId

  // 检查文件夹是否有书签
  const folder = allFolders.value.find(f => f.id === folderId)
  if (!folder) {
    console.log('文件夹不存在:', folderId)
    return
  }

  if (folder.bookmarkCount === 0) {
    // 空文件夹：显示提示信息
    console.log('点击了空文件夹:', folder.title)
    // 可以在这里添加提示用户文件夹为空的逻辑
    // 例如显示一个 toast 提示
    return
  }

  const element = document.querySelector(`[data-group-id="${folderId}"]`)
  if (element) {
    // 找到包含这个书签容器的文件夹组
    const bookmarkGroup = element.closest('.bookmark-group')
    if (bookmarkGroup) {
      // 找到文件夹标题
      const groupHeader = bookmarkGroup.querySelector('.group-header')
      if (groupHeader) {
        // 计算偏移量，确保文件夹标题完全可见
        const offset = 100 // 给文件夹标题留出足够空间
        const elementTop = groupHeader.getBoundingClientRect().top + window.pageYOffset - offset

        window.scrollTo({
          top: elementTop,
          behavior: 'smooth'
        })

        // 滚动完成后立即检测并更新激活状态
        setTimeout(() => {
          handleScroll()
        }, 100)

        // 再次延迟检测，确保平滑滚动完成后状态正确
        setTimeout(() => {
          handleScroll()
        }, 600)

        return
      }
    }

    // 如果找不到文件夹标题，回退到原来的逻辑
    element.scrollIntoView({
      behavior: 'smooth',
      block: 'start',
      inline: 'nearest'
    })

    // 同样添加滚动后的状态检测
    setTimeout(() => {
      handleScroll()
    }, 100)
    setTimeout(() => {
      handleScroll()
    }, 600)
  } else {
    console.log('找不到文件夹对应的DOM元素:', folderId)
  }
}



// 处理鼠标进入
const handleMouseEnter = (folder: BookmarkGroup) => {
  hoveredFolder.value = folder.id
}

// 处理鼠标离开
const handleMouseLeave = () => {
  hoveredFolder.value = ''
}

// 标签页列表相关方法
const toggleTabsList = () => {
  if (isTabsListVisible.value) {
    // 关闭面板
    isTabsListAnimating.value = true
    isTabsListVisible.value = false
    setTimeout(() => {
      isTabsListAnimating.value = false
    }, 300)
  } else {
    // 打开面板
    isTabsListVisible.value = true
    isTabsListAnimating.value = true
    setTimeout(() => {
      isTabsListAnimating.value = false
    }, 300)
  }
}

const refreshTabs = () => {
  loadTabs()
}

// 格式化URL显示
const formatUrl = (url: string) => {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 处理图标加载错误
const handleFaviconError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 拖拽相关方法
const handleTabDragStart = (event: DragEvent, tab: any) => {
  draggedTab.value = tab
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', JSON.stringify(tab))
    event.dataTransfer.effectAllowed = 'copy'
    // 设置拖拽图像
    const dragImage = event.target as HTMLElement
    event.dataTransfer.setDragImage(dragImage, 0, 0)
  }
  console.log('开始拖拽标签页:', tab.title)
}

const handleDragOver = (event: DragEvent, folderId: string) => {
  event.preventDefault()
  if (event.dataTransfer) {
    event.dataTransfer.dropEffect = 'copy'
  }
  dragOverFolder.value = folderId
}

const handleDragLeave = () => {
  dragOverFolder.value = ''
}

const handleDrop = async (event: DragEvent, folderId: string) => {
  event.preventDefault()
  dragOverFolder.value = ''

  if (!draggedTab.value) return

  try {
    await createBookmarkFromTab(draggedTab.value, folderId)
    console.log(`标签页 "${draggedTab.value.title}" 已保存为书签到文件夹:`, folderId)

    // 触发书签列表刷新
    window.dispatchEvent(new CustomEvent('bookmark-created'))

    // 显示成功提示（可选）
    console.log('✅ 书签创建成功')
  } catch (error) {
    console.error('保存书签失败:', error)
  } finally {
    draggedTab.value = null
  }
}



// 滚动事件处理
const handleScroll = () => {
  // 检查是否滚动到页面顶部（增加容错度）
  const isAtTop = window.pageYOffset <= 50

  if (isAtTop) {
    // 滚动到顶部时激活第一个有书签的文件夹
    const firstFolder = foldersWithBookmarks.value[0]
    if (firstFolder && activeFolder.value !== firstFolder.id) {
      activeFolder.value = firstFolder.id
      clickedFolder.value = '' // 清除点击状态
      console.log('滚动到顶部，激活第一个文件夹:', firstFolder.id)
    }
    return
  }

  // 检查是否滚动到页面底部（增加容错度）
  const isAtBottom = (window.innerHeight + window.pageYOffset) >= (document.documentElement.scrollHeight - 100)

  if (isAtBottom) {
    // 滚动到底部时激活最后一个有书签的文件夹
    const lastFolder = foldersWithBookmarks.value[foldersWithBookmarks.value.length - 1]
    if (lastFolder && activeFolder.value !== lastFolder.id) {
      activeFolder.value = lastFolder.id
      clickedFolder.value = '' // 清除点击状态
      console.log('滚动到底部，激活最后一个文件夹:', lastFolder.id)
    }
    return
  }

  // 找到当前应该激活的文件夹
  // 规则：找到最后一个在视口上方或视口内的文件夹
  let targetFolderId = ''

  for (const folder of foldersWithBookmarks.value) {
    const element = document.querySelector(`[data-group-id="${folder.id}"]`)
    if (!element) continue

    const bookmarkGroup = element.closest('.bookmark-group')
    if (!bookmarkGroup) continue

    const groupHeader = bookmarkGroup.querySelector('.group-header')
    if (!groupHeader) continue

    const rect = groupHeader.getBoundingClientRect()

    // 如果文件夹标题在视口上方或视口内，则这个文件夹应该被激活
    // 我们要找到最后一个满足条件的文件夹
    if (rect.top <= window.innerHeight * 0.2) {
      targetFolderId = folder.id
    }
  }

  // 更新活跃文件夹
  if (targetFolderId && activeFolder.value !== targetFolderId) {
    activeFolder.value = targetFolderId
    // 如果当前激活的文件夹与点击的文件夹一致，清除点击状态
    if (clickedFolder.value === targetFolderId) {
      clickedFolder.value = ''
    }
    console.log('设置活跃文件夹:', targetFolderId)
  }
}

// 防抖处理
let scrollTimeout: number | null = null
const debouncedHandleScroll = () => {
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
  scrollTimeout = window.setTimeout(handleScroll, 20) // 进一步减少延迟以提高响应性
}

onMounted(() => {
  nextTick(() => {
    // 立即执行一次滚动检测
    handleScroll()

    // 添加事件监听器
    window.addEventListener('scroll', debouncedHandleScroll, { passive: true })
    window.addEventListener('resize', debouncedHandleScroll, { passive: true })

    // 延迟再执行一次，确保DOM完全渲染后的状态正确
    setTimeout(() => {
      handleScroll()
    }, 100)
  })
})

onUnmounted(() => {
  window.removeEventListener('scroll', debouncedHandleScroll)
  window.removeEventListener('resize', debouncedHandleScroll)
  if (scrollTimeout) {
    clearTimeout(scrollTimeout)
  }
})
</script>

<style scoped>
/* 模糊背景层 */
.tabs-overlay {
  position: fixed;
  top: 0;
  left: 300px; /* 从标签页列表右侧开始 */
  width: calc(100vw - 300px);
  height: 100vh;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  z-index: 200; /* 高于搜索框(100) */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 标签页列表按钮 */
.tabs-list-button {
  position: absolute;
  top: -60px;
  left: 8px;
  z-index: 35;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tabs-toggle-btn {
  width: 44px;
  height: 44px;
  border-radius: 0 12px 12px 0;
  background: transparent;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  opacity: 0.7;
}

.tabs-toggle-btn:hover {
  background: var(--bg-surface);
  border: 1px solid var(--border-light);
  border-left: none;
  box-shadow: var(--shadow-neumorphism);
  backdrop-filter: blur(var(--component-blur));
  -webkit-backdrop-filter: blur(var(--component-blur));
  color: var(--text-primary);
  opacity: 1;
  transform: translateX(4px);
}

.tabs-toggle-btn:active {
  transform: translateX(2px) scale(0.98);
  box-shadow: var(--shadow-neumorphism-inset);
}

/* 标签页列表面板 */
.tabs-list-panel {
  position: fixed;
  left: -300px;
  top: 0;
  width: 300px;
  height: 100vh;
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-left: none;
  box-shadow: var(--shadow-neumorphism-elevated);
  backdrop-filter: blur(var(--component-blur));
  -webkit-backdrop-filter: blur(var(--component-blur));
  z-index: 250; /* 高于搜索框和背景层 */
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.tabs-list-panel.visible {
  transform: translateX(300px);
}

.tabs-panel-header {
  padding: 24px 20px 20px;
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background: var(--bg-elevated);
  position: relative;
}

.tabs-panel-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 20px;
  right: 20px;
  height: 1px;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

.tabs-panel-header h3 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

.refresh-btn {
  width: 36px;
  height: 36px;
  border-radius: 12px;
  background: var(--bg-surface);
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-neumorphism);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.refresh-btn:hover:not(:disabled) {
  background: var(--bg-elevated);
  color: var(--text-primary);
  box-shadow: var(--shadow-neumorphism-hover);
  transform: translateY(-1px) scale(1.05);
}

.refresh-btn:active:not(:disabled) {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--shadow-neumorphism-inset);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.tabs-list-content {
  flex: 1;
  overflow-y: auto;
  padding: 12px;
}

/* 状态样式 */
.loading-state,
.error-state,
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-secondary);
  text-align: center;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.folder-navigation {
  position: fixed;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  z-index: 30; /* 降低z-index，低于设置页面的模糊背景层(z-40) */
  pointer-events: auto;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.folder-navigation.tabs-open {
  left: calc(300px + 40px); /* 标签页列表宽度 + 更大间距 */
  top: 50%;
  transform: translateY(-50%);
  width: calc(100vw - 380px); /* 更宽的显示区域 */
  max-width: none; /* 移除最大宽度限制 */
  z-index: 220; /* 高于背景层，但低于标签页列表 */
}

/* 标签页列表项 */
.tabs-list {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.tab-item {
  display: flex;
  align-items: center;
  padding: 14px 16px;
  border-radius: 12px;
  background: var(--bg-surface);
  border: 1px solid var(--border-light);
  cursor: pointer;
  box-shadow: var(--shadow-neumorphism);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.tab-item::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tab-item:hover {
  background: var(--bg-elevated);
  border-color: var(--color-primary);
  box-shadow: var(--shadow-neumorphism-hover);
  transform: translateY(-1px) scale(1.02);
}

.tab-item:hover::before {
  opacity: 1;
}

.tab-item:active {
  transform: translateY(0) scale(0.98);
  box-shadow: var(--shadow-neumorphism-inset);
}

.tab-item[draggable="true"]:hover {
  cursor: grab;
}

.tab-item[draggable="true"]:active {
  cursor: grabbing;
}

.tab-favicon {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-favicon img {
  width: 16px;
  height: 16px;
  border-radius: 2px;
}

.default-favicon {
  width: 16px;
  height: 16px;
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-info {
  flex: 1;
  min-width: 0;
  margin-right: 8px;
}

.tab-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
}

.tab-url {
  font-size: 0.75rem;
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin-top: 2px;
}

.close-tab-btn {
  width: 28px;
  height: 28px;
  border-radius: 8px;
  background: var(--bg-surface);
  border: 1px solid var(--border-light);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-neumorphism);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  flex-shrink: 0;
  transform: scale(0.8);
}

.tab-item:hover .close-tab-btn {
  opacity: 1;
  transform: scale(1);
}

.close-tab-btn:hover {
  background: var(--color-error);
  border-color: var(--color-error);
  color: white;
  box-shadow: var(--shadow-neumorphism-hover);
  transform: scale(1.1);
}

.close-tab-btn:active {
  transform: scale(0.95);
  box-shadow: var(--shadow-neumorphism-inset);
}

/* 设置页面打开时的左移效果 */
body.settings-open .folder-navigation:not(.tabs-open) {
  transform: translateY(-50%) translateX(-60px);
}

.navigation-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
  padding: 12px 8px;
  background: transparent;
  border-radius: 0 12px 12px 0;
  opacity: 0.7;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: visible;
}

.navigation-container:hover {
  opacity: 1;
  background: var(--bg-surface);
  border: 1px solid var(--border-light);
  border-left: none;
  box-shadow: var(--shadow-neumorphism);
  backdrop-filter: blur(var(--component-blur));
  -webkit-backdrop-filter: blur(var(--component-blur));
  padding: 16px 12px;
  transform: translateX(4px);
}

/* 展开模式的网格布局 */
.navigation-container.expanded {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
  padding: 24px;
  background: var(--bg-surface);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  box-shadow: var(--shadow-neumorphism-elevated);
  backdrop-filter: blur(var(--component-blur));
  -webkit-backdrop-filter: blur(var(--component-blur));
  opacity: 1;
  max-height: 85vh;
  overflow-y: auto;
  min-height: 200px;
  width: 100%;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

/* 根据文件夹数量动态调整网格 */
.navigation-container.expanded[data-folder-count="1"] { grid-template-columns: 1fr; max-width: 200px; }
.navigation-container.expanded[data-folder-count="2"] { grid-template-columns: repeat(2, 1fr); max-width: 400px; }
.navigation-container.expanded[data-folder-count="3"] { grid-template-columns: repeat(3, 1fr); max-width: 600px; }
.navigation-container.expanded[data-folder-count="4"] { grid-template-columns: repeat(2, 1fr); max-width: 500px; }
.navigation-container.expanded[data-folder-count="5"] { grid-template-columns: repeat(3, 1fr); max-width: 600px; }
.navigation-container.expanded[data-folder-count="6"] { grid-template-columns: repeat(3, 1fr); max-width: 700px; }

/* 超过6个文件夹时使用自适应布局 */
.navigation-container.expanded:not([data-folder-count="1"]):not([data-folder-count="2"]):not([data-folder-count="3"]):not([data-folder-count="4"]):not([data-folder-count="5"]):not([data-folder-count="6"]) {
  grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
  max-width: 90vw;
}

.navigation-container.expanded::before {
  opacity: 0.7;
}

/* 拖拽提示 */
.drop-hint {
  grid-column: 1 / -1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--bg-elevated);
  border-radius: 12px;
  color: var(--text-secondary);
  font-size: 0.8rem;
  font-weight: 500;
  border: 2px dashed var(--color-primary);
  margin-bottom: 8px;
  box-shadow: var(--shadow-neumorphism-inset);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.drop-hint::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(90deg, transparent, rgba(var(--color-primary-rgb, 59, 130, 246), 0.1), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

.nav-item {
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 6px 8px 6px 4px;
  border-radius: 0 8px 8px 0;
  min-width: 36px;
  min-height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  background: transparent;
  border: none;
  margin-left: 0;
}

.nav-item:hover {
  background: var(--bg-elevated);
  border: 1px solid var(--border-light);
  border-left: none;
  box-shadow: var(--shadow-neumorphism);
  transform: translateX(4px) scale(1.05);
}

.nav-item.active {
  background: var(--bg-elevated);
  border: 1px solid var(--color-primary);
  border-left: none;
  box-shadow: var(--shadow-neumorphism-inset);
  transform: translateX(2px);
}

/* 展开模式的导航项样式 */
.nav-item.expanded {
  flex-direction: column;
  padding: 16px 12px;
  min-width: 100px;
  min-height: 85px;
  background: var(--bg-elevated);
  border: 2px solid var(--border-light);
  border-radius: 16px;
  gap: 8px;
  box-shadow: var(--shadow-neumorphism);
  position: relative;
  overflow: hidden;
}

.nav-item.expanded::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.nav-item.expanded:hover {
  background: var(--bg-surface);
  border-color: var(--color-primary);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-neumorphism-hover);
}

.nav-item.expanded:hover::before {
  opacity: 1;
}

.nav-item.expanded.active {
  border-color: var(--color-accent);
  background: var(--bg-surface);
  box-shadow: var(--shadow-neumorphism-elevated);
}

.nav-item.expanded.active::before {
  opacity: 0.5;
}

.nav-item.drag-over {
  background: var(--color-primary);
  border-color: var(--color-primary);
  transform: scale(1.15) translateY(-2px);
  box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4);
  animation: pulse-drag 1.5s infinite;
}

.nav-item.drag-over .nav-line {
  background: linear-gradient(to right, transparent, white, transparent);
  box-shadow: 0 0 16px rgba(255, 255, 255, 0.8);
  height: 4px;
  animation: glow-line 1s infinite alternate;
}

/* 展开模式的拖拽样式 */
.nav-item.expanded.drag-over {
  background: var(--color-primary);
  border-color: var(--color-primary);
  transform: translateY(-6px) scale(1.08);
  box-shadow: 0 12px 32px rgba(59, 130, 246, 0.4);
  animation: pulse-drag 1.5s infinite;
}

.nav-item.expanded.drag-over::before {
  opacity: 0.3;
}

.nav-item.expanded.drag-over .folder-icon {
  color: white;
  transform: scale(1.2);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.5));
}

.nav-item.expanded.drag-over .folder-label {
  color: white;
  text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
}

.nav-item.expanded.drag-over .bookmark-count {
  color: rgba(255, 255, 255, 0.9);
  text-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
}

@keyframes pulse-drag {
  0%, 100% { box-shadow: 0 8px 24px rgba(59, 130, 246, 0.4); }
  50% { box-shadow: 0 12px 32px rgba(59, 130, 246, 0.6); }
}

@keyframes glow-line {
  0% { box-shadow: 0 0 16px rgba(255, 255, 255, 0.8); }
  100% { box-shadow: 0 0 24px rgba(255, 255, 255, 1); }
}

/* 文件夹图标样式 */
.folder-icon {
  color: var(--color-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.nav-item.expanded:hover .folder-icon {
  color: var(--color-accent);
  transform: scale(1.15) rotate(5deg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.15));
}

.nav-item.expanded.active .folder-icon {
  color: var(--color-accent);
  transform: scale(1.1);
}

/* 书签数量样式 */
.bookmark-count {
  font-size: 0.7rem;
  color: var(--text-secondary);
  font-weight: 600;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.8;
}

.bookmark-count.empty {
  color: var(--text-tertiary);
  opacity: 0.6;
  font-style: italic;
  font-weight: 500;
}

.nav-item.expanded:hover .bookmark-count {
  color: var(--text-primary);
  opacity: 1;
  transform: scale(1.05);
}

.nav-item.expanded:hover .bookmark-count.empty {
  color: var(--text-secondary);
  opacity: 0.8;
}

/* 横线样式 */
.nav-line {
  width: 20px;
  height: 2px;
  background: var(--color-primary);
  border-radius: 0 2px 2px 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0.6;
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
}

.nav-item:hover .nav-line {
  opacity: 1;
  width: 28px;
  height: 3px;
  background: var(--color-accent);
  box-shadow: 0 0 8px rgba(139, 92, 246, 0.3);
}

.nav-item.active .nav-line {
  opacity: 1;
  width: 24px;
  height: 3px;
  background: var(--color-accent);
  box-shadow: 0 0 12px rgba(139, 92, 246, 0.5);
}

/* 文件夹标签样式 */
.folder-label {
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  margin-left: 12px;
  background: var(--bg-surface);
  color: var(--text-primary);
  padding: 4px 8px;
  border: 1px solid var(--border-light);
  border-radius: 0 8px 8px 0;
  font-size: 0.7rem;
  font-weight: 600;
  white-space: nowrap;
  box-shadow: var(--shadow-neumorphism);
  backdrop-filter: blur(var(--component-blur));
  -webkit-backdrop-filter: blur(var(--component-blur));
  opacity: 0;
  visibility: hidden;
  transform: translateY(-50%) translateX(-8px) scale(0.9);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 35;
  pointer-events: none;
  max-width: 120px;
  overflow: hidden;
  text-overflow: ellipsis;
}

.folder-label.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(-50%) translateX(0) scale(1);
  box-shadow: var(--shadow-neumorphism-hover);
}

/* 展开模式的文件夹标签样式 */
.folder-label.expanded {
  position: static;
  transform: none;
  margin: 0;
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
  backdrop-filter: none;
  -webkit-backdrop-filter: none;
  opacity: 1;
  visibility: visible;
  font-size: 0.875rem;
  font-weight: 700;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  pointer-events: auto;
  color: var(--text-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.folder-label.expanded::before {
  display: none;
}

.nav-item.expanded:hover .folder-label.expanded {
  color: var(--color-primary);
  transform: scale(1.05);
}



/* 暗色主题适配 */
[data-theme="dark"] .nav-line {
  background: var(--color-primary);
}

[data-theme="dark"] .nav-item:hover .nav-line,
[data-theme="dark"] .nav-item.active .nav-line {
  background: var(--color-accent);
}



/* 响应式调整 */
@media (max-width: 768px) {
  .folder-navigation {
    left: 0;
  }

  .folder-navigation.tabs-open {
    left: calc(300px + 20px);
    width: calc(100vw - 340px);
  }

  .navigation-container {
    padding: 8px 6px;
  }

  .navigation-container:hover {
    padding: 12px 8px;
  }

  .navigation-container.expanded {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr)) !important;
    gap: 12px;
    padding: 16px;
    max-width: 95vw !important;
    max-height: 80vh;
  }

  .nav-item.expanded {
    min-width: 90px;
    min-height: 75px;
    padding: 12px 8px;
    gap: 6px;
  }

  .folder-icon svg {
    width: 28px;
    height: 28px;
  }

  .folder-label {
    font-size: 0.65rem;
    padding: 3px 6px;
    margin-left: 8px;
    max-width: 100px;
  }

  .tabs-toggle-btn {
    width: 40px;
    height: 40px;
  }

  .nav-item {
    min-width: 32px;
    min-height: 18px;
  }

  .nav-line {
    width: 16px;
  }

  .nav-item:hover .nav-line {
    width: 22px;
  }

  .nav-item.active .nav-line {
    width: 20px;
  }
}
</style>
