<template>
  <!-- 模态框背景 -->
  <div v-if="isOpen" class="modal" @click="$emit('close')">
    <div class="modal-content modal-content--md" @click.stop>
      <div class="modal-header">
        <h3 class="modal-title">文件夹路径</h3>
        <button class="modal-close" @click="$emit('close')">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>

      <div class="modal-body">
        <!-- 路径显示 -->
      <div class="mb-6">
        <div class="flex items-center gap-2 flex-wrap justify-center">
          <!-- 根目录 -->
          <div class="flex items-center gap-2">
            <div
              class="px-3 py-2 rounded-xl text-sm font-medium"
              style="background-color: var(--bg-elevated); color: var(--text-primary);"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 inline mr-1">
                <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12l8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25" />
              </svg>
              {{ getRootFolderName() }}
            </div>
            
            <!-- 路径分隔符 -->
            <svg v-if="folderPath && folderPath.length > 0" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-400">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
          </div>

          <!-- 路径中的每个文件夹 -->
          <template v-if="folderPath && folderPath.length > 0">
            <div 
              v-for="(pathItem, index) in folderPath" 
              :key="index"
              class="flex items-center gap-2"
            >
              <div 
                class="px-3 py-2 rounded-xl text-sm font-medium transition-all duration-200"
                :class="{
                  'bg-blue-100 text-blue-700': index === folderPath.length - 1,
                  'bg-gray-100 text-gray-700': index !== folderPath.length - 1
                }"
                style="box-shadow: var(--shadow-neumorphism);"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 inline mr-1">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M2.25 12.75V12A2.25 2.25 0 014.5 9.75h15A2.25 2.25 0 0121.75 12v.75m-8.69-6.44l-2.12-2.12a1.5 1.5 0 00-1.061-.44H4.5A2.25 2.25 0 002.25 6v12a2.25 2.25 0 002.25 2.25h15A2.25 2.25 0 0021.75 18V9a2.25 2.25 0 00-2.25-2.25h-5.379a1.5 1.5 0 01-1.06-.44z" />
                </svg>
                {{ pathItem }}
              </div>
              
              <!-- 路径分隔符 -->
              <svg v-if="index < folderPath.length - 1" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4 text-gray-400">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </div>
          </template>
        </div>
      </div>
      </div>

      <div class="modal-footer">
        <button class="btn btn--neumorphism btn--sm" @click="$emit('close')">
          关闭
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{
  isOpen: boolean
  folderTitle: string
  folderPath?: string[]
  parentId?: string
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

// 根据parentId判断根文件夹名称
const getRootFolderName = () => {
  // 如果folderPath为空或者只有一层，说明是顶级文件夹
  if (!props.folderPath || props.folderPath.length === 0) {
    // 根据parentId判断是书签栏还是其他书签
    if (props.parentId === '2') {
      return '其他书签'
    } else {
      return '书签栏'
    }
  }

  // 如果有路径，根据第一层路径判断
  // 这里可以根据实际需要调整逻辑
  return '书签栏'
}
</script>

<style scoped>
.modal-content {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}
</style>
