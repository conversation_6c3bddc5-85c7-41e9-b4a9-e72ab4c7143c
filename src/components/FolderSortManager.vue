<template>
  <div class="folder-sort-manager">
    <div class="space-y-1 max-h-96 overflow-y-auto">
      <!-- 显示所有顶层文件夹 -->
      <template v-for="topFolder in topLevelFolders" :key="topFolder.id">
        <!-- 顶层文件夹（不可拖拽） -->
        <button class="modal-folder-btn disabled">
          <FolderIcon :type="getFolderIconType(topFolder.id)" />
          <span class="truncate">{{ topFolder.title }}</span>
          <span class="text-xs opacity-60 ml-2">(顶层文件夹)</span>
        </button>

        <!-- 子文件夹（递归显示） -->
        <template v-if="expandedFolders.has(topFolder.id)">
          <SortableFolderTreeItem
            v-for="folder in getChildFolders(topFolder.id)"
            :key="folder.id"
            :folder="folder"
            :level="2"
            :all-folders="allFolders"
            :expanded-folders="expandedFolders"
            :bookmarks="enrichedBookmarks"
            @change="handleSortChange"
            @delete-folder="handleDeleteFolder"
          />
        </template>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { vDraggable } from 'vue-draggable-plus'
import type { BookmarkGroup } from '../types'
import FolderIcon from './FolderIcon.vue'
import SortableFolderTreeItem from './SortableFolderTreeItem.vue'
import { useBookmarks } from '../composables/useBookmarks'

// 定义事件
const emit = defineEmits<{
  (e: 'sort-complete'): void
}>()

// 使用书签数据
const { allFolders, enrichedBookmarks, loadBookmarks } = useBookmarks()

// 展开状态管理
const expandedFolders = ref<Set<string>>(new Set())

// 获取顶层文件夹
const topLevelFolders = computed(() => {
  return allFolders.value.filter(folder => folder.parentId === '0')
})

// 获取指定文件夹的子文件夹
const getChildFolders = (parentId: string) => {
  return allFolders.value.filter(folder =>
    folder.parentId === parentId &&
    folder.title &&
    folder.title.trim() !== ''
  )
}

// 检查文件夹是否有子文件夹
const hasSubFolders = (folderId: string) => {
  return getChildFolders(folderId).length > 0
}

// 获取文件夹图标类型
const getFolderIconType = (folderId: string): 'empty' | 'closed' | 'open' => {
  if (!hasSubFolders(folderId)) {
    return 'empty'
  }
  return 'open' // 在排序模式下始终显示为展开状态
}

// 拖拽事件处理
const handleSortUpdate = async (evt: any, parentId: string) => {
  console.log('=== Sort Update 事件 ===')
  console.log('事件对象:', evt)
  console.log('父文件夹ID:', parentId)
  console.log('oldIndex:', evt.oldIndex)
  console.log('newIndex:', evt.newIndex)
  console.log('移动的元素:', evt.item)

  // 使用与书签拖移相同的方法获取文件夹ID
  const folderElement = evt.item
  const folderId = folderElement.dataset.folderId

  console.log('文件夹ID:', folderId)

  if (!folderId) {
    console.error('❌ 无法获取文件夹ID')
    return
  }

  // 从 allFolders 中找到对应的文件夹信息
  const folderInfo = allFolders.value.find(f => f.id === folderId)
  if (!folderInfo) {
    console.error('❌ 找不到文件夹信息:', folderId)
    return
  }

  console.log('Moving folder:', folderInfo.title, 'in parent:', parentId, 'from index:', evt.oldIndex, 'to index:', evt.newIndex)

  try {
    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // 移动文件夹到新位置
      await chrome.bookmarks.move(folderId, {
        parentId: parentId,
        index: evt.newIndex
      })
      console.log(`✅ 文件夹 "${folderInfo.title}" 在父级 ${parentId} 中移动到位置 ${evt.newIndex}`)

      // 发射排序完成事件，让父组件使用智能刷新
      emit('sort-complete')
    } else {
      console.log('🛠️ 开发环境：模拟文件夹移动')
    }
  } catch (error) {
    console.error('移动文件夹失败:', error)
  }
}

const handleSortAdd = async (evt: any, targetParentId: string) => {
  console.log('=== Sort Add 事件 ===')
  console.log('事件对象:', evt)
  console.log('目标父文件夹ID:', targetParentId)
  console.log('newIndex:', evt.newIndex)
  console.log('oldIndex:', evt.oldIndex)
  console.log('item:', evt.item)
  console.log('from:', evt.from)
  console.log('to:', evt.to)

  // 使用与书签拖移相同的方法获取文件夹ID
  const folderElement = evt.item
  const folderId = folderElement.dataset.folderId

  console.log('DOM 元素:', folderElement)
  console.log('文件夹ID:', folderId)

  if (!folderId) {
    console.error('❌ 无法获取文件夹ID')
    console.log('DOM 元素属性:', {
      id: folderElement.id,
      className: folderElement.className,
      dataset: folderElement.dataset,
      textContent: folderElement.textContent
    })
    return
  }

  // 从 allFolders 中找到对应的文件夹信息
  const folderInfo = allFolders.value.find(f => f.id === folderId)
  if (!folderInfo) {
    console.error('❌ 找不到文件夹信息:', folderId)
    return
  }

  console.log('Moving folder:', folderInfo.title, 'to parent:', targetParentId, 'at index:', evt.newIndex)

  try {
    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // 移动文件夹到新的父级和位置
      await chrome.bookmarks.move(folderId, {
        parentId: targetParentId,
        index: evt.newIndex
      })
      console.log(`✅ 文件夹 "${folderInfo.title}" 移动到新父级 ${targetParentId} 位置 ${evt.newIndex}`)

      // 发射排序完成事件，让父组件使用智能刷新
      emit('sort-complete')
    } else {
      console.log('🛠️ 开发环境：模拟文件夹移动')
    }
  } catch (error) {
    console.error('移动文件夹失败:', error)
  }
}

const handleSortRemove = async (evt: any, sourceParentId: string) => {
  console.log('Sort remove:', evt, 'from parent:', sourceParentId)
  // 移除事件通常不需要额外处理，因为 Add 事件会处理实际的移动
}

const handleSortChange = () => {
  console.log('Sort change detected')
}

// 处理删除文件夹
const handleDeleteFolder = async (folderId: string) => {
  console.log('=== 删除文件夹完成 ===')
  console.log('文件夹ID:', folderId)

  try {
    // 重新加载数据
    await loadBookmarks()
    console.log('✅ 删除后数据重新加载完成')

    // 发射排序完成事件，通知父组件刷新数据
    emit('sort-complete')
  } catch (error) {
    console.error('删除后重新加载数据失败:', error)
  }
}



// 初始化
onMounted(async () => {
  await loadBookmarks()
  // 默认展开所有顶层文件夹
  const topLevelIds = topLevelFolders.value.map(folder => folder.id)
  expandedFolders.value = new Set(topLevelIds)

  console.log('=== 文件夹原生数据分析 ===')
  console.log('所有文件夹数据:', allFolders.value)
  console.log('顶层文件夹:', topLevelFolders.value)

  // 分析每个顶层文件夹的子文件夹
  topLevelFolders.value.forEach(topFolder => {
    const children = getChildFolders(topFolder.id)
    console.log(`顶层文件夹 "${topFolder.title}" (ID: ${topFolder.id}) 的子文件夹:`, children)

    // 分析子文件夹的层级结构
    children.forEach(child => {
      const grandChildren = getChildFolders(child.id)
      if (grandChildren.length > 0) {
        console.log(`  子文件夹 "${child.title}" (ID: ${child.id}) 的子文件夹:`, grandChildren)
      }
    })
  })

  console.log('=== 数据分析完成 ===')
})
</script>

<style scoped>
/* 使用与MoveFolderModal相同的样式 */
.modal-folder-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 12px;
  text-align: left;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-primary);
  font-size: 14px;
  position: relative;
  margin-bottom: 0.25rem;
}

.modal-folder-btn:hover:not(.disabled) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 100%);
  color: var(--text-primary);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5px);
  border-color: rgba(59, 130, 246, 0.1);
}

.modal-folder-btn:active:not(.disabled) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0.06) 100%);
  transform: translateY(0);
  box-shadow:
    0 1px 4px rgba(0, 0, 0, 0.08),
    inset 0 1px 2px rgba(0, 0, 0, 0.04);
}

/* 禁用状态样式（顶层文件夹） */
.modal-folder-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary);
}

.modal-folder-btn.disabled:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: none;
  box-shadow: none;
  border-color: transparent;
}
</style>


