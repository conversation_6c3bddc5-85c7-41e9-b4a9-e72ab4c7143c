<template>
  <div class="folder-tabs-container mb-3">
    <!-- 文件夹标签页 -->
    <div class="tabs tabs-lifted">
      <button
        v-for="(group, index) in groups"
        :key="group.id"
        class="tab"
        :class="{
          'tab-active': activeGroupId === group.id
        }"
        @click="selectGroup(group.id)"
      >
        <span class="tab-title">{{ group.title }}</span>
        <span class="tab-count badge badge-sm ml-2">
          {{ getGroupBookmarkCount(group.id) }}
        </span>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue'
import type { BookmarkGroup, BookmarkWithMeta } from '../types'

interface Props {
  groups: BookmarkGroup[]
  bookmarks: BookmarkWithMeta[]
  activeGroupId?: string
}

const props = withDefaults(defineProps<Props>(), {
  activeGroupId: ''
})

const emit = defineEmits<{
  (e: 'group-selected', groupId: string): void
}>()

// 计算每个文件夹的书签数量
const getGroupBookmarkCount = (groupId: string) => {
  return props.bookmarks.filter(bookmark => bookmark.parentId === groupId).length
}

// 选择文件夹
const selectGroup = (groupId: string) => {
  emit('group-selected', groupId)
}

// 组件挂载时，如果没有激活的组且有可用组，则激活第一个
onMounted(() => {
  if (!props.activeGroupId && props.groups.length > 0) {
    emit('group-selected', props.groups[0].id)
  }
})
</script>

<style scoped>
/* 文件夹标签页容器 */
.folder-tabs-container {
  width: 100%;
  display: flex;
  justify-content: center;
  margin-bottom: 0.75rem;
}

/* 标签页容器 - 根据内容调整宽度 */
.tabs {
  display: inline-flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.375rem;
  max-width: 600px;
  padding: 0.5rem;
  border-radius: 0.75rem;
}

/* 单个标签页 */
.tab {
  position: relative;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  border: 1px solid rgba(var(--border-base-300, 209 213 219), 0.3);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 0.5rem;
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-base-content, #1f2937);
  min-width: fit-content;
}

.tab:hover {
  border-color: rgba(var(--bg-primary, 59 130 246), 0.5);
  color: var(--text-primary, #3b82f6);
  transform: translateY(-1px);
}

/* 激活状态的标签页 */
.tab-active {
  border-color: rgba(var(--bg-primary, 59 130 246), 0.6);
  color: var(--text-primary, #3b82f6);
  font-weight: 600;
}

.tab-active:hover {
  transform: translateY(-1px);
  box-shadow:
    0 6px 20px rgba(59, 130, 246, 0.25),
    0 3px 8px rgba(59, 130, 246, 0.2);
}

/* 标签页标题 */
.tab-title {
  font-size: inherit;
  font-weight: inherit;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 120px;
}

/* 标签页计数 */
.tab-count {
  font-size: 0.7rem;
  font-weight: 600;
  color: var(--text-base-content-secondary, #6b7280);
  background: rgba(var(--bg-base-200, 248 250 252), 1);
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
  line-height: 1;
  flex-shrink: 0;
}

.tab-active .tab-count {
  background: rgba(var(--bg-primary, 59 130 246), 0.15);
  color: var(--text-primary, #3b82f6);
}

/* 暗色主题适配 */
[data-theme="dark"] .tabs {
  --bg-base-100: 30 41 59;
  --bg-base-200: 51 65 85;
  --border-base-300: 71 85 105;
  --text-base-content: 248 250 252;
  --text-base-content-secondary: 148 163 184;
  --bg-primary: 147 197 253;
  --text-primary: 147 197 253;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .tabs {
    max-width: 90%;
    padding: 0.375rem;
    gap: 0.25rem;
  }

  .tab {
    padding: 0.5rem 0.75rem;
    font-size: 0.75rem;
  }

  .tab-title {
    max-width: 100px;
  }

  .tab-count {
    font-size: 0.65rem;
    padding: 0.1rem 0.3rem;
  }
}

@media (max-width: 480px) {
  .tabs {
    max-width: 95%;
    padding: 0.25rem;
    gap: 0.2rem;
  }

  .tab {
    padding: 0.375rem 0.5rem;
    font-size: 0.7rem;
    gap: 0.25rem;
  }

  .tab-title {
    max-width: 80px;
  }

  .tab-count {
    font-size: 0.6rem;
    padding: 0.08rem 0.25rem;
  }
}
</style>
