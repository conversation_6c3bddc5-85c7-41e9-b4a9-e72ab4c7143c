<template>
  <div class="folder-tree-item" :style="{ paddingLeft: `${level * 1.5}rem` }">
    <div
      class="folder-item"
      :class="{
        'selected': activeGroupId === folder.id,
        'has-children': hasSubFolders
      }"
      @click="handleFolderClick(folder.id)"
    >
      <div class="folder-content">
        <div class="folder-icon">
          <svg v-if="hasSubFolders" class="w-4 h-4 transition-transform" :class="{ 'rotate-90': expandedFolders.has(folder.id) }" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
          </svg>
          <svg v-else class="w-4 h-4 opacity-30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
          </svg>
        </div>
        <div class="folder-info">
          <span class="folder-title">{{ folder.title }}</span>
          <span class="folder-count">{{ getGroupBookmarkCount(folder.id) }}</span>
        </div>
      </div>
    </div>

    <!-- 递归渲染子文件夹 -->
    <template v-if="expandedFolders.has(folder.id)">
      <FolderTreeItem
        v-for="childFolder in subFolders"
        :key="childFolder.id"
        :folder="childFolder"
        :level="level + 1"
        :all-folders="allFolders"
        :active-group-id="activeGroupId"
        :expanded-folders="expandedFolders"
        :bookmarks="bookmarks"
        @select="$emit('select', $event)"
        @toggle="$emit('toggle', $event)"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { BookmarkGroup, BookmarkWithMeta } from '../types'

interface Props {
  folder: BookmarkGroup
  level: number
  allFolders: BookmarkGroup[]
  activeGroupId?: string
  expandedFolders: Set<string>
  bookmarks: BookmarkWithMeta[]
}

const props = withDefaults(defineProps<Props>(), {
  activeGroupId: ''
})

const emit = defineEmits<{
  (e: 'select', folderId: string): void
  (e: 'toggle', folderId: string): void
}>()

// 获取当前文件夹的子文件夹
const subFolders = computed(() => {
  return props.allFolders.filter(group => group.parentId === props.folder.id)
})

// 检查是否有子文件夹
const hasSubFolders = computed(() => {
  return subFolders.value.length > 0
})

// 计算每个文件夹的书签数量
const getGroupBookmarkCount = (groupId: string) => {
  return props.bookmarks.filter(bookmark => bookmark.parentId === groupId).length
}

// 处理文件夹点击（选择+展开）
const handleFolderClick = (folderId: string) => {
  // 选择文件夹
  emit('select', folderId)
  // 如果有子文件夹，同时切换展开状态
  if (hasSubFolders.value) {
    emit('toggle', folderId)
  }
}
</script>

<style scoped>
.folder-tree-item {
  position: relative;
}

.folder-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  margin: 0.125rem 0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.folder-item:hover {
  background: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.2);
}

.folder-item.selected {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.folder-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.5rem;
}

.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.folder-icon:hover {
  background: rgba(0, 0, 0, 0.1);
}

.folder-item.selected .folder-icon:hover {
  background: rgba(255, 255, 255, 0.2);
}

.folder-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  min-width: 0;
}

.folder-title {
  font-size: 0.875rem;
  font-weight: 500;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-count {
  font-size: 0.75rem;
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  min-width: fit-content;
  margin-left: 0.5rem;
}

.folder-item.selected .folder-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 层级连接线 */
.folder-tree-item::before {
  content: '';
  position: absolute;
  left: calc(var(--level, 0) * 1.5rem + 0.75rem);
  top: 0;
  bottom: 0;
  width: 1px;
  background: rgba(0, 0, 0, 0.1);
}

.folder-tree-item:last-child::before {
  height: 50%;
}

/* 水平连接线 */
.folder-tree-item::after {
  content: '';
  position: absolute;
  left: calc(var(--level, 0) * 1.5rem + 0.75rem);
  top: 50%;
  width: 0.75rem;
  height: 1px;
  background: rgba(0, 0, 0, 0.1);
}

/* 暗色主题适配 */
[data-theme="dark"] .folder-tree-item::before,
[data-theme="dark"] .folder-tree-item::after {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .folder-count {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .folder-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}
</style>
