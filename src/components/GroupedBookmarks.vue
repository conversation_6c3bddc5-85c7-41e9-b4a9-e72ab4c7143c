<template>
  <div class="grouped-bookmarks" :class="folderLayout === 'double' ? 'double-column' : 'single-column'">
    <!-- 未分组的书签 -->
    <div v-if="ungroupedBookmarks.length > 0" class="bookmark-group">
      <div class="group-header">
        <div class="title-row">
          <h2 class="group-title">未分组</h2>
          <span class="bookmark-count">{{ ungroupedBookmarks.length }}</span>
        </div>
      </div>
      <div
        v-draggable="[
          ungroupedBookmarks,
          {
            animation: 150,
            ghostClass: 'ghost',
            group: 'bookmarks',
            forceFallback: true,
            preventOnFilter: false,
            dragoverBubble: false,
            removeCloneOnHide: true,
            onStart: onDragStart,
            onEnd: onDragEnd,
            onUpdate: (evt) => onUpdate(evt, ''),
            onAdd: (evt) => onAdd(evt, ''),
            onRemove: (evt) => onRemove(evt, '')
          }
        ]"
        :class="`bookmarks-container ${viewMode}-mode`"
        data-group-id=""
      >
        <BookmarkCard
          v-for="bookmark in ungroupedBookmarks"
          :key="bookmark.id"
          :bookmark="bookmark"
          :view-mode="viewMode"
          :is-selected="selectedBookmarks.includes(bookmark.id)"
          :show-tags="tagsEnabled !== false"
          :has-any-selected="selectedBookmarks.length > 0"
          :is-pending-delete="pendingDeleteBookmarks.includes(bookmark.id)"
          @toggle-select="toggleBookmarkSelection"
          @shift-select="handleShiftSelect"
          @edit="$emit('edit', bookmark)"
          @delete="$emit('delete', bookmark)"
          @move="$emit('move', bookmark)"
          @show-qrcode="$emit('show-qrcode', bookmark)"
        />
      </div>
    </div>

    <!-- 分组的书签 -->
    <div
      v-for="group in groupsWithBookmarks"
      :key="group.id"
      class="bookmark-group"
    >
      <div class="group-header" style="display: none;">
        <!-- 文件夹标题行 - 隐藏，因为标签页中已有标题 -->
        <div class="title-row">
          <h2 class="group-title">
            <span
              v-if="!isEditingTitle(group.id)"
              @click="startEditTitle(group)"
              class="folder-title-text"
              :title="canEditTitle(group) ? '点击编辑文件夹名称' : ''"
            >{{ group.title }}</span>
            <input
              v-else
              v-model="editingTitleValue"
              @blur="saveTitle(group)"
              @keyup.enter="saveTitle(group)"
              @keyup.escape="cancelEditTitle"
              class="folder-title-input"
              ref="titleInput"
              :maxlength="100"
            />
          </h2>
          <span class="bookmark-count">{{ group.bookmarks.length }}</span>
        </div>
        <div class="group-actions">
          <button
            class="action-btn text-green-500 hover:text-green-600"
            @click="$emit('add-bookmark', group.id)"
            title="添加书签"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
          </button>

          <!-- 三个点下拉菜单 -->
          <div class="relative">
            <button
              class="folder-menu-btn action-btn"
              @click="toggleDropdown(group.id)"
              title="更多操作"
            >
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                <path stroke-linecap="round" stroke-linejoin="round" d="M12 6.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 12.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5ZM12 18.75a.75.75 0 1 1 0-1.5.75.75 0 0 1 0 1.5Z" />
              </svg>
            </button>

            <!-- 下拉菜单 -->
            <div
              v-if="activeDropdown === group.id"
              class="absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-50"
              style="background-color: var(--bg-surface); border-color: var(--border-color); box-shadow: var(--shadow-neumorphism-elevated);"
            >

              <button
                v-if="canMoveFolder(group)"
                class="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                style="color: var(--text-primary);"
                @click="moveFolder(group)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
                </svg>
                移动文件夹
              </button>
              <button
                class="w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2"
                style="color: var(--text-primary);"
                @click="confirmOpenAllBookmarks(group.bookmarks, group.title)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 6H5.25A2.25 2.25 0 0 0 3 8.25v10.5A2.25 2.25 0 0 0 5.25 21h10.5A2.25 2.25 0 0 0 18 18.75V10.5m-10.5 6L21 3m0 0h-5.25M21 3v5.25" />
                </svg>
                打开全部书签
              </button>

              <button
                class="w-full px-4 py-2 text-left text-sm text-red-600 hover:bg-red-50 rounded-b-lg flex items-center gap-2"
                @click="confirmDeleteGroup(group.id, group.title, group.bookmarks.length)"
              >
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
                  <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
                </svg>
                删除文件夹
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        v-draggable="[
          getDisplayedBookmarks(group.bookmarks, group.id),
          {
            animation: 150,
            ghostClass: 'ghost',
            group: 'bookmarks',
            forceFallback: true,
            preventOnFilter: false,
            dragoverBubble: false,
            removeCloneOnHide: true,
            onStart: onDragStart,
            onEnd: onDragEnd,
            onUpdate: (evt) => onUpdate(evt, group.id),
            onAdd: (evt) => onAdd(evt, group.id),
            onRemove: (evt) => onRemove(evt, group.id)
          }
        ]"
        :class="`bookmarks-container ${viewMode}-mode`"
        :data-group-id="group.id"
      >
          <BookmarkCard
            v-for="bookmark in getDisplayedBookmarks(group.bookmarks, group.id)"
            :key="bookmark.id"
            :bookmark="bookmark"
            :view-mode="viewMode"
            :is-selected="selectedBookmarks.includes(bookmark.id)"
            :show-tags="tagsEnabled !== false"
            :has-any-selected="selectedBookmarks.length > 0"
            :is-pending-delete="pendingDeleteBookmarks.includes(bookmark.id)"
            @toggle-select="toggleBookmarkSelection"
            @shift-select="handleShiftSelect"
            @edit="$emit('edit', bookmark)"
            @delete="$emit('delete', bookmark)"
            @move="$emit('move', bookmark)"
            @show-qrcode="$emit('show-qrcode', bookmark)"
          />
      </div>

      <!-- 展开/折叠按钮 -->
      <div
        v-if="autoCollapse && shouldShowExpandButton(group.bookmarks)"
        class="flex justify-center mt-4"
      >
        <button
          class="btn btn--neumorphism px-4 py-2 rounded-xl text-sm flex items-center gap-2 transition-all duration-200"
          @click="toggleFolderExpansion(group.id)"
        >
          <span>{{ isFolderExpanded(group.id) ? '收起' : '展开更多' }}</span>
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke-width="1.5"
            stroke="currentColor"
            class="w-4 h-4 transition-transform duration-200"
            :class="{ 'rotate-180': isFolderExpanded(group.id) }"
          >
            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { vDraggable } from 'vue-draggable-plus'
import BookmarkCard from './BookmarkCard.vue'
import type { BookmarkWithMeta, BookmarkGroup } from '../types'

const props = defineProps<{
  bookmarks: BookmarkWithMeta[]
  groups: BookmarkGroup[]
  groupedBookmarks?: Array<BookmarkGroup & { bookmarks: BookmarkWithMeta[] }>
  viewMode: 'icon' | 'card'
  selectedBookmarks: string[]
  tagsEnabled?: boolean
  pendingDeleteBookmarks: string[]
  folderLayout?: 'single' | 'double'
  autoCollapse?: boolean
}>()

const emit = defineEmits<{
  (e: 'edit', bookmark: BookmarkWithMeta): void
  (e: 'delete', bookmark: BookmarkWithMeta): void
  (e: 'move', bookmark: BookmarkWithMeta): void
  (e: 'show-qrcode', bookmark: BookmarkWithMeta): void
  (e: 'delete-group', groupId: string): void
  (e: 'add-bookmark', groupId: string): void
  (e: 'toggle-select', bookmarkId: string): void
  (e: 'shift-select', bookmarkId: string): void
  (e: 'immediate-remove', data: {
    bookmarkId: string
    sourceGroupId: string
  }): void
  (e: 'reorder', data: {
    draggedBookmarkId: string
    targetBookmarkId: string
    sourceParentId: string
    targetParentId: string
    sourceIndex: number
    targetIndex: number
    dropPosition?: 'before' | 'after'
  }): void
  (e: 'confirm-open-all', data: { bookmarks: BookmarkWithMeta[]; groupTitle: string }): void
  (e: 'confirm-delete-group', data: { groupId: string; groupTitle: string; bookmarkCount: number }): void
  (e: 'move-folder', data: { folderId: string; folderTitle: string; parentId?: string }): void
  (e: 'folder-renamed', data: { folderId: string; oldTitle: string; newTitle: string }): void
}>()

// 下拉菜单状态
const activeDropdown = ref<string | null>(null)

// 文件夹展开状态
const expandedFolders = ref<Set<string>>(new Set())

// 状态保存和恢复机制
const savedExpandedState = ref<Set<string>>(new Set())

// 保存当前展开状态
const saveExpandedState = () => {
  savedExpandedState.value = new Set(expandedFolders.value)
  console.log('💾 保存文件夹展开状态:', Array.from(savedExpandedState.value))
}

// 恢复展开状态
const restoreExpandedState = () => {
  if (savedExpandedState.value.size > 0) {
    expandedFolders.value = new Set(savedExpandedState.value)
    console.log('🔄 恢复文件夹展开状态:', Array.from(expandedFolders.value))
  }
}

// 清除保存的状态
const clearSavedState = () => {
  savedExpandedState.value.clear()
}

// 暴露方法给父组件
defineExpose({
  saveExpandedState,
  restoreExpandedState,
  clearSavedState
})

// 编辑标题状态
const editingTitleId = ref<string | null>(null)
const editingTitleValue = ref('')
const titleInput = ref<HTMLInputElement | null>(null)



// 切换下拉菜单
const toggleDropdown = (groupId: string) => {
  activeDropdown.value = activeDropdown.value === groupId ? null : groupId
}

// 关闭下拉菜单
const closeDropdown = () => {
  activeDropdown.value = null
}

// 切换文件夹展开状态
const toggleFolderExpansion = (folderId: string) => {
  if (expandedFolders.value.has(folderId)) {
    expandedFolders.value.delete(folderId)
  } else {
    expandedFolders.value.add(folderId)
  }
}

// 检查文件夹是否展开
const isFolderExpanded = (folderId: string) => {
  return expandedFolders.value.has(folderId)
}

// 计算最大显示数量（根据文件夹布局和视图模式）
const getMaxDisplayItems = () => {
  let itemsPerRow: number

  if (props.folderLayout === 'double') {
    // 双列布局：文件夹宽度减半，每行显示的书签数量也相应减少
    itemsPerRow = props.viewMode === 'icon' ? 3 : 2
  } else {
    // 单列布局：正常显示
    itemsPerRow = props.viewMode === 'icon' ? 8 : 5
  }

  const maxRows = 3
  return itemsPerRow * maxRows
}

// 判断是否应该显示展开按钮
const shouldShowExpandButton = (bookmarks: BookmarkWithMeta[]) => {
  return bookmarks.length > getMaxDisplayItems()
}

// 获取显示的书签（限制行数）
const getDisplayedBookmarks = (bookmarks: BookmarkWithMeta[], folderId: string) => {
  // 如果自动折叠功能关闭，显示所有书签
  if (!props.autoCollapse) {
    console.log(`📂 文件夹 ${folderId}: 自动折叠关闭，显示所有 ${bookmarks.length} 个书签`)
    return bookmarks
  }

  const isExpanded = isFolderExpanded(folderId)
  const maxItems = getMaxDisplayItems()

  if (isExpanded) {
    console.log(`📂 文件夹 ${folderId}: 已展开，显示所有 ${bookmarks.length} 个书签`)
    return bookmarks
  }

  const displayedCount = Math.min(bookmarks.length, maxItems)
  console.log(`📂 文件夹 ${folderId}: 折叠状态，显示 ${displayedCount}/${bookmarks.length} 个书签`)
  return bookmarks.slice(0, maxItems)
}



// 全局点击事件监听器
const handleGlobalClick = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.relative')) {
    closeDropdown()
  }
}

// 未分组的书签
const ungroupedBookmarks = computed(() => {
  return props.bookmarks.filter(bookmark => !bookmark.parentId)
})

// 有书签的分组
const groupsWithBookmarks = computed(() => {
  // 如果传入了预先分组好的数据，直接使用
  if (props.groupedBookmarks && props.groupedBookmarks.length > 0) {
    console.log('📊 使用预先分组好的数据:', props.groupedBookmarks.length, '个分组')
    return props.groupedBookmarks
  }

  // 否则使用传统方式计算分组
  console.log('📊 使用传统方式计算分组')
  return props.groups.map(group => ({
    ...group,
    bookmarks: props.bookmarks.filter(bookmark => bookmark.parentId === group.id)
  })).filter(group => group.bookmarks.length > 0)
})

// 辅助函数：从容器元素获取组ID
function getGroupIdFromContainer(container: HTMLElement): string {
  // 查找包含 data-group-id 的父元素
  let element = container
  while (element) {
    if (element.dataset && element.dataset.groupId !== undefined) {
      return element.dataset.groupId
    }
    // 如果是未分组的容器，检查是否包含特定的类名或属性
    if (element.classList.contains('bookmarks-container')) {
      // 查找父级的 bookmark-group
      const bookmarkGroup = element.closest('.bookmark-group')
      if (bookmarkGroup) {
        const groupTitle = bookmarkGroup.querySelector('.group-title')?.textContent
        if (groupTitle === '未分组') {
          return '' // 未分组的组ID为空字符串
        }
      }
    }
    element = element.parentElement as HTMLElement
  }
  return '' // 默认返回空字符串（未分组）
}

// 获取书签的原生Chrome索引
function getBookmarkNativeIndex(bookmarkId: string, parentId: string): number {
  console.log('📍 获取书签原生索引:', bookmarkId, parentId)

  // 获取当前组的所有书签（按原生index字段排序）
  const currentGroupBookmarks = parentId
    ? props.bookmarks.filter(b => b.parentId === parentId)
    : props.bookmarks.filter(b => !b.parentId || b.parentId === '')

  // 按照原生 index 字段排序
  currentGroupBookmarks.sort((a, b) => (a.index || 0) - (b.index || 0))

  // 找到目标书签
  const targetBookmark = currentGroupBookmarks.find(b => b.id === bookmarkId)

  if (targetBookmark && targetBookmark.index !== undefined) {
    console.log('📍 找到书签原生索引:', targetBookmark.index)
    return targetBookmark.index
  }

  console.log('📍 未找到书签，返回默认索引 0')
  return 0
}

// 计算目标插入位置的原生索引
function calculateTargetNativeIndex(displayIndex: number, parentId: string): number {
  console.log('📍 计算目标插入位置的原生索引:', displayIndex, parentId)

  // 获取当前组的所有书签（按原生index字段排序）
  const currentGroupBookmarks = parentId
    ? props.bookmarks.filter(b => b.parentId === parentId)
    : props.bookmarks.filter(b => !b.parentId || b.parentId === '')

  // 按照原生 index 字段排序
  currentGroupBookmarks.sort((a, b) => (a.index || 0) - (b.index || 0))

  console.log('📍 当前组书签列表:', currentGroupBookmarks.map(b => ({
    id: b.id,
    title: b.title,
    index: b.index
  })))

  // 如果显示索引超出范围，插入到末尾
  if (displayIndex >= currentGroupBookmarks.length) {
    console.log('📍 插入到末尾，返回列表长度:', currentGroupBookmarks.length)
    return currentGroupBookmarks.length
  }

  // 返回目标位置的原生索引
  const targetIndex = displayIndex
  console.log('📍 目标插入位置的原生索引:', targetIndex)
  return targetIndex
}

// 拖拽开始事件处理 - 轻量级处理，不干扰拖拽流程
function onDragStart(evt: any) {
  console.log('🎯 拖拽开始 - 防止文字选中')

  // 轻量级处理：只添加样式类和清除选择，不禁用事件
  document.body.classList.add('sortable-drag-active')

  // 清除任何现有的文本选择，但不禁用选择功能
  if (window.getSelection) {
    const selection = window.getSelection()
    if (selection) {
      selection.removeAllRanges()
    }
  }

  // 不干扰 vue-draggable-plus 的默认行为
  return true
}

// 拖拽结束事件处理 - 轻量级清理
function onDragEnd(evt: any) {
  console.log('🏁 拖拽结束 - 清理防选中状态')

  // 延迟清理，确保拖拽操作完全完成
  setTimeout(() => {
    document.body.classList.remove('sortable-drag-active')

    // 清除任何残留的文本选择
    if (window.getSelection) {
      const selection = window.getSelection()
      if (selection) {
        selection.removeAllRanges()
      }
    }
  }, 50) // 缩短延迟时间，避免影响后续操作

  // 不干扰 vue-draggable-plus 的默认行为
  return true
}

// 拖拽事件处理函数
function onUpdate(evt: any, groupId: string) {
  console.log('� UPDATE 事件触发')
  console.log('�📍 事件详情:', {
    type: 'update',
    groupId: groupId,
    oldIndex: evt.oldIndex,
    newIndex: evt.newIndex,
    item: evt.item,
    from: evt.from,
    to: evt.to
  })

  // 获取被拖拽的书签信息
  const bookmarkElement = evt.item
  const bookmarkId = bookmarkElement.dataset.bookmarkId

  if (!bookmarkId) {
    console.error('❌ 无法获取书签ID')
    return
  }

  const bookmark = props.bookmarks.find(b => b.id === bookmarkId)

  // 获取原生索引
  const realSourceIndex = getBookmarkNativeIndex(bookmarkId, groupId)
  const realTargetIndex = calculateTargetNativeIndex(evt.newIndex, groupId)

  console.log('📍 同组内排序详情:', {
    bookmarkId,
    bookmarkTitle: bookmark?.title,
    显示索引: { oldIndex: evt.oldIndex, newIndex: evt.newIndex },
    真实索引: { sourceIndex: realSourceIndex, targetIndex: realTargetIndex },
    groupId,
    parentId: bookmark?.parentId
  })

  // 发射重新排序事件
  const reorderData = {
    draggedBookmarkId: bookmarkId,
    targetBookmarkId: '',
    sourceParentId: groupId,
    targetParentId: groupId,
    sourceIndex: realSourceIndex,
    targetIndex: realTargetIndex
  }
  console.log('🚀 发射 reorder 事件:', reorderData)
  emit('reorder', reorderData)
}

function onAdd(evt: any, targetGroupId: string) {
  console.log('➕ ADD 事件触发')
  console.log('📍 事件详情:', {
    type: 'add',
    targetGroupId: targetGroupId,
    oldIndex: evt.oldIndex,
    newIndex: evt.newIndex,
    item: evt.item,
    from: evt.from,
    to: evt.to
  })

  // 获取被拖拽的书签信息
  const bookmarkElement = evt.item
  const bookmarkId = bookmarkElement.dataset.bookmarkId

  if (!bookmarkId) {
    console.error('❌ 无法获取书签ID')
    return
  }

  // 获取源容器的组ID
  const sourceContainer = evt.from
  const sourceGroupId = getGroupIdFromContainer(sourceContainer)

  const bookmark = props.bookmarks.find(b => b.id === bookmarkId)

  // 获取原生索引
  const realSourceIndex = getBookmarkNativeIndex(bookmarkId, sourceGroupId)
  const realTargetIndex = calculateTargetNativeIndex(evt.newIndex, targetGroupId)

  console.log('📍 跨列表移动详情:', {
    bookmarkId,
    bookmarkTitle: bookmark?.title,
    sourceGroupId,
    targetGroupId,
    显示索引: { oldIndex: evt.oldIndex, newIndex: evt.newIndex },
    真实索引: { sourceIndex: realSourceIndex, targetIndex: realTargetIndex },
    originalParentId: bookmark?.parentId
  })

  // 不移除元素，让 vue-draggable-plus 处理视觉效果
  // 我们通过快速的数据更新来同步状态
  console.log('� 保留 vue-draggable-plus 的视觉效果，通过数据更新同步')

  // 发射重新排序事件
  const reorderData = {
    draggedBookmarkId: bookmarkId,
    targetBookmarkId: '',
    sourceParentId: sourceGroupId,
    targetParentId: targetGroupId,
    sourceIndex: realSourceIndex,
    targetIndex: realTargetIndex
  }
  console.log('🚀 发射 reorder 事件:', reorderData)
  emit('reorder', reorderData)
}

function onRemove(evt: any, sourceGroupId: string) {
  console.log('➖ REMOVE 事件触发')
  console.log('📍 事件详情:', {
    type: 'remove',
    sourceGroupId: sourceGroupId,
    oldIndex: evt.oldIndex,
    item: evt.item,
    from: evt.from,
    to: evt.to
  })

  const bookmarkElement = evt.item
  const bookmarkId = bookmarkElement.dataset.bookmarkId
  const bookmark = props.bookmarks.find(b => b.id === bookmarkId)

  console.log('📍 从源列表移除详情:', {
    bookmarkId,
    bookmarkTitle: bookmark?.title,
    sourceGroupId,
    oldIndex: evt.oldIndex,
    originalParentId: bookmark?.parentId
  })

  // 发射立即移除事件，通知父组件更新数据
  emit('immediate-remove', {
    bookmarkId,
    sourceGroupId
  })

  console.log('✅ REMOVE 事件：已发射立即移除事件')
}

// 切换书签选择状态
const toggleBookmarkSelection = (bookmarkId: string) => {
  emit('toggle-select', bookmarkId)
}

// 处理Shift多选
const handleShiftSelect = (bookmarkId: string) => {
  emit('shift-select', bookmarkId)
}

// 确认打开全部书签
const confirmOpenAllBookmarks = (bookmarks: BookmarkWithMeta[], groupTitle: string) => {
  closeDropdown()
  emit('confirm-open-all', { bookmarks, groupTitle })
}



// 确认删除文件夹
const confirmDeleteGroup = (groupId: string, groupTitle: string, bookmarkCount: number) => {
  closeDropdown()
  emit('confirm-delete-group', { groupId, groupTitle, bookmarkCount })
}

// 检查文件夹是否可以移动
const canMoveFolder = (group: any) => {
  // 不能移动顶层文件夹（parentId为'0'的文件夹）
  return group.parentId !== '0'
}

// 移动文件夹
const moveFolder = (group: any) => {
  closeDropdown()
  emit('move-folder', {
    folderId: group.id,
    folderTitle: group.title,
    parentId: group.parentId
  })
}

// 编辑标题相关方法
const isEditingTitle = (groupId: string) => {
  return editingTitleId.value === groupId
}

const canEditTitle = (group: any) => {
  // 不能编辑顶层文件夹（parentId为'0'的文件夹）
  return group.parentId !== '0'
}

const startEditTitle = (group: any) => {
  if (!canEditTitle(group)) {
    return
  }

  editingTitleId.value = group.id
  editingTitleValue.value = group.title

  // 下一帧聚焦输入框
  nextTick(() => {
    if (titleInput.value) {
      titleInput.value.focus()
      titleInput.value.select()
    }
  })
}

const saveTitle = async (group: any) => {
  if (!editingTitleValue.value.trim()) {
    cancelEditTitle()
    return
  }

  if (editingTitleValue.value.trim() === group.title) {
    cancelEditTitle()
    return
  }

  try {
    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      await chrome.bookmarks.update(group.id, {
        title: editingTitleValue.value.trim()
      })
      console.log(`✅ 文件夹 "${group.title}" 重命名为 "${editingTitleValue.value.trim()}"`)

      // 发射事件通知父组件刷新数据
      emit('folder-renamed', {
        folderId: group.id,
        oldTitle: group.title,
        newTitle: editingTitleValue.value.trim()
      })
    } else {
      console.log('🛠️ 开发环境：模拟重命名文件夹')
    }
  } catch (error) {
    console.error('重命名文件夹失败:', error)
  }

  cancelEditTitle()
}

const cancelEditTitle = () => {
  editingTitleId.value = null
  editingTitleValue.value = ''
}



// 生命周期钩子
onMounted(() => {
  document.addEventListener('click', handleGlobalClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleGlobalClick)
})




</script>

<style scoped>
.grouped-bookmarks {
  padding: 0 2rem;
}

/* 单列布局 */
.grouped-bookmarks.single-column {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

/* 双列布局 */
.grouped-bookmarks.double-column {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  align-items: start;
}

/* 瀑布流效果 - 让文件夹高度自适应 */
.grouped-bookmarks.double-column .bookmark-group {
  break-inside: avoid;
  margin-bottom: 0;
}

/* 书签容器过渡动画 */
.bookmarks-container {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}



/* 书签列表过渡动画 */
.bookmark-list-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition-delay: calc(var(--i, 0) * 0.05s);
}

.bookmark-list-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.bookmark-list-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.9);
}

.bookmark-list-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.bookmark-list-move {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 展开/折叠按钮动画 */
.btn-neumorphism {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-neumorphism:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-neumorphism-elevated);
}

.bookmark-group {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.98) 0%,
    rgba(248, 250, 252, 0.95) 100%);
  border-radius: 24px;
  padding: 2rem;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.08),
    0 1px 3px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  will-change: transform;
  transform: translateZ(0);
}

.bookmark-group::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.8) 50%,
    transparent 100%);
  pointer-events: none;
}

.bookmark-group:hover {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.12),
    0 2px 6px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.8);
  transform: translateY(-4px);
  border-color: rgba(59, 130, 246, 0.2);
}

/* 暗色主题下的文件夹背景 */
[data-theme="dark"] .bookmark-group {
  background: linear-gradient(135deg,
    rgba(30, 41, 59, 0.98) 0%,
    rgba(15, 23, 42, 0.95) 100%);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.3),
    0 1px 3px rgba(0, 0, 0, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .bookmark-group::before {
  background: linear-gradient(90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%);
}

[data-theme="dark"] .bookmark-group:hover {
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.4),
    0 2px 6px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(59, 130, 246, 0.3);
}

.group-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--border-color);
}

.title-row {
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
  flex: 1;
}

/* 文件夹路径样式 */
.folder-path {
  position: absolute;
  top: -1.2rem;
  left: 0;
  display: flex;
  align-items: center;
  font-size: 0.75rem;
  color: var(--text-secondary);
  opacity: 0.8;
  pointer-events: none;
}

.path-item {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.path-separator {
  width: 0.75rem;
  height: 0.75rem;
  margin: 0 0.25rem;
  opacity: 0.6;
}

.group-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  user-select: none;
  flex: 1;
  min-width: 0; /* 允许flex项目收缩 */
}

.folder-title-text {
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;
  display: block;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
}

.folder-title-text:hover {
  background-color: var(--bg-hover);
}

.folder-title-input {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  background: var(--bg-secondary);
  border: 2px solid var(--color-primary);
  border-radius: 6px;
  padding: 4px 8px;
  margin: -4px -8px;
  outline: none;
  width: 100%;
  min-width: 200px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.bookmark-count {
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-color);
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 500;
  user-select: none;
}

.group-actions {
  margin-left: auto;
  display: flex;
  gap: 0.5rem;
}

.action-btn {
  padding: 0.5rem;
  background-color: var(--bg-elevated);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-secondary);
  transition: all 0.2s ease;
  box-shadow: var(--shadow-neumorphism);
  cursor: pointer;
}

.action-btn:hover {
  background-color: var(--bg-surface);
  box-shadow: var(--shadow-neumorphism-hover);
  transform: translateY(-1px);
}

.bookmarks-container {
  display: grid;
  gap: 1rem;
}

/* 图标模式 */
.bookmarks-container.icon-mode {
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
}

/* 卡片模式 - 每行最多5个 */
.bookmarks-container.card-mode {
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1.5rem;
  max-width: 100%;
}

@media (max-width: 768px) {
  .grouped-bookmarks {
    padding: 0 1rem;
  }

  .bookmark-group {
    padding: 1.5rem;
  }

  .group-title {
    font-size: 1.25rem;
  }

  .folder-title-text {
    font-size: 1.25rem;
    padding: 3px 6px;
  }

  .folder-title-input {
    font-size: 1.25rem;
    min-width: 150px;
  }

  .bookmarks-container {
    grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
  }
}

/* vuedraggable 样式 */
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}

.chosen {
  opacity: 0.8;
}

.drag {
  opacity: 0.5;
  transform: rotate(2deg);
}
</style>
