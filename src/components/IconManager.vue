<!--
  图标管理器组件
  批量管理书签图标、缓存统计、清理工具
-->

<template>
  <div class="icon-manager">
    <!-- 统计信息 -->
    <div class="stats-section">
      <h3 class="section-title">图标缓存统计</h3>
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-value">{{ stats.totalCached }}</div>
          <div class="stat-label">已缓存图标</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ formatSize(stats.cacheSize) }}</div>
          <div class="stat-label">缓存大小</div>
        </div>
        <div class="stat-card">
          <div class="stat-value">{{ loadingCount }}</div>
          <div class="stat-label">正在加载</div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="actions-section">
      <button 
        class="btn btn-primary"
        @click="preloadAllIcons"
        :disabled="isPreloading"
      >
        {{ isPreloading ? '预加载中...' : '预加载所有图标' }}
      </button>
      
      <button 
        class="btn btn-outline"
        @click="cleanupCache"
        :disabled="isCleaningUp"
      >
        {{ isCleaningUp ? '清理中...' : '清理过期缓存' }}
      </button>
      
      <button 
        class="btn btn-ghost"
        @click="refreshStats"
      >
        刷新统计
      </button>
    </div>

    <!-- 图标预览网格 -->
    <div class="preview-section">
      <h3 class="section-title">图标预览</h3>
      
      <!-- 筛选器 -->
      <div class="filter-bar">
        <select v-model="sourceFilter" class="select select-sm">
          <option value="">所有来源</option>
          <option value="cache">缓存</option>
          <option value="favicon">网站</option>
          <option value="google">Google</option>
          <option value="duckduckgo">DuckDuckGo</option>
          <option value="fallback">默认</option>
        </select>
        
        <input
          v-model="searchFilter"
          type="text"
          placeholder="搜索域名..."
          class="input input-sm"
        />
      </div>

      <!-- 图标网格 -->
      <div class="icon-grid">
        <div
          v-for="item in filteredIcons"
          :key="item.url"
          class="icon-item"
        >
          <BookmarkIcon
            :url="item.url"
            :alt="item.title"
            size="lg"
            show-source
            @click="selectIcon(item)"
          />
          
          <div class="icon-info">
            <div class="icon-title">{{ item.title }}</div>
            <div class="icon-domain">{{ getDomain(item.url) }}</div>
          </div>
          
          <div class="icon-actions">
            <button
              class="action-btn"
              @click="retryIcon(item)"
              title="重新加载"
            >
              🔄
            </button>
            <button
              class="action-btn"
              @click="removeIcon(item)"
              title="移除缓存"
            >
              🗑️
            </button>
          </div>
        </div>
      </div>

      <!-- 空状态 -->
      <div v-if="filteredIcons.length === 0" class="empty-state">
        <div class="empty-icon">📷</div>
        <div class="empty-text">暂无图标数据</div>
      </div>
    </div>

    <!-- 图标详情模态框 -->
    <div v-if="selectedIcon" class="modal">
      <div class="modal-box" @click.stop>
        <h3 class="modal-title">图标详情</h3>
        
        <div class="icon-detail">
          <BookmarkIcon
            :url="selectedIcon.url"
            :alt="selectedIcon.title"
            size="xl"
            show-source
          />
          
          <div class="detail-info">
            <div class="detail-row">
              <span class="detail-label">标题:</span>
              <span class="detail-value">{{ selectedIcon.title }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">域名:</span>
              <span class="detail-value">{{ getDomain(selectedIcon.url) }}</span>
            </div>
            <div class="detail-row">
              <span class="detail-label">URL:</span>
              <span class="detail-value">{{ selectedIcon.url }}</span>
            </div>
          </div>
        </div>
        
        <div class="modal-action">
          <button class="btn" @click="selectedIcon = null">关闭</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { IconService } from '../services/iconService'
import BookmarkIcon from './BookmarkIcon.vue'

// Props
interface Props {
  bookmarks: Array<{ url: string; title: string }>
}

const props = defineProps<Props>()

// 响应式状态
const stats = ref({
  totalCached: 0,
  cacheSize: 0
})

const isPreloading = ref(false)
const isCleaningUp = ref(false)
const loadingCount = ref(0)
const sourceFilter = ref('')
const searchFilter = ref('')
const selectedIcon = ref<{ url: string; title: string } | null>(null)

// 计算属性
const filteredIcons = computed(() => {
  let filtered = props.bookmarks

  // 按来源筛选
  if (sourceFilter.value) {
    // 这里需要根据实际的图标来源数据进行筛选
    // 暂时返回所有数据
  }

  // 按域名搜索
  if (searchFilter.value) {
    const query = searchFilter.value.toLowerCase()
    filtered = filtered.filter(item => 
      getDomain(item.url).toLowerCase().includes(query) ||
      item.title.toLowerCase().includes(query)
    )
  }

  return filtered
})

// 方法
const getDomain = (url: string): string => {
  try {
    return new URL(url).hostname.replace(/^www\./, '')
  } catch {
    return 'unknown'
  }
}

const formatSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const refreshStats = async () => {
  try {
    const cacheStats = await IconService.getCacheStats()
    stats.value = cacheStats
  } catch (error) {
    console.error('获取缓存统计失败:', error)
  }
}

const preloadAllIcons = async () => {
  if (isPreloading.value) return
  
  isPreloading.value = true
  loadingCount.value = props.bookmarks.length
  
  try {
    const urls = props.bookmarks.map(item => item.url)
    await IconService.preloadIcons(urls)
    
    // 刷新统计
    await refreshStats()
  } catch (error) {
    console.error('预加载图标失败:', error)
  } finally {
    isPreloading.value = false
    loadingCount.value = 0
  }
}

const cleanupCache = async () => {
  if (isCleaningUp.value) return
  
  isCleaningUp.value = true
  
  try {
    await IconService.cleanupCache()
    await refreshStats()
  } catch (error) {
    console.error('清理缓存失败:', error)
  } finally {
    isCleaningUp.value = false
  }
}

const selectIcon = (item: { url: string; title: string }) => {
  selectedIcon.value = item
}

const retryIcon = async (item: { url: string; title: string }) => {
  try {
    // 重新获取图标
    await IconService.getIcon(item.url)
  } catch (error) {
    console.error('重新加载图标失败:', error)
  }
}

const removeIcon = async (item: { url: string; title: string }) => {
  if (confirm(`确定要移除 ${getDomain(item.url)} 的图标缓存吗？`)) {
    try {
      // 这里需要实现移除缓存的方法
      console.log('移除图标缓存:', item.url)
      await refreshStats()
    } catch (error) {
      console.error('移除图标缓存失败:', error)
    }
  }
}

// 生命周期
onMounted(() => {
  refreshStats()
})
</script>

<style scoped>
.icon-manager {
  padding: 1.5rem;
  max-width: 1200px;
  margin: 0 auto;
}

.section-title {
  font-size: 1.25rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
}

.stats-section {
  margin-bottom: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-card {
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  text-align: center;
}

.stat-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: #3b82f6;
  margin-bottom: 0.25rem;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
}

.actions-section {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.preview-section {
  margin-bottom: 2rem;
}

.filter-bar {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  align-items: center;
}

.icon-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.icon-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  transition: all 0.2s ease;
  cursor: pointer;
}

.icon-item:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.icon-info {
  margin-top: 0.75rem;
  text-align: center;
  width: 100%;
}

.icon-title {
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 0.25rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.icon-domain {
  font-size: 0.75rem;
  color: #64748b;
}

.icon-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.icon-item:hover .icon-actions {
  opacity: 1;
}

.action-btn {
  padding: 0.25rem;
  border: none;
  background: #f1f5f9;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.action-btn:hover {
  background: #e2e8f0;
}

.empty-state {
  text-align: center;
  padding: 3rem;
  color: #64748b;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-text {
  font-size: 1.125rem;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
}

.icon-detail {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.detail-info {
  flex: 1;
}

.detail-row {
  display: flex;
  margin-bottom: 0.5rem;
}

.detail-label {
  font-weight: 500;
  color: #374151;
  min-width: 60px;
}

.detail-value {
  color: #6b7280;
  word-break: break-all;
}

/* 暗色主题 */
[data-theme="dark"] .section-title {
  color: #f1f5f9;
}

[data-theme="dark"] .stat-card,
[data-theme="dark"] .icon-item {
  background: #1e293b;
  border-color: #475569;
}

[data-theme="dark"] .icon-title {
  color: #f1f5f9;
}

[data-theme="dark"] .modal-box {
  background: #1e293b;
  color: #f1f5f9;
}
</style>
