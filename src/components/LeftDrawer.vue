<template>
  <!-- 无遮罩层，允许操作内容区 -->
  <Teleport to="body">

    <!-- 左侧抽屉面板 -->
    <Transition
      enter-active-class="transition-transform duration-300 ease-out"
      leave-active-class="transition-transform duration-300 ease-in"
      enter-from-class="-translate-x-full"
      enter-to-class="translate-x-0"
      leave-from-class="translate-x-0"
      leave-to-class="-translate-x-full"
    >
      <div
        v-if="isOpen"
        class="drawer drawer-left"
        style="width: 360px;"
        @click.stop
      >
        <div class="h-full flex flex-col">
          <!-- 顶部关闭按钮 -->
          <div class="flex justify-end p-4 pb-2">
            <button
              @click="close"
              class="btn btn-ghost btn-circle btn-sm"
              title="关闭侧边栏"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          <!-- Tab 导航 -->
          <div class="tab-navigation">
            <button
              class="nav-tab"
              :class="{ 'nav-tab-active': activeTab === 'folders' }"
              @click="activeTab = 'folders'"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
              </svg>
              文件夹
            </button>
            <button
              class="nav-tab"
              :class="{ 'nav-tab-active': activeTab === 'tags' }"
              @click="activeTab = 'tags'"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H9l-2-2H3a2 2 0 01-2-2V5a2 2 0 012-2z" />
              </svg>
              标签
            </button>
            <button
              class="nav-tab"
              :class="{ 'nav-tab-active': activeTab === 'tabs' }"
              @click="activeTab = 'tabs'"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
              </svg>
              浏览器
            </button>
          </div>

          <!-- Tab 内容区域 -->
          <div class="flex-1 overflow-hidden">
            <!-- 书签文件夹 Tab -->
            <div v-if="activeTab === 'folders'" class="h-full overflow-y-auto">
              <div class="p-4 space-y-2">
                <!-- 显示所有书签按钮 -->
                <div
                  class="folder-item"
                  :class="{ 'selected': activeGroupId === 'all' }"
                  @click="selectFolder('all')"
                >
                  <div class="folder-content">
                    <div class="folder-icon">
                      <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                      </svg>
                    </div>
                    <div class="folder-info">
                      <span class="folder-title">所有书签</span>
                      <span class="folder-count">{{ bookmarks.length }}</span>
                    </div>
                  </div>
                </div>

                <!-- 文件夹列表 -->
                <!-- 顶层文件夹 -->
                <template v-for="topFolder in topLevelFolders" :key="topFolder.id">
                  <div
                    class="folder-item"
                    :class="{
                      'selected': activeGroupId === topFolder.id,
                      'has-children': hasSubFolders(topFolder.id)
                    }"
                    @click="handleFolderClick(topFolder.id)"
                  >
                    <div class="folder-content">
                      <div class="folder-expand-icon">
                        <svg
                          v-if="hasSubFolders(topFolder.id)"
                          class="w-3 h-3 transition-transform duration-200"
                          :class="{ 'rotate-90': expandedFolders.has(topFolder.id) }"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                        </svg>
                      </div>
                      <div class="folder-icon">
                        <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-5l-2-2H5a2 2 0 00-2 2z" />
                        </svg>
                      </div>
                      <div class="folder-info">
                        <span class="folder-title">{{ topFolder.title }}</span>
                        <span class="folder-count">{{ getGroupBookmarkCount(topFolder.id) }}</span>
                      </div>
                    </div>
                  </div>

                  <!-- 子文件夹 -->
                  <template v-if="expandedFolders.has(topFolder.id)">
                    <FolderTreeItem
                      v-for="folder in getChildFolders(topFolder.id)"
                      :key="folder.id"
                      :folder="folder"
                      :level="1"
                      :all-folders="allFolders"
                      :active-group-id="activeGroupId"
                      :expanded-folders="expandedFolders"
                      :bookmarks="bookmarks"
                      @select="handleFolderClick"
                      @toggle="toggleFolder"
                    />
                  </template>
                </template>
              </div>
            </div>

            <!-- 标签筛选 Tab -->
            <div v-if="activeTab === 'tags'" class="h-full overflow-y-auto">
              <div class="p-4 space-y-2">
                <!-- 所有标签按钮 -->
                <div
                  class="tag-item"
                  :class="{ 'selected': activeTags.length === 0 }"
                  @click="clearTagFilter"
                >
                  <div class="tag-content">
                    <div class="tag-icon">
                      <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H9l-2-2H3a2 2 0 01-2-2V5a2 2 0 012-2z" />
                      </svg>
                    </div>
                    <div class="tag-info">
                      <span class="tag-title">所有标签</span>
                      <span class="tag-count">{{ bookmarks.length }}</span>
                    </div>
                  </div>
                </div>

                <!-- 从书签标题提取的标签列表 -->
                <template v-for="tag in extractedTags" :key="tag.name">
                  <div
                    class="tag-item"
                    :class="{ 'selected': activeTags.includes(tag.name) }"
                    @click="toggleTag(tag.name)"
                  >
                    <div class="tag-content">
                      <div class="tag-icon">
                        <div
                          class="tag-color-dot"
                          :style="{ backgroundColor: getTagColor(tag.name) }"
                        ></div>
                      </div>
                      <div class="tag-info">
                        <span class="tag-title">{{ tag.name }}</span>
                        <span class="tag-count">{{ tag.count }}</span>
                      </div>
                    </div>
                  </div>
                </template>

                <!-- 无标签提示 -->
                <div v-if="extractedTags.length === 0" class="text-center py-8">
                  <div class="text-4xl mb-4">🏷️</div>
                  <p class="text-sm text-base-content/70">暂无标签</p>
                  <p class="text-xs text-base-content/50 mt-2">在书签标题中使用 # 来添加标签</p>
                </div>
              </div>
            </div>

            <!-- 浏览器标签页 Tab -->
            <div v-if="activeTab === 'tabs'" class="h-full overflow-y-auto">
              <div v-if="tabs.length === 0" class="flex items-center justify-center h-full">
                <div class="text-center py-8">
                  <div class="text-4xl mb-4">🌐</div>
                  <p class="text-sm text-base-content/70">暂无浏览器标签页</p>
                </div>
              </div>

              <div v-else class="p-4 space-y-2">
                <div class="space-y-2">
                  <div
                    v-for="tab in tabs"
                    :key="tab.id"
                    class="tab-item group"
                    @click="openTab(tab)"
                    draggable="true"
                    @dragstart="handleDragStart(tab, $event)"
                  >
                    <div class="tab-content">
                      <div class="tab-icon">
                        <img
                          v-if="tab.favIconUrl"
                          :src="tab.favIconUrl"
                          :alt="tab.title"
                          @error="handleFaviconError"
                        />
                        <div v-else class="default-icon">🌐</div>
                      </div>

                      <div class="tab-info">
                        <h4 class="tab-title">{{ tab.title || '无标题' }}</h4>
                        <p class="tab-url">{{ formatUrl(tab.url) }}</p>
                      </div>

                      <div class="tab-actions">
                        <button
                          class="action-btn bookmark-btn"
                          @click.stop="showBookmarkModal(tab)"
                          :title="isTabBookmarked(tab) ? '编辑书签' : '保存为书签'"
                        >
                          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                            <path
                              :d="isTabBookmarked(tab) ? 'M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z' : 'M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z'"
                              :stroke="isTabBookmarked(tab) ? 'none' : 'currentColor'"
                              :fill="isTabBookmarked(tab) ? 'currentColor' : 'none'"
                              stroke-width="2"
                              stroke-linecap="round"
                              stroke-linejoin="round"
                            />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>

    <!-- 快速编辑书签模态框 -->
    <Transition
      enter-active-class="transition-opacity duration-300 ease-out"
      leave-active-class="transition-opacity duration-300 ease-in"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="showQuickEditModal"
        class="fixed inset-0 z-[10600] bg-black/40 backdrop-blur-sm flex items-center justify-center"
        @click="closeQuickEditModal"
      >
        <div
          class="modal-box"
          @click.stop
        >
          <h3 class="text-lg font-bold mb-4 text-gray-900">
            {{ isEditingExistingBookmark ? '编辑书签' : '保存为书签' }}
          </h3>

          <div class="space-y-4">
            <!-- 标题 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                标题
              </label>
              <input
                v-model="quickEditData.title"
                type="text"
                class="input"
                placeholder="书签标题"
              />
            </div>

            <!-- 文件夹选择 -->
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">
                文件夹
              </label>
              <select v-model="quickEditData.folderId" class="select">
                <option value="">选择文件夹</option>
                <option v-for="folder in allFolders" :key="folder.id" :value="folder.id">
                  {{ folder.title }}
                </option>
              </select>
            </div>
          </div>

          <div class="modal-action">
            <button class="btn" @click="closeQuickEditModal">取消</button>
            <button class="btn btn-primary" @click="saveQuickEdit">
              {{ isEditingExistingBookmark ? '保存' : '添加' }}
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import type { BookmarkGroup, BookmarkWithMeta } from '../types'
import { useTagColors } from '../composables/useTagColors'
import { useSettingsStore } from '../stores/settings'
import FolderTreeItem from './FolderTreeItem.vue'

interface Tab {
  id: number
  title: string
  url: string
  favIconUrl?: string
  active?: boolean
}

interface Props {
  isOpen: boolean
  groups: BookmarkGroup[]
  bookmarks: BookmarkWithMeta[]
  activeGroupId?: string
  availableTags: string[]
  activeTags: string | string[]
  filterMode: 'AND' | 'OR'
}

const props = withDefaults(defineProps<Props>(), {
  activeGroupId: '',
  activeTags: () => []
})

const emit = defineEmits<{
  (e: 'close'): void
  (e: 'group-selected', groupId: string): void
  (e: 'toggle-tag', tag: string): void
  (e: 'clear-tags'): void
  (e: 'set-filter-mode', mode: 'AND' | 'OR'): void
  (e: 'save-bookmark', tab: Tab): void
}>()

const { getTagColor } = useTagColors()
const settingsStore = useSettingsStore()

// 当前激活的 Tab
const activeTab = ref<'folders' | 'tags' | 'tabs'>('folders')

// 从书签标题中提取标签
const extractedTags = computed(() => {
  const tagMap = new Map<string, number>()
  const separator = settingsStore.settings.tagSeparator || '#'

  props.bookmarks.forEach(bookmark => {
    if (bookmark.title) {
      // 使用设置中的分隔符提取标签
      const parts = bookmark.title.split(separator)
      if (parts.length > 1) {
        // 跳过第一部分（书签名称），处理后续的标签
        parts.slice(1).forEach(tag => {
          const cleanTag = tag.trim()
          if (cleanTag) {
            tagMap.set(cleanTag, (tagMap.get(cleanTag) || 0) + 1)
          }
        })
      }
    }
  })

  return Array.from(tagMap.entries())
    .map(([name, count]) => ({ name, count }))
    .sort((a, b) => b.count - a.count) // 按使用频率排序
})

// 浏览器标签页数据
const tabs = ref<Tab[]>([])

// 文件夹展开状态
const expandedFolders = ref<Set<string>>(new Set())

// 快速编辑模态框状态
const showQuickEditModal = ref(false)
const quickEditData = ref({
  title: '',
  url: '',
  folderId: '',
  tabId: null as number | null
})
const isEditingExistingBookmark = ref(false)

// 计算属性：将activeTags转换为数组
const activeTagsArray = computed(() => {
  if (Array.isArray(props.activeTags)) {
    return props.activeTags
  }
  return props.activeTags ? [props.activeTags] : []
})

// 获取所有文件夹（用于显示完整层级结构）
const allFolders = computed(() => {
  return props.groups.filter(group => {
    return group.id !== '0' &&
           group.title &&
           group.title.trim() !== ''
  })
})

// 获取所有顶层文件夹（parentId为'0'的文件夹）
const topLevelFolders = computed(() => {
  return allFolders.value.filter(group => group.parentId === '0')
})

// 获取指定文件夹的子文件夹
const getChildFolders = (parentId: string) => {
  return allFolders.value.filter(group => group.parentId === parentId)
}

// 检查文件夹是否有子文件夹
const hasSubFolders = (folderId: string) => {
  return getChildFolders(folderId).length > 0
}

// 计算每个文件夹的书签数量
const getGroupBookmarkCount = (groupId: string) => {
  return props.bookmarks.filter(bookmark => bookmark.parentId === groupId).length
}

// 选择文件夹
const selectGroup = (groupId: string) => {
  emit('group-selected', groupId)
}

// 标签筛选相关方法
const clearTagFilter = () => {
  emit('clear-tags')
}

const toggleTag = (tag: string) => {
  emit('toggle-tag', tag)
}

// 关闭侧边栏
const close = () => {
  emit('close')
}

// 选择文件夹（新的方法名）
const selectFolder = (folderId: string) => {
  emit('group-selected', folderId)
}

// 切换文件夹展开状态
const toggleFolder = (folderId: string) => {
  if (expandedFolders.value.has(folderId)) {
    expandedFolders.value.delete(folderId)
  } else {
    expandedFolders.value.add(folderId)
  }
}

// 处理文件夹点击（选择+展开）
const handleFolderClick = (folderId: string) => {
  // 选择文件夹
  selectFolder(folderId)
  // 如果有子文件夹，同时切换展开状态
  if (hasSubFolders(folderId)) {
    toggleFolder(folderId)
  }
}

// 检查标签是否激活
function isTagActive(tag: string): boolean {
  if (Array.isArray(props.activeTags)) {
    return props.activeTags.includes(tag)
  }
  return props.activeTags === tag
}

// 切换标签功能已在上面定义

// 清除所有标签
function clearAllTags() {
  emit('clear-tags')
}

// 设置筛选模式
function setFilterMode(mode: 'AND' | 'OR') {
  emit('set-filter-mode', mode)
}

// 获取标签使用次数
function getTagCount(tag: string): number {
  return props.bookmarks.filter(bookmark => 
    bookmark.tags?.includes(tag)
  ).length
}

// 获取浏览器标签页
async function loadTabs() {
  try {
    if (typeof chrome !== 'undefined' && chrome.tabs) {
      const allTabs = await chrome.tabs.query({})
      tabs.value = allTabs.map(tab => ({
        id: tab.id!,
        title: tab.title || '',
        url: tab.url || '',
        favIconUrl: tab.favIconUrl,
        active: tab.active
      }))
    }
  } catch (error) {
    console.warn('无法获取浏览器标签页:', error)
  }
}

// 打开标签页
function openTab(tab: Tab) {
  if (typeof chrome !== 'undefined' && chrome.tabs) {
    chrome.tabs.update(tab.id, { active: true })
  } else {
    // 开发环境下打开新窗口
    window.open(tab.url, '_blank')
  }
}

// 保存为书签
function saveAsBookmark(tab: Tab) {
  emit('save-bookmark', tab)
}

// 处理书签标签页（保存或编辑）
function handleBookmarkTab(tab: Tab) {
  emit('save-bookmark', tab)
}

// 检查标签页是否已经被收藏
function isTabBookmarked(tab: Tab): boolean {
  return props.bookmarks.some(bookmark => bookmark.url === tab.url)
}

// 显示快速编辑模态框
function showBookmarkModal(tab: Tab) {
  const existingBookmark = props.bookmarks.find(bookmark => bookmark.url === tab.url)

  if (existingBookmark) {
    // 编辑现有书签
    isEditingExistingBookmark.value = true
    quickEditData.value = {
      title: existingBookmark.title,
      url: existingBookmark.url,
      folderId: existingBookmark.parentId,
      tabId: tab.id
    }
  } else {
    // 创建新书签
    isEditingExistingBookmark.value = false
    quickEditData.value = {
      title: tab.title || '',
      url: tab.url,
      folderId: props.activeGroupId || '',
      tabId: tab.id
    }
  }

  showQuickEditModal.value = true
}

// 关闭快速编辑模态框
function closeQuickEditModal() {
  showQuickEditModal.value = false
  quickEditData.value = {
    title: '',
    url: '',
    folderId: '',
    tabId: null
  }
}

// 保存快速编辑
function saveQuickEdit() {
  if (!quickEditData.value.title.trim() || !quickEditData.value.folderId) {
    return
  }

  const bookmarkData = {
    title: quickEditData.value.title.trim(),
    url: quickEditData.value.url,
    parentId: quickEditData.value.folderId,
    tabId: quickEditData.value.tabId
  }

  emit('save-bookmark', bookmarkData)
  closeQuickEditModal()
}

// 处理拖拽开始
function handleDragStart(tab: Tab, event: DragEvent) {
  if (event.dataTransfer) {
    event.dataTransfer.setData('text/plain', tab.url)
    event.dataTransfer.setData('text/uri-list', tab.url)
    event.dataTransfer.setData('application/x-bookmark', JSON.stringify({
      title: tab.title,
      url: tab.url,
      favIconUrl: tab.favIconUrl
    }))
  }
}

// 处理图标错误
function handleFaviconError(event: Event) {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 格式化URL显示
function formatUrl(url: string): string {
  try {
    const urlObj = new URL(url)
    return urlObj.hostname
  } catch {
    return url
  }
}

// 关闭面板功能已在上面定义

// 监听面板打开状态，加载标签页数据
watch(() => props.isOpen, (isOpen) => {
  if (isOpen && activeTab.value === 'tabs') {
    loadTabs()
  }
})

// 监听当前Tab切换，加载对应数据
watch(activeTab, (newTab) => {
  if (newTab === 'tabs' && props.isOpen) {
    loadTabs()
  }
})

// 组件挂载时，如果面板打开且在标签页Tab，则加载数据
onMounted(() => {
  if (props.isOpen && activeTab.value === 'tabs') {
    loadTabs()
  }
})
</script>

<style scoped>
/* 新的Tab导航样式 */
.tab-navigation {
  display: flex;
  padding: 0 1rem;
  margin-top: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.nav-tab {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  background: none;
  color: #6b7280;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  position: relative;
}

.nav-tab:hover {
  color: #374151;
  background: #f9fafb;
}

.nav-tab-active {
  color: #3b82f6;
  border-bottom-color: #3b82f6;
  background: #eff6ff;
}

.nav-tab-active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  right: 0;
  height: 2px;
  background: #3b82f6;
}

/* 统一的文件夹和标签项样式 */
.folder-item,
.tag-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.folder-item:hover,
.tag-item:hover {
  background: #f3f4f6;
  border-color: #e5e7eb;
}

.folder-item.selected,
.tag-item.selected {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #1d4ed8;
}

.folder-content,
.tag-content {
  display: flex;
  align-items: center;
  width: 100%;
  min-width: 0;
}

.folder-expand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  margin-right: 0.25rem;
  color: #6b7280;
}

.folder-icon,
.tag-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  margin-right: 0.75rem;
  color: #6b7280;
}

.folder-info,
.tag-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-width: 0;
}

.folder-title,
.tag-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: inherit;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  min-width: 0;
}

.folder-count,
.tag-count {
  font-size: 0.75rem;
  color: #6b7280;
  background: #f3f4f6;
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  margin-left: 0.5rem;
  flex-shrink: 0;
}

.folder-item.selected .folder-count,
.tag-item.selected .tag-count {
  background: #dbeafe;
  color: #1d4ed8;
}

.tag-color-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 暗色主题滚动条 */
[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* 文件夹样式 */
.folder-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  margin: 0.125rem 0;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.folder-item:hover {
  background: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.2);
}

.folder-item.selected {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.folder-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.5rem;
}

.folder-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background-color 0.2s ease;
}

.folder-icon:hover {
  background: rgba(0, 0, 0, 0.1);
}

.folder-item.selected .folder-icon:hover {
  background: rgba(255, 255, 255, 0.2);
}

.folder-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex: 1;
  min-width: 0;
}

.folder-title {
  font-size: 0.875rem;
  font-weight: 500;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.folder-count {
  font-size: 0.75rem;
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  min-width: fit-content;
  margin-left: 0.5rem;
}

.folder-item.selected .folder-count {
  background: rgba(255, 255, 255, 0.2);
  color: white;
}

/* 标签卡片样式 */
.tag-card {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  padding: 0.5rem 0.75rem 0.75rem;
  border-radius: 0.5rem;
  border: 1px solid transparent;
}

.tag-inactive {
  background: rgba(0, 0, 0, 0.02);
  border-color: rgba(0, 0, 0, 0.1);
}

.tag-inactive:hover {
  background: rgba(0, 0, 0, 0.05);
  border-color: rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.tag-active {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.tag-content {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  justify-content: space-between;
}

.tag-name {
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1.2;
  flex: 1;
  text-align: left;
}

.tag-count {
  font-size: 0.625rem;
  opacity: 0.7;
  background: rgba(0, 0, 0, 0.1);
  padding: 0.125rem 0.375rem;
  border-radius: 0.75rem;
  min-width: fit-content;
}

.tag-accent-line {
  position: absolute;
  bottom: 0.125rem;
  left: 0.75rem;
  right: 0.75rem;
  height: 2px;
  border-radius: 1px;
  transition: all 0.2s ease;
  opacity: 0.8;
}

/* 浏览器标签页样式 */
.tab-item {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  background: rgba(0, 0, 0, 0.02);
}

.tab-item:hover {
  background: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

.tab-content {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 0.75rem;
}

.tab-icon {
  width: 1.5rem;
  height: 1.5rem;
  border-radius: 0.25rem;
  overflow: hidden;
  flex-shrink: 0;
}

.tab-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.default-icon {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.1);
  font-size: 0.75rem;
}

.tab-info {
  flex: 1;
  min-width: 0;
}

.tab-title {
  font-size: 0.875rem;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 0.125rem;
}

.tab-url {
  font-size: 0.75rem;
  opacity: 0.7;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.tab-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tab-item:hover .tab-actions {
  opacity: 1;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border: none;
  border-radius: 0.375rem;
  background: rgba(0, 0, 0, 0.1);
  color: currentColor;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
  transform: scale(1.1);
}

.bookmark-btn svg {
  transition: all 0.2s ease;
}

/* 暗色主题适配 */
[data-theme="dark"] .tag-inactive {
  background: rgba(255, 255, 255, 0.02);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .tag-inactive:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.15);
}

[data-theme="dark"] .tag-count {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .tab-item {
  background: rgba(255, 255, 255, 0.02);
}

[data-theme="dark"] .default-icon {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .action-btn {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .folder-count {
  background: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .folder-icon:hover {
  background: rgba(255, 255, 255, 0.1);
}
</style>
