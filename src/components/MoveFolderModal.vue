<template>
  <!-- 使用 daisyUI modal 组件 -->
  <div
    v-if="isOpen"
    class="modal"
    @click="$emit('cancel')"
  >
    <div class="modal-box w-full max-w-2xl" @click.stop>
      <!-- 关闭按钮 -->
      <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" @click="$emit('cancel')">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>

      <!-- 标题 -->
      <h3 class="font-bold text-lg mb-4">{{ currentMode === 'move' ? '移动文件夹' : '排序文件夹' }}</h3>

      <!-- 模式切换按钮 -->
      <div class="mode-switcher">
        <button
          class="mode-btn"
          :class="{ 'active': currentMode === 'move' }"
          @click="switchMode('move')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M7.5 21L3 16.5m0 0L7.5 12M3 16.5h13.5m0-13.5L21 7.5m0 0L16.5 12M21 7.5H7.5" />
          </svg>
          移动
        </button>
        <button
          class="mode-btn"
          :class="{ 'active': currentMode === 'sort' }"
          @click="switchMode('sort')"
        >
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
            <path stroke-linecap="round" stroke-linejoin="round" d="M3 7.5L7.5 3m0 0L12 7.5M7.5 3v13.5m13.5 0L16.5 21m0 0L12 16.5m4.5 4.5V7.5" />
          </svg>
          排序
        </button>
      </div>

      <!-- 内容区域 -->
      <div>
        <!-- 移动模式 -->
        <template v-if="currentMode === 'move'">
          
          <!-- 显示当前文件夹的完整路径 -->
          <div class="current-folder-path">
            当前文件夹：
            <span v-for="(pathItem, index) in currentFolderPath" :key="index" class="path-item">
              {{ pathItem }}
              <svg v-if="index < currentFolderPath.length - 1" class="path-separator" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
              </svg>
            </span>
            <svg v-if="currentFolderPath.length > 0" class="path-separator" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
            </svg>
            <span class="current-folder">{{ folderTitle }}</span>
          </div>
          <p v-if="selectedFolderId" class="text-sm text-blue-600 mt-2 mb-4">
            移动到：{{ getSelectedFolderName() }}
          </p>

          <div class="space-y-1 max-h-64 overflow-y-auto">
            <!-- 动态显示所有顶层文件夹 -->
            <template v-for="topFolder in topLevelFolders" :key="topFolder.id">
              <button
                class="modal-folder-btn"
                :class="{
                  'selected': selectedFolderId === topFolder.id,
                  'disabled': disabledFolderIds.has(topFolder.id)
                }"
                :disabled="disabledFolderIds.has(topFolder.id)"
                @click="selectFolder(topFolder.id)"
              >
                <FolderIcon :type="getFolderIconType(topFolder.id)" />
                <span class="truncate">{{ topFolder.title }}</span>
                <span v-if="topFolder.id === folderId" class="text-xs opacity-60 ml-2">(当前文件夹)</span>
              </button>

              <!-- 子文件夹 -->
              <template v-if="expandedFolders.has(topFolder.id)">
                <MoveFolderTreeItem
                  v-for="folder in getChildFolders(topFolder.id)"
                  :key="folder.id"
                  :folder="folder"
                  :level="2"
                  :all-folders="allFolders"
                  :selected-folder-id="selectedFolderId"
                  :expanded-folders="expandedFolders"
                  :disabled-folder-ids="disabledFolderIds"
                  :current-folder-id="folderId"
                  @select="selectFolder"
                />
              </template>
            </template>
          </div>
        </template>

        <!-- 排序模式 -->
        <template v-else-if="currentMode === 'sort'">
          <div class="mb-4">
            <p class="text-secondary">
              拖拽文件夹进行排序，支持嵌套移动。顶层文件夹（书签栏、其他书签）不可移动。
            </p>
          </div>

          <div class="sort-mode-container">
            <FolderSortManager @sort-complete="handleSortComplete" />
          </div>
        </template>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <button class="btn" @click="cancel">取消</button>
        <button
          v-if="currentMode === 'move'"
          class="btn btn-primary"
          :disabled="!selectedFolderId"
          @click="confirmMove"
        >
          移动
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import type { BookmarkGroup } from '../types'
import FolderIcon from './FolderIcon.vue'
import MoveFolderTreeItem from './MoveFolderTreeItem.vue'
import FolderSortManager from './FolderSortManager.vue'
import { useBookmarks } from '../composables/useBookmarks'

const props = defineProps<{
  isOpen: boolean
  groups: BookmarkGroup[]
  folderId: string
  folderTitle: string
}>()

const emit = defineEmits<{
  (e: 'move', targetFolderId: string): void
  (e: 'sort'): void
  (e: 'cancel'): void
}>()

// 使用最新的书签数据
const { groups: latestGroups, loadBookmarks } = useBookmarks()

// 状态管理
const currentMode = ref<'move' | 'sort'>('move')
const selectedFolderId = ref<string>('')
// 默认展开所有顶层文件夹
const expandedFolders = ref<Set<string>>(new Set())



// 初始化展开状态
const initializeExpandedFolders = () => {
  // 获取所有顶层文件夹的ID并设置为展开状态
  const topLevelIds = props.groups
    .filter(group => group.parentId === '0')
    .map(group => group.id)
  expandedFolders.value = new Set(topLevelIds)
}



// 模式切换
const switchMode = async (mode: 'move' | 'sort') => {
  currentMode.value = mode
  // 重置选择状态
  selectedFolderId.value = ''

  // 切换到移动模式时，重新加载最新数据
  if (mode === 'move') {
    console.log('🔄 切换到移动模式，重新加载最新文件夹数据')
    await loadBookmarks()
  }
}

// 监听props变化，重新初始化状态
watch(() => props.groups, () => {
  initializeExpandedFolders()
}, { immediate: true })

// 监听模态框打开状态
watch(() => props.isOpen, async (isOpen) => {
  if (isOpen) {
    // 重置为移动模式
    currentMode.value = 'move'
    selectedFolderId.value = ''
    initializeExpandedFolders()

    // 主动加载最新的文件夹数据
    console.log('🔄 移动文件夹模态框打开，加载最新文件夹数据')
    await loadBookmarks()
  }
})

// 计算当前文件夹的完整路径（统一使用parentId和title）
const currentFolderPath = computed(() => {
  // 查找当前文件夹
  const currentFolder = props.groups.find(g => g.id === props.folderId)

  if (!currentFolder) {
    return []
  }

  // 根据parentId构建路径
  const buildPath = (folder: BookmarkGroup): string[] => {
    if (!folder || !folder.parentId || folder.parentId === '0') {
      // parentId为'0'或不存在表示顶层文件夹，不显示路径
      return []
    }

    // 查找父文件夹
    const parentFolder = props.groups.find(g => g.id === folder.parentId)
    if (parentFolder) {
      // 递归获取父级路径，然后添加父文件夹标题
      const parentPath = buildPath(parentFolder)
      return [...parentPath, parentFolder.title]
    }

    return []
  }

  // 构建到当前文件夹父级的路径
  return buildPath(currentFolder)
})

// 获取当前文件夹的所有子文件夹ID（递归）
const getAllSubFolderIds = (folderId: string, allFolders: BookmarkGroup[]): Set<string> => {
  const subFolderIds = new Set<string>()
  
  const findSubFolders = (parentId: string) => {
    const directChildren = allFolders.filter(folder => folder.parentId === parentId)
    directChildren.forEach(child => {
      subFolderIds.add(child.id)
      findSubFolders(child.id) // 递归查找子文件夹
    })
  }
  
  findSubFolders(folderId)
  return subFolderIds
}

// 获取所有文件夹（用于显示完整层级结构）
const allFolders = computed(() => {
  // 使用最新的文件夹数据
  const currentGroups = latestGroups.value.length > 0 ? latestGroups.value : props.groups

  return currentGroups.filter(group => {
    return group.id !== '0' &&
           group.title &&
           group.title.trim() !== ''
  })
})

// 获取不能移动到的文件夹ID（当前文件夹和它的子文件夹）
const disabledFolderIds = computed(() => {
  const currentGroups = latestGroups.value.length > 0 ? latestGroups.value : props.groups
  const excludedIds = getAllSubFolderIds(props.folderId, currentGroups)
  excludedIds.add(props.folderId) // 也不能移动到自己
  return excludedIds
})

// 获取所有顶层文件夹（parentId为'0'的文件夹）
const topLevelFolders = computed(() => {
  return allFolders.value.filter(group => group.parentId === '0')
})



// 获取指定文件夹的子文件夹
const getChildFolders = (parentId: string) => {
  return allFolders.value.filter(group => group.parentId === parentId)
}

// 获取指定文件夹的子文件夹
const getSubFolders = (parentId: string) => {
  return allFolders.value.filter(group => group.parentId === parentId)
}

// 检查文件夹是否有子文件夹
const hasSubFolders = (folderId: string) => {
  return getSubFolders(folderId).length > 0
}

// 获取文件夹图标类型
const getFolderIconType = (folderId: string): 'empty' | 'closed' | 'open' => {
  if (!hasSubFolders(folderId)) {
    return 'empty'
  }
  return expandedFolders.value.has(folderId) ? 'open' : 'closed'
}

// 选择文件夹
const selectFolder = (folderId: string) => {
  // 检查文件夹是否被禁用
  if (disabledFolderIds.value.has(folderId)) {
    // 如果是被禁用的文件夹，只切换展开状态，不选择
    if (hasSubFolders(folderId)) {
      if (expandedFolders.value.has(folderId)) {
        expandedFolders.value.delete(folderId)
      } else {
        expandedFolders.value.add(folderId)
      }
    }
    return
  }

  // 选择文件夹
  selectedFolderId.value = folderId

  // 如果有子文件夹，切换展开状态
  if (hasSubFolders(folderId)) {
    if (expandedFolders.value.has(folderId)) {
      expandedFolders.value.delete(folderId)
    } else {
      expandedFolders.value.add(folderId)
    }
  }
}

// 获取选中文件夹的名称
const getSelectedFolderName = () => {
  const folder = allFolders.value.find(f => f.id === selectedFolderId.value)
  return folder?.title || ''
}

// 获取文件夹层级
const getFolderLevel = (folder: BookmarkGroup): number => {
  let level = 0
  let currentFolder = folder

  while (currentFolder.parentId && currentFolder.parentId !== '0') {
    level++
    const parent = props.groups.find(g => g.id === currentFolder.parentId)
    if (!parent) break
    currentFolder = parent
  }

  return level
}



// 处理排序完成
const handleSortComplete = () => {
  console.log('📁 排序完成，通知父组件刷新数据')
  emit('sort')
}

// 确认移动
const confirmMove = () => {
  if (selectedFolderId.value) {
    emit('move', selectedFolderId.value)
  }
}

const cancel = () => {
  emit('cancel')
}
</script>

<style scoped>
/* 使用全局样式，这里只做微调 */
.space-y-1 > * + * {
  margin-top: 0.25rem;
}

.max-h-64 {
  max-height: 16rem;
}

.overflow-y-auto {
  overflow-y: auto;
}

/* 调整按钮样式 */
.modal-folder-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 12px;
  text-align: left;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-primary);
  font-size: 14px;
  position: relative;
}

.modal-folder-btn:hover:not(.selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 100%);
  color: var(--text-primary);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5px);
  border-color: rgba(59, 130, 246, 0.1);
}

.modal-folder-btn:active:not(.selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0.06) 100%);
  transform: translateY(0);
  box-shadow: 
    0 1px 4px rgba(0, 0, 0, 0.08),
    inset 0 1px 2px rgba(0, 0, 0, 0.04);
}

.modal-folder-btn.selected {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.3),
    0 2px 4px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  font-weight: 500;
}

.modal-folder-btn.selected:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 
    0 6px 16px rgba(59, 130, 246, 0.4),
    0 3px 6px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.modal-folder-btn.selected:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 8px rgba(59, 130, 246, 0.3),
    0 1px 2px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 选中状态下的图标颜色 */
.modal-folder-btn.selected .folder-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.modal-folder-btn.selected svg {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 禁用状态样式 */
.modal-folder-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary);
}

.modal-folder-btn.disabled:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

.modal-folder-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary);
}

.modal-folder-btn:disabled:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

/* 添加选中状态的左侧指示条 */
.modal-folder-btn.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #60a5fa, #3b82f6);
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

/* 当前文件夹路径样式 */
.current-folder-path {
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(59, 130, 246, 0.02) 100%);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  font-size: 0.875rem;
}

.path-item {
  display: flex;
  align-items: center;
  color: var(--text-secondary);
  font-weight: 500;
}

.path-separator {
  width: 0.75rem;
  height: 0.75rem;
  margin: 0 0.5rem;
  opacity: 0.6;
  color: var(--text-secondary);
}

.current-folder {
  font-weight: 600;
  color: var(--color-primary);
}

/* 模式切换器样式 */
.mode-switcher {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  padding: 0.25rem;
  background: var(--bg-secondary);
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.mode-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: var(--text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  justify-content: center;
}

.mode-btn:hover:not(.active) {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.mode-btn.active {
  background: var(--color-primary);
  color: white;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
}

.mode-btn svg {
  width: 1rem;
  height: 1rem;
}

/* 排序容器样式 */
.sort-container {
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background: var(--bg-primary);
}

.draggable-list {
  padding: 0.5rem;
}

.sortable-folder-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  cursor: grab;
  transition: all 0.2s ease;
}

.sortable-folder-item:hover {
  background: var(--bg-hover);
  border-color: var(--color-primary);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sortable-folder-item:active {
  cursor: grabbing;
}

.folder-drag-handle {
  color: var(--text-secondary);
  cursor: grab;
}

.folder-drag-handle:hover {
  color: var(--color-primary);
}

.folder-title {
  flex: 1;
  font-weight: 500;
  color: var(--text-primary);
}

.folder-level {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* 拖拽状态样式 */
.ghost {
  opacity: 0.5;
  background: var(--color-primary) !important;
  color: white !important;
}

.chosen {
  background: var(--bg-hover) !important;
  border-color: var(--color-primary) !important;
}

.drag {
  transform: rotate(5deg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

/* 排序模式特有样式 */
.top-level-folder {
  opacity: 0.6;
  cursor: not-allowed;
  background: var(--bg-tertiary) !important;
}

.top-level-folder:hover {
  background: var(--bg-tertiary) !important;
  transform: none !important;
}

.folder-badge {
  font-size: 0.75rem;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  margin-left: auto;
}

.child-folders-container {
  margin-left: 1rem;
  border-left: 2px solid var(--border-color);
  padding-left: 0.5rem;
  margin-top: 0.25rem;
}
</style>
