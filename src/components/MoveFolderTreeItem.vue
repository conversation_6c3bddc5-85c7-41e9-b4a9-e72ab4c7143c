<template>
  <button
    class="modal-folder-btn"
    :class="{
      'selected': selectedFolderId === folder.id,
      'disabled': disabledFolderIds.has(folder.id)
    }"
    :style="{ paddingLeft: `${level * 16}px` }"
    :disabled="disabledFolderIds.has(folder.id)"
    @click="$emit('select', folder.id)"
  >
    <FolderIcon :type="getFolderIconType()" />
    <span class="truncate">{{ folder.title }}</span>
    <span v-if="folder.id === currentFolderId" class="text-xs opacity-60 ml-2">(当前文件夹)</span>
  </button>

  <!-- 递归显示子文件夹 -->
  <template v-if="expandedFolders.has(folder.id)">
    <MoveFolderTreeItem
      v-for="subFolder in subFolders"
      :key="subFolder.id"
      :folder="subFolder"
      :level="level + 1"
      :all-folders="allFolders"
      :selected-folder-id="selectedFolderId"
      :expanded-folders="expandedFolders"
      :disabled-folder-ids="disabledFolderIds"
      :current-folder-id="currentFolderId"
      @select="$emit('select', $event)"
    />
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { BookmarkGroup } from '../types'
import FolderIcon from './FolderIcon.vue'

const props = defineProps<{
  folder: BookmarkGroup
  level: number
  allFolders: BookmarkGroup[]
  selectedFolderId: string
  expandedFolders: Set<string>
  disabledFolderIds: Set<string>
  currentFolderId: string
}>()

const emit = defineEmits<{
  (e: 'select', folderId: string): void
}>()

// 获取当前文件夹的子文件夹
const subFolders = computed(() => {
  return props.allFolders.filter(group => group.parentId === props.folder.id)
})

// 检查是否有子文件夹
const hasSubFolders = computed(() => {
  return subFolders.value.length > 0
})

// 获取文件夹图标类型
const getFolderIconType = (): 'empty' | 'closed' | 'open' => {
  if (!hasSubFolders.value) {
    return 'empty'
  }
  return props.expandedFolders.has(props.folder.id) ? 'open' : 'closed'
}
</script>

<style scoped>
/* 调整按钮样式 */
.modal-folder-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 12px;
  text-align: left;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-primary);
  font-size: 14px;
  position: relative;
}

.modal-folder-btn:hover:not(.selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 100%);
  color: var(--text-primary);
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5px);
  border-color: rgba(59, 130, 246, 0.1);
}

.modal-folder-btn:active:not(.selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0.06) 100%);
  transform: translateY(0);
  box-shadow: 
    0 1px 4px rgba(0, 0, 0, 0.08),
    inset 0 1px 2px rgba(0, 0, 0, 0.04);
}

.modal-folder-btn.selected {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  box-shadow: 
    0 4px 12px rgba(59, 130, 246, 0.3),
    0 2px 4px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border-color: rgba(59, 130, 246, 0.4);
  transform: translateY(-1px);
  font-weight: 500;
}

.modal-folder-btn.selected:hover {
  background: linear-gradient(135deg, #2563eb 0%, #1e40af 100%);
  box-shadow: 
    0 6px 16px rgba(59, 130, 246, 0.4),
    0 3px 6px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25);
  transform: translateY(-2px);
}

.modal-folder-btn.selected:active {
  transform: translateY(0);
  box-shadow: 
    0 2px 8px rgba(59, 130, 246, 0.3),
    0 1px 2px rgba(59, 130, 246, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.15);
}

/* 选中状态下的图标颜色 */
.modal-folder-btn.selected .folder-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.modal-folder-btn.selected svg {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

/* 添加选中状态的左侧指示条 */
.modal-folder-btn.selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(to bottom, #60a5fa, #3b82f6);
  border-radius: 0 2px 2px 0;
  box-shadow: 0 0 8px rgba(59, 130, 246, 0.5);
}

/* 禁用状态样式 */
.modal-folder-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary);
}

.modal-folder-btn.disabled:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: none;
  box-shadow: none;
  border-color: transparent;
}

.modal-folder-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: rgba(0, 0, 0, 0.05);
  color: var(--text-secondary);
}

.modal-folder-btn:disabled:hover {
  background: rgba(0, 0, 0, 0.05);
  transform: none;
  box-shadow: none;
  border-color: transparent;
}
</style>
