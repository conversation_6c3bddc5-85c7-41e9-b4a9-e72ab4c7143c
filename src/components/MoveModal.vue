<template>
  <!-- 模态框背景 -->
  <div
    v-if="isOpen"
    class="modal"
    @click="$emit('cancel')"
  >
    <div class="modal-box" style="width: 24rem; max-width: 28rem;" @click.stop>
      <!-- 关闭按钮 -->
      <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" @click="$emit('cancel')">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>

      <!-- 标题 -->
      <h3 class="font-bold text-lg mb-4 text-gray-900">移动书签</h3>

      <!-- 内容 -->
        <div class="mb-4">
          <p class="text-base-content/70">
            将 <span class="font-medium">{{ selectedCount }}</span> 个书签移动到：
          </p>
          <p v-if="selectedFolderId" class="text-sm text-primary mt-2">
            已选择：{{ getSelectedFolderName() }}
          </p>
        </div>

        <div class="space-y-1 max-h-64 overflow-y-auto">
          <!-- 书签栏 -->
          <button
            class="btn btn-ghost justify-start w-full text-left"
            :class="{ 'bg-blue-100 text-blue-700': selectedFolderId === '1' }"
            @click="selectFolder('1')"
          >
            <FolderIcon :type="getFolderIconType('1')" />
            <span class="truncate">书签栏</span>
          </button>
          
          <!-- 书签栏下的子文件夹 -->
          <template v-if="expandedFolders.has('1')">
            <FolderTreeItem
              v-for="folder in bookmarkBarFolders"
              :key="folder.id"
              :folder="folder"
              :level="2"
              :all-folders="allFolders"
              :selected-folder-id="selectedFolderId"
              :expanded-folders="expandedFolders"
              @select="selectFolder"
            />
          </template>
          
          <!-- 其他书签 -->
          <button
            class="btn btn-ghost justify-start w-full text-left"
            :class="{ 'bg-blue-100 text-blue-700': selectedFolderId === '2' }"
            @click="selectFolder('2')"
          >
            <FolderIcon :type="getFolderIconType('2')" />
            <span class="truncate">其他书签</span>
          </button>

          <!-- 其他书签下的子文件夹 -->
          <template v-if="expandedFolders.has('2')">
            <FolderTreeItem
              v-for="folder in otherBookmarksFolders"
              :key="folder.id"
              :folder="folder"
              :level="2"
              :all-folders="allFolders"
              :selected-folder-id="selectedFolderId"
              :expanded-folders="expandedFolders"
              @select="selectFolder"
            />
          </template>
        </div>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <button class="btn" @click="cancel">取消</button>
        <button
          class="btn btn-primary"
          :disabled="!selectedFolderId"
          @click="confirmMove"
        >
          保存
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, ref } from 'vue'
import type { BookmarkGroup } from '../types'
import FolderIcon from './FolderIcon.vue'
import FolderTreeItem from './FolderTreeItem.vue'

const props = defineProps<{
  isOpen: boolean
  groups: BookmarkGroup[]
  selectedCount: number
}>()

const emit = defineEmits<{
  (e: 'move', folderId: string): void
  (e: 'cancel'): void
}>()

// 状态管理
const selectedFolderId = ref<string>('')
// 默认展开第一层（书签栏和其他书签）
const expandedFolders = ref<Set<string>>(new Set(['1', '2']))

// 所有文件夹（排除根目录）
const allFolders = computed(() => {
  return props.groups.filter(group => {
    return group.id !== '0' && group.title && group.title.trim() !== ''
  })
})

// 书签栏下的直接子文件夹
const bookmarkBarFolders = computed(() => {
  return allFolders.value.filter(group => group.parentId === '1')
})

// 其他书签下的直接子文件夹
const otherBookmarksFolders = computed(() => {
  return allFolders.value.filter(group => group.parentId === '2')
})

// 获取指定文件夹的子文件夹
const getSubFolders = (parentId: string) => {
  return allFolders.value.filter(group => group.parentId === parentId)
}

// 检查文件夹是否有子文件夹
const hasSubFolders = (folderId: string) => {
  return getSubFolders(folderId).length > 0
}

// 获取文件夹图标类型
const getFolderIconType = (folderId: string): 'empty' | 'closed' | 'open' => {
  if (!hasSubFolders(folderId)) {
    return 'empty'
  }
  return expandedFolders.value.has(folderId) ? 'open' : 'closed'
}

// 选择文件夹
const selectFolder = (folderId: string) => {
  selectedFolderId.value = folderId

  // 如果有子文件夹，切换展开状态
  if (hasSubFolders(folderId)) {
    if (expandedFolders.value.has(folderId)) {
      expandedFolders.value.delete(folderId)
    } else {
      expandedFolders.value.add(folderId)
    }
  }
}

// 获取选中文件夹的名称
const getSelectedFolderName = () => {
  if (selectedFolderId.value === '1') return '书签栏'
  if (selectedFolderId.value === '2') return '其他书签'

  const folder = allFolders.value.find(f => f.id === selectedFolderId.value)
  return folder?.title || ''
}

// 确认移动
const confirmMove = () => {
  if (selectedFolderId.value) {
    emit('move', selectedFolderId.value)
  }
}

const cancel = () => {
  emit('cancel')
}
</script>