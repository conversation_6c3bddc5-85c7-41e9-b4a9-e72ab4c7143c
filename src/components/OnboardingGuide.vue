<!--
  首次运行引导组件
  引导用户了解核心功能
-->

<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-500 ease-out"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-all duration-300 ease-in"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div v-if="isVisible" class="onboarding-overlay">
        <!-- 背景遮罩 -->
        <div class="overlay-backdrop" @click="handleSkip"></div>

        <!-- 引导内容 -->
        <div class="onboarding-container">
          <!-- 进度指示器 -->
          <div class="progress-indicator">
            <div
              v-for="(step, index) in steps"
              :key="index"
              class="progress-dot"
              :class="{
                'dot-active': index === currentStep,
                'dot-completed': index < currentStep
              }"
            ></div>
          </div>

          <!-- 步骤内容 -->
          <Transition
            mode="out-in"
            enter-active-class="transition-all duration-300 ease-out"
            enter-from-class="opacity-0 translate-x-4"
            enter-to-class="opacity-100 translate-x-0"
            leave-active-class="transition-all duration-200 ease-in"
            leave-from-class="opacity-100 translate-x-0"
            leave-to-class="opacity-0 -translate-x-4"
          >
            <div :key="currentStep" class="step-content">
              <!-- 图标 -->
              <div class="step-icon">
                <component :is="currentStepData.icon" class="w-16 h-16" />
              </div>

              <!-- 标题和描述 -->
              <div class="step-text">
                <h2 class="step-title">{{ currentStepData.title }}</h2>
                <p class="step-description">{{ currentStepData.description }}</p>
              </div>

              <!-- 特性列表 -->
              <div v-if="currentStepData.features" class="step-features">
                <div
                  v-for="(feature, index) in currentStepData.features"
                  :key="index"
                  class="feature-item"
                >
                  <div class="feature-icon">{{ feature.icon }}</div>
                  <div class="feature-text">
                    <div class="feature-title">{{ feature.title }}</div>
                    <div class="feature-description">{{ feature.description }}</div>
                  </div>
                </div>
              </div>

              <!-- 设置表单 -->
              <div v-if="currentStepData.type === 'settings'" class="step-settings">
                <div class="setting-item">
                  <label class="setting-label">标签分隔符</label>
                  <input
                    v-model="tagSeparator"
                    type="text"
                    class="setting-input"
                    placeholder="例如: #"
                    maxlength="2"
                  />
                  <div class="setting-hint">
                    用于从书签标题中提取标签，例如："Vue教程#前端#学习"
                  </div>
                </div>
              </div>
            </div>
          </Transition>

          <!-- 操作按钮 -->
          <div class="onboarding-actions">
            <button
              v-if="currentStep > 0"
              class="btn btn-ghost"
              @click="previousStep"
            >
              上一步
            </button>

            <div class="flex-1"></div>

            <button
              class="btn btn-ghost"
              @click="handleSkip"
            >
              跳过
            </button>

            <button
              class="btn btn-primary"
              @click="nextStep"
            >
              {{ isLastStep ? '开始使用' : '下一步' }}
            </button>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useSettingsStore } from '../stores/settings'
import { useTagStore } from '../stores/tags'

// Props
interface Props {
  autoStart?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  autoStart: true
})

// Emits
const emit = defineEmits<{
  'complete': []
  'skip': []
}>()

// Stores
const settingsStore = useSettingsStore()
const tagStore = useTagStore()

// 响应式状态
const isVisible = ref(false)
const currentStep = ref(0)
const tagSeparator = ref('#')

// 引导步骤数据
const steps = [
  {
    type: 'welcome',
    icon: 'BookmarkIcon',
    title: '欢迎使用书签管理器',
    description: '让我们花几分钟时间了解如何使用这个强大的书签管理工具。',
    features: [
      {
        icon: '🏷️',
        title: '智能标签系统',
        description: '自动提取和管理书签标签'
      },
      {
        icon: '🔍',
        title: '强大搜索功能',
        description: '支持中文拼音搜索'
      },
      {
        icon: '🎨',
        title: '美观界面',
        description: '现代化的用户界面设计'
      }
    ]
  },
  {
    type: 'features',
    icon: 'SparklesIcon',
    title: '核心功能介绍',
    description: '了解书签管理器的主要功能特性。',
    features: [
      {
        icon: '📁',
        title: '文件夹管理',
        description: '左侧面板可以浏览和管理书签文件夹'
      },
      {
        icon: '🏷️',
        title: '标签筛选',
        description: '使用标签快速筛选和组织书签'
      },
      {
        icon: '🔍',
        title: '快速搜索',
        description: '支持标题、URL和拼音搜索'
      },
      {
        icon: '🎯',
        title: '右键菜单',
        description: '右键点击书签获取更多操作选项'
      }
    ]
  },
  {
    type: 'settings',
    icon: 'CogIcon',
    title: '基础设置',
    description: '配置一些基本设置来个性化您的体验。'
  },
  {
    type: 'complete',
    icon: 'CheckCircleIcon',
    title: '设置完成！',
    description: '您已经准备好开始使用书签管理器了。我们将在后台同步您的书签数据。',
    features: [
      {
        icon: '⌨️',
        title: '快捷键',
        description: 'Cmd/Ctrl + / 快速打开搜索'
      },
      {
        icon: '🎨',
        title: '主题切换',
        description: '点击右上角图标切换明暗主题'
      },
      {
        icon: '⚙️',
        title: '更多设置',
        description: '在设置面板中探索更多功能'
      }
    ]
  }
]

// 计算属性
const currentStepData = computed(() => steps[currentStep.value])
const isLastStep = computed(() => currentStep.value === steps.length - 1)

// 方法
const show = () => {
  isVisible.value = true
}

const hide = () => {
  isVisible.value = false
}

const nextStep = async () => {
  if (currentStepData.value.type === 'settings') {
    // 保存设置
    await saveSettings()
  }

  if (isLastStep.value) {
    await complete()
  } else {
    currentStep.value++
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
  }
}

const handleSkip = () => {
  emit('skip')
  hide()
}

const complete = async () => {
  try {
    // 标记引导完成
    await settingsStore.completeOnboarding()
    
    emit('complete')
    hide()
  } catch (error) {
    console.error('完成引导失败:', error)
  }
}

const saveSettings = async () => {
  try {
    // 保存标签分隔符
    if (tagSeparator.value.trim()) {
      tagStore.setTagSeparator(tagSeparator.value.trim())
    }
  } catch (error) {
    console.error('保存设置失败:', error)
  }
}

// 暴露方法给父组件
defineExpose({
  show,
  hide
})

// 生命周期
onMounted(async () => {
  if (props.autoStart) {
    // 等待设置加载完成
    await settingsStore.loadSettings()

    // 检查是否是首次运行且未完成引导
    if (settingsStore.settings.isFirstRun && !settingsStore.settings.onboardingCompleted) {
      setTimeout(() => {
        show()
      }, 1000) // 延迟1秒显示，让页面先加载完成
    }
  }
})
</script>

<style scoped>
.onboarding-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.overlay-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
}

.onboarding-container {
  position: relative;
  width: 100%;
  max-width: 600px;
  background: white;
  border-radius: 1rem;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  overflow: hidden;
}

.progress-indicator {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem 2rem 0;
}

.progress-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  background: #e5e7eb;
  transition: all 0.3s ease;
}

.dot-active {
  background: #3b82f6;
  transform: scale(1.2);
}

.dot-completed {
  background: #10b981;
}

.step-content {
  padding: 2rem;
  text-align: center;
}

.step-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: #3b82f6;
}

.step-text {
  margin-bottom: 2rem;
}

.step-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #111827;
  margin-bottom: 1rem;
}

.step-description {
  font-size: 1.125rem;
  color: #6b7280;
  line-height: 1.6;
}

.step-features {
  display: grid;
  gap: 1rem;
  text-align: left;
  margin-bottom: 2rem;
}

.feature-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 0.75rem;
}

.feature-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.feature-text {
  flex: 1;
}

.feature-title {
  font-weight: 600;
  color: #111827;
  margin-bottom: 0.25rem;
}

.feature-description {
  font-size: 0.875rem;
  color: #6b7280;
}

.step-settings {
  text-align: left;
  margin-bottom: 2rem;
}

.setting-item {
  margin-bottom: 1.5rem;
}

.setting-label {
  display: block;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.setting-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.setting-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.setting-hint {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.onboarding-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2rem 2rem;
  border-top: 1px solid #e5e7eb;
}

.flex-1 {
  flex: 1;
}

/* 暗色主题 */
[data-theme="dark"] .onboarding-container {
  background: #1f2937;
}

[data-theme="dark"] .step-title {
  color: #f9fafb;
}

[data-theme="dark"] .step-description {
  color: #d1d5db;
}

[data-theme="dark"] .feature-item {
  background: #374151;
}

[data-theme="dark"] .feature-title {
  color: #f9fafb;
}

[data-theme="dark"] .feature-description {
  color: #d1d5db;
}

[data-theme="dark"] .setting-label {
  color: #e5e7eb;
}

[data-theme="dark"] .setting-input {
  background: #374151;
  border-color: #4b5563;
  color: #f9fafb;
}

[data-theme="dark"] .setting-input:focus {
  border-color: #3b82f6;
}

[data-theme="dark"] .setting-hint {
  color: #9ca3af;
}

[data-theme="dark"] .onboarding-actions {
  border-top-color: #374151;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .onboarding-overlay {
    padding: 1rem;
  }
  
  .step-content {
    padding: 1.5rem;
  }
  
  .step-title {
    font-size: 1.5rem;
  }
  
  .step-description {
    font-size: 1rem;
  }
  
  .onboarding-actions {
    flex-wrap: wrap;
    gap: 0.75rem;
  }
}
</style>
