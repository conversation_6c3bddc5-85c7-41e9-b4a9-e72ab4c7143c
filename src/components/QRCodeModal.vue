<template>
  <!-- 使用 daisyUI modal 组件 -->
  <div
    v-if="isOpen"
    class="modal modal-open modal-with-drawer-offset"
    @click="$emit('close')"
  >
    <div class="modal-box w-96 max-w-md" @click.stop>
      <!-- 关闭按钮 -->
      <button class="btn btn-sm btn-circle btn-ghost absolute right-2 top-2" @click="$emit('close')">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </button>

      <!-- 标题 -->
      <h3 class="font-bold text-lg mb-4">扫码访问</h3>

      <!-- 书签标题 -->
      <div class="text-center mb-6">
        <p class="text-sm text-base-content/70 truncate" :title="bookmark?.title">
          {{ bookmark?.title }}
        </p>
      </div>

      <!-- 二维码容器 -->
      <div class="flex justify-center mb-6">
        <div
          ref="qrContainer"
          class="qr-code-container p-4 rounded-2xl bg-base-200"
        >
          <!-- 二维码将在这里生成 -->
        </div>
      </div>

      <!-- URL显示 -->
      <div class="mb-6">
        <div class="p-3 rounded-xl text-xs text-center break-all bg-base-200 text-base-content/70">
          {{ bookmark?.url }}
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="modal-action">
        <button class="btn btn-ghost" @click="$emit('close')">
          关闭
        </button>
        <button class="btn btn-primary" @click="copyUrl">
          复制链接
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick } from 'vue'
import type { BookmarkWithMeta } from '../types'
// @ts-ignore
import QRCode from 'qrcode-generator'

const props = defineProps<{
  isOpen: boolean
  bookmark: BookmarkWithMeta | null
}>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

const qrContainer = ref<HTMLElement | null>(null)

// 生成二维码
const generateQRCode = async () => {
  if (!props.bookmark?.url || !qrContainer.value) return

  try {
    // 清空容器
    qrContainer.value.innerHTML = ''

    // 创建二维码
    const qr = QRCode(0, 'M') // 0表示自动选择版本，'M'表示中等纠错级别
    qr.addData(props.bookmark.url)
    qr.make()

    // 创建SVG元素
    const size = 200
    const cellSize = size / qr.getModuleCount()
    const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg')
    svg.setAttribute('width', size.toString())
    svg.setAttribute('height', size.toString())
    svg.setAttribute('viewBox', `0 0 ${size} ${size}`)
    svg.style.borderRadius = '12px'

    // 添加白色背景
    const background = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
    background.setAttribute('width', size.toString())
    background.setAttribute('height', size.toString())
    background.setAttribute('fill', '#ffffff')
    background.setAttribute('rx', '12')
    svg.appendChild(background)

    // 生成二维码模块
    for (let row = 0; row < qr.getModuleCount(); row++) {
      for (let col = 0; col < qr.getModuleCount(); col++) {
        if (qr.isDark(row, col)) {
          const rect = document.createElementNS('http://www.w3.org/2000/svg', 'rect')
          rect.setAttribute('x', (col * cellSize).toString())
          rect.setAttribute('y', (row * cellSize).toString())
          rect.setAttribute('width', cellSize.toString())
          rect.setAttribute('height', cellSize.toString())
          rect.setAttribute('fill', '#1f2937')
          
          // 添加圆角效果
          const radius = cellSize * 0.2
          rect.setAttribute('rx', radius.toString())
          rect.setAttribute('ry', radius.toString())
          
          svg.appendChild(rect)
        }
      }
    }

    qrContainer.value.appendChild(svg)
  } catch (error) {
    console.error('生成二维码失败:', error)
    if (qrContainer.value) {
      qrContainer.value.innerHTML = '<div class="text-center text-red-500 text-sm">二维码生成失败</div>'
    }
  }
}

// 复制URL
const copyUrl = async () => {
  if (!props.bookmark?.url) return

  try {
    await navigator.clipboard.writeText(props.bookmark.url)
    // 可以添加一个简单的提示
    const button = event?.target as HTMLButtonElement
    if (button) {
      const originalText = button.textContent
      button.textContent = '已复制!'
      setTimeout(() => {
        button.textContent = originalText
      }, 1500)
    }
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 监听模态框打开状态
watch(() => props.isOpen, async (isOpen) => {
  if (isOpen && props.bookmark?.url) {
    await nextTick()
    generateQRCode()
  }
})
</script>

<style scoped>
/* 使用daisyUI样式，保留必要的自定义样式 */
.qr-code-container {
  display: inline-block;
  transition: all 0.3s ease;
}

.qr-code-container:hover {
  transform: scale(1.02);
}
</style>
