<template>
  <!-- 遮罩层 -->
  <Teleport to="body">
    <Transition
      enter-active-class="transition-opacity duration-300 ease-out"
      leave-active-class="transition-opacity duration-300 ease-in"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div
        v-if="isOpen"
        class="fixed inset-0 z-[10200] bg-black/20"
        @click="close"
      ></div>
    </Transition>

    <!-- 右侧抽屉面板 -->
    <Transition
      enter-active-class="transition-transform duration-300 ease-out"
      leave-active-class="transition-transform duration-300 ease-in"
      enter-from-class="translate-x-full"
      enter-to-class="translate-x-0"
      leave-from-class="translate-x-0"
      leave-to-class="translate-x-full"
    >
      <div
        v-if="isOpen"
        class="fixed top-0 right-0 h-screen z-[10201] bg-base-100 shadow-xl"
        style="width: 480px;"
        @click.stop
      >
        <div class="h-full flex flex-col">
          <!-- 标题栏 -->
          <div class="flex items-center justify-between p-6 border-b border-base-300">
            <div class="flex items-center gap-3">
              <div class="p-2 bg-primary/10 rounded-full">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6 text-primary">
                  <path stroke-linecap="round" stroke-linejoin="round" d="M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.324.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 011.37.49l1.296 2.247a1.125 1.125 0 01-.26 1.431l-1.003.827c-.293.24-.438.613-.431.992a6.759 6.759 0 010 .255c-.007.378.138.75.43.99l1.005.828c.424.35.534.954.26 1.43l-1.298 2.247a1.125 1.125 0 01-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.57 6.57 0 01-.22.128c-.331.183-.581.495-.644.869l-.213 1.28c-.09.543-.56.941-1.11.941h-2.594c-.55 0-1.02-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 01-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 01-1.369-.49l-1.297-2.247a1.125 1.125 0 01.26-1.431l1.004-.827c.292-.24.437-.613.43-.992a6.932 6.932 0 010-.255c.007-.378-.138-.75-.43-.99l-1.004-.828a1.125 1.125 0 01-.26-1.43l1.297-2.247a1.125 1.125 0 011.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.087.22-.128.332-.183.582-.495.644-.869l.214-1.281z" />
                  <path stroke-linecap="round" stroke-linejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              </div>
              <div>
                <h3 class="text-xl font-bold">设置</h3>
                <p class="text-sm text-base-content/60">个性化您的书签管理器</p>
              </div>
            </div>
            <button
              @click="close"
              class="btn btn-ghost btn-circle btn-sm"
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>

          <!-- Tab 导航 -->
          <div class="tabs tabs-boxed mx-6 mt-4">
            <button
              class="tab"
              :class="{ 'tab-active': activeTab === 'display' }"
              @click="activeTab = 'display'"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              显示设置
            </button>
            <button
              class="tab"
              :class="{ 'tab-active': activeTab === 'tags' }"
              @click="activeTab = 'tags'"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5l2 2h6a2 2 0 012 2v4a2 2 0 01-2 2H9l-2-2H3a2 2 0 01-2-2V5a2 2 0 012-2z" />
              </svg>
              标签设置
            </button>
            <button
              class="tab"
              :class="{ 'tab-active': activeTab === 'other' }"
              @click="activeTab = 'other'"
            >
              <svg class="w-4 h-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4" />
              </svg>
              其他设置
            </button>
          </div>

          <!-- Tab 内容区域 -->
          <div class="flex-1 overflow-y-auto p-6">
            <!-- 显示设置 Tab -->
            <div v-if="activeTab === 'display'" class="space-y-6">
              <!-- 界面显示 -->
              <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                  <h3 class="card-title text-lg mb-4 text-primary">界面显示</h3>

                  <!-- 默认图标 -->
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-medium">默认图标地址</span>
                    </label>
                    <div class="label">
                      <span class="label-text-alt">当书签网站无法获取图标时显示的默认图标</span>
                    </div>
                    <div class="input-group">
                      <input
                        v-model="defaultIcon"
                        type="text"
                        placeholder="图片链接 https://..."
                        class="input input-bordered flex-1 focus:input-primary"
                      />
                      <button
                        v-if="defaultIcon.trim()"
                        @click="defaultIcon = ''"
                        class="btn btn-ghost btn-square hover:btn-error"
                        title="清除"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- 书签卡片样式 -->
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-medium">书签卡片样式</span>
                    </label>
                    <div class="label">
                      <span class="label-text-alt">方形卡片8×3布局，矩形卡片5×3布局</span>
                    </div>
                    <div class="btn-group">
                      <button
                        @click="setBookmarkStyle('square')"
                        :class="['btn', bookmarkStyle === 'square' ? 'btn-active' : 'btn-outline']"
                        title="方形卡片"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <rect x="2" y="2" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="7" y="2" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="12" y="2" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="17" y="2" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="2" y="7" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="7" y="7" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="12" y="7" width="4" height="4" rx="1" fill="currentColor"/>
                          <rect x="17" y="7" width="4" height="4" rx="1" fill="currentColor"/>
                        </svg>
                        方形
                      </button>
                      <button
                        @click="setBookmarkStyle('rectangle')"
                        :class="['btn', bookmarkStyle === 'rectangle' ? 'btn-active' : 'btn-outline']"
                        title="矩形卡片"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <rect x="2" y="3" width="8" height="4" rx="1" fill="currentColor"/>
                          <rect x="12" y="3" width="8" height="4" rx="1" fill="currentColor"/>
                          <rect x="2" y="9" width="8" height="4" rx="1" fill="currentColor"/>
                          <rect x="12" y="9" width="8" height="4" rx="1" fill="currentColor"/>
                          <rect x="2" y="15" width="8" height="4" rx="1" fill="currentColor"/>
                          <rect x="12" y="15" width="8" height="4" rx="1" fill="currentColor"/>
                        </svg>
                        矩形
                      </button>
                    </div>
                  </div>

                  <!-- 图标形状 -->
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-medium">图标形状</span>
                    </label>
                    <div class="label">
                      <span class="label-text-alt">圆形更现代，圆角矩形更传统</span>
                    </div>
                    <div class="btn-group">
                      <button
                        @click="setIconShape('circle')"
                        :class="['btn', iconShape === 'circle' ? 'btn-active' : 'btn-outline']"
                        title="圆形"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <circle cx="12" cy="12" r="8" fill="currentColor"/>
                        </svg>
                        圆形
                      </button>
                      <button
                        @click="setIconShape('rounded')"
                        :class="['btn', iconShape === 'rounded' ? 'btn-active' : 'btn-outline']"
                        title="圆角矩形"
                      >
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                          <rect x="4" y="4" width="16" height="16" rx="4" fill="currentColor"/>
                        </svg>
                        圆角
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 标签设置 Tab -->
            <div v-if="activeTab === 'tags'" class="space-y-6">
              <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                  <h3 class="card-title text-lg mb-4 text-primary">标签功能</h3>

                  <!-- 启用标签功能 -->
                  <div class="form-control">
                    <label class="label cursor-pointer">
                      <div>
                        <span class="label-text font-medium">启用标签功能</span>
                        <div class="label-text-alt">关闭后将隐藏所有标签相关功能</div>
                      </div>
                      <input v-model="tagsEnabled" type="checkbox" class="toggle toggle-primary" />
                    </label>
                  </div>

                  <!-- 标签分隔符 -->
                  <div v-if="tagsEnabled" class="form-control">
                    <label class="label">
                      <span class="label-text font-medium">标签分隔符</span>
                    </label>
                    <div class="label">
                      <span class="label-text-alt">用于分隔书签名称和标签</span>
                    </div>
                    <div class="input-group max-w-xs">
                      <input
                        v-model="tagSeparator"
                        type="text"
                        placeholder="#"
                        maxlength="2"
                        class="input input-bordered w-20 focus:input-primary"
                      />
                      <button
                        v-if="tagSeparator.trim() && tagSeparator !== '#'"
                        @click="tagSeparator = '#'"
                        class="btn btn-ghost btn-square hover:btn-error"
                        title="重置为默认"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                          <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- 显示标签圆点 -->
                  <div v-if="tagsEnabled" class="form-control">
                    <label class="label cursor-pointer">
                      <div>
                        <span class="label-text font-medium">显示标签圆点</span>
                        <div class="label-text-alt">在书签卡片上显示彩色标签圆点</div>
                      </div>
                      <input v-model="showTagDots" type="checkbox" class="toggle toggle-primary" />
                    </label>
                  </div>
                </div>
              </div>
            </div>

            <!-- 其他设置 Tab -->
            <div v-if="activeTab === 'other'" class="space-y-6">
              <!-- 数据清理 -->
              <div class="card bg-base-100 shadow-sm border border-base-300">
                <div class="card-body">
                  <h3 class="card-title text-lg mb-4 text-primary">数据清理</h3>

                  <!-- 清理图标缓存 -->
                  <div class="form-control">
                    <label class="label">
                      <span class="label-text font-medium">清理图标缓存</span>
                    </label>
                    <div class="label">
                      <span class="label-text-alt">清理所有缓存的网站图标，下次访问时重新获取</span>
                    </div>
                    <div class="card-actions">
                      <button
                        @click="clearIconCache"
                        :disabled="isClearing"
                        class="btn btn-error btn-outline"
                        :class="{ 'loading': isClearing }"
                      >
                        {{ isClearing ? '清理中...' : '清理缓存' }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useTagSettings } from '../composables/useTagSettings'
import { useBookmarkDisplay } from '../composables/useBookmarkDisplay'

interface Props {
  isOpen: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  (e: 'close'): void
}>()

// 当前激活的 Tab
const activeTab = ref<'display' | 'tags' | 'other'>('display')

// 标签功能管理
const { isTagsEnabled, setTagsEnabled } = useTagSettings()
const tagsEnabled = ref(true)

// 书签显示管理
const { bookmarkStyle, iconShape, setBookmarkStyle, setIconShape } = useBookmarkDisplay()

const defaultIcon = ref('')
const tagSeparator = ref('#')
const showTagDots = ref(true)

// 清理状态
const isClearing = ref(false)

// 关闭面板
function close() {
  emit('close')
}

// 清理图标缓存
async function clearIconCache() {
  isClearing.value = true
  try {
    if (typeof chrome !== 'undefined' && chrome.storage) {
      // Chrome 环境：清理 chrome.storage.local
      const keys = await new Promise<string[]>((resolve) => {
        chrome.storage.local.get(null, (items) => {
          const iconKeys = Object.keys(items).filter(key => key.startsWith('icon_'))
          resolve(iconKeys)
        })
      })
      
      if (keys.length > 0) {
        await new Promise<void>((resolve) => {
          chrome.storage.local.remove(keys, () => {
            resolve()
          })
        })
      }
    } else {
      // 开发环境：清理 localStorage
      const keys = Object.keys(localStorage).filter(key => key.startsWith('icon_'))
      keys.forEach(key => localStorage.removeItem(key))
    }
    
    console.log('图标缓存清理完成')
  } catch (error) {
    console.error('清理图标缓存失败:', error)
  } finally {
    isClearing.value = false
  }
}

// 初始化设置
onMounted(() => {
  // 加载设置
  defaultIcon.value = localStorage.getItem('defaultIcon') || ''
  tagSeparator.value = localStorage.getItem('tagSeparator') || '#'
  tagsEnabled.value = isTagsEnabled.value
  showTagDots.value = localStorage.getItem('showTagDots') !== 'false'
})

// 监听设置变化，立即保存
watch([defaultIcon, tagSeparator, tagsEnabled, showTagDots], () => {
  // 保存基础设置
  localStorage.setItem('defaultIcon', defaultIcon.value.trim())
  localStorage.setItem('tagSeparator', tagSeparator.value || '#')
  localStorage.setItem('showTagDots', showTagDots.value.toString())
  setTagsEnabled(tagsEnabled.value)

  // 触发设置变更事件
  window.dispatchEvent(new CustomEvent('settings-changed', {
    detail: {
      bookmarkStyle: bookmarkStyle.value,
      iconShape: iconShape.value,
      tagsEnabled: tagsEnabled.value
    }
  }))
})
</script>

<style scoped>
/* 自定义滚动条样式 */
.overflow-y-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-y-auto::-webkit-scrollbar-track {
  background: transparent;
}

.overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}

.overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

/* 暗色主题滚动条 */
[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.2);
}

[data-theme="dark"] .overflow-y-auto::-webkit-scrollbar-thumb:hover {
  background: rgba(255, 255, 255, 0.3);
}
</style>
