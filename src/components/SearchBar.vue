<template>
  <div class="search-input-container">
    <!-- 搜索输入框 -->
    <div class="search-input-wrapper">
      <!-- 搜索图标 -->
      <div class="search-icon">
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
          <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
          <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
        </svg>
      </div>

      <input
        ref="inputRef"
        :value="modelValue"
        @input="handleInput"
        @keydown="handleKeydown"
        @focus="handleFocus"
        @blur="handleBlur"
        type="text"
        placeholder="搜索书签（支持拼音首字母/全拼/英文混合搜索）..."
        class="search-input"
      />

      <div class="search-actions">
        <!-- 清除按钮 -->
        <button
          v-if="modelValue.trim()"
          @click="clearSearch"
          class="clear-button"
          title="清除搜索"
        >
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>

        <!-- 快捷键提示 -->
        <div v-if="!modelValue.trim()" class="shortcut-hint">
          <kbd class="shortcut-key">{{ modifierKey }}</kbd>
          <kbd class="shortcut-key">/</kbd>
        </div>
      </div>
    </div>

    <!-- 搜索结果 -->
    <SearchResults
      ref="searchResultsRef"
      :search-query="modelValue"
      :bookmarks="searchResults || []"
      :visible="showSearchResults"
      @open-bookmark="handleOpenBookmark"
      @close="closeSearchResults"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import SearchResults from './SearchResults.vue'
import type { BookmarkWithMeta } from '../types'

const props = defineProps<{
  modelValue: string
  searchResults?: BookmarkWithMeta[]
}>()

const emit = defineEmits(['update:modelValue', 'blur', 'search-focus'])

const inputRef = ref<HTMLInputElement | null>(null)
const searchResultsRef = ref<InstanceType<typeof SearchResults> | null>(null)

// 搜索结果状态
const showSearchResults = ref(false)

// 检测操作系统并显示对应的修饰键
const modifierKey = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMac = userAgent.includes('mac') || userAgent.includes('darwin')
  return isMac ? '⌘' : 'Ctrl'
})

// 搜索相关方法
const handleInput = (event: Event) => {
  const value = (event.target as HTMLInputElement).value
  emit('update:modelValue', value)

  // 显示搜索结果
  if (value.trim()) {
    showSearchResults.value = true
  } else {
    showSearchResults.value = false
  }
}

const handleFocus = () => {
  emit('search-focus')
  // 如果有搜索内容，显示搜索结果
  if (props.modelValue.trim()) {
    showSearchResults.value = true
  }
}

const handleBlur = (event: FocusEvent) => {
  // 延迟关闭，允许点击搜索结果
  setTimeout(() => {
    if (!searchResultsRef.value?.$el?.contains(event.relatedTarget as Node)) {
      showSearchResults.value = false
      // 不清空搜索内容，只触发失焦事件
      emit('blur')
    }
  }, 150)
}

const handleKeydown = (event: KeyboardEvent) => {
  // 如果搜索结果可见，让搜索结果组件处理键盘事件
  if (showSearchResults.value && searchResultsRef.value) {
    searchResultsRef.value.handleKeydown(event)
    return
  }

  // ESC 键处理
  if (event.key === 'Escape') {
    showSearchResults.value = false
    emit('blur')
  }
}

// 搜索结果相关方法
const handleOpenBookmark = (bookmark: BookmarkWithMeta) => {
  console.log('打开书签:', bookmark.title)
  // 清空搜索框
  emit('update:modelValue', '')
  showSearchResults.value = false
}

const closeSearchResults = () => {
  showSearchResults.value = false
}

// 清除搜索
const clearSearch = () => {
  emit('update:modelValue', '')
  showSearchResults.value = false
  inputRef.value?.focus()
}

// 暴露聚焦方法给父组件
const focusInput = () => {
  inputRef.value?.focus()
}

defineExpose({
  focusInput
})
</script>

<style scoped>
/* 搜索框容器 */
.search-input-container {
  position: fixed; /* 改为relative，让外层控制定位 */
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  /* 继承外部的滚动隐藏样式 */
}

/* 搜索输入框包装器 */
.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(var(--bg-base-100, 255 255 255), 1);
  border: 1px solid rgba(var(--border-base-300, 209 213 219), 1);
  border-radius: 0.75rem;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 3px rgba(0, 0, 0, 0.04);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.search-input-wrapper:hover {
  border-color: rgba(var(--border-base-400, 156 163 175), 1);
  box-shadow:
    0 4px 12px rgba(0, 0, 0, 0.08),
    0 2px 4px rgba(0, 0, 0, 0.06);
}

.search-input-wrapper:focus-within {
  border-color: rgba(var(--bg-primary, 59 130 246), 0.5);
  box-shadow:
    0 4px 16px rgba(59, 130, 246, 0.15),
    0 2px 6px rgba(59, 130, 246, 0.1),
    0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 搜索图标 */
.search-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 1rem;
  color: var(--text-base-content-secondary, #6b7280);
  flex-shrink: 0;
}

/* 搜索输入框 */
.search-input {
  flex: 1;
  padding: 0.875rem 0;
  border: none;
  outline: none;
  background: transparent;
  font-size: 0.875rem;
  color: var(--text-base-content, #1f2937);
}

.search-input::placeholder {
  color: var(--text-base-content-secondary, #6b7280);
  opacity: 0.8;
}

/* 搜索操作区域 */
.search-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0 1rem;
  flex-shrink: 0;
}

/* 清除按钮 */
.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.75rem;
  height: 1.75rem;
  border-radius: 50%;
  border: none;
  background: rgba(var(--bg-base-200, 248 250 252), 1);
  color: var(--text-base-content-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-button:hover {
  background: rgba(var(--bg-base-300, 229 231 235), 1);
  color: var(--text-base-content, #1f2937);
  transform: scale(1.05);
}

/* 快捷键提示 */
.shortcut-hint {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.shortcut-key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.25rem;
  height: 1.25rem;
  padding: 0 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  color: var(--text-base-content-secondary, #6b7280);
  background: rgba(var(--bg-base-200, 248 250 252), 1);
  border: 1px solid rgba(var(--border-base-300, 209 213 219), 1);
  border-radius: 0.25rem;
  line-height: 1;
}

/* 暗色主题适配 */
[data-theme="dark"] .search-input-wrapper {
  --bg-base-100: 30 41 59;
  --bg-base-200: 51 65 85;
  --bg-base-300: 71 85 105;
  --border-base-300: 71 85 105;
  --border-base-400: 100 116 139;
  --text-base-content: 248 250 252;
  --text-base-content-secondary: 148 163 184;
  --bg-primary: 147 197 253;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-input-container {
    max-width: 100%;
  }

  .search-input {
    font-size: 0.8rem;
    padding: 0.75rem 0;
  }

  .search-icon {
    padding: 0 0.75rem;
  }

  .search-actions {
    padding: 0 0.75rem;
  }

  .search-input::placeholder {
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .search-input::placeholder {
    content: "搜索书签...";
  }

  .shortcut-hint {
    display: none;
  }
}
</style>
