<template>
  <!-- 使用 daisyUI dropdown 和 menu 组件 -->
  <div v-if="isSearching && searchResults.length > 0" class="dropdown-content z-[1000] menu bg-base-100 rounded-box w-full shadow-lg border border-base-300 mt-2 max-h-[70vh] overflow-y-auto">
    <!-- 搜索结果头部 -->
    <div class="menu-title px-4 py-2 border-b border-base-300">
      <div class="flex justify-between items-center">
        <span class="font-semibold text-sm">搜索结果 ({{ searchResults.length }})</span>
        <span class="text-xs opacity-60">{{ modifierKey }}+数字键快速访问</span>
      </div>
    </div>

    <!-- 搜索结果列表 - 纵向排列 -->
    <ul class="p-0 flex flex-col">
      <li
        v-for="(bookmark, index) in displayedResults"
        :key="bookmark.id"
        :class="{ 'bordered': highlightedIndex === index }"
        @mouseenter="highlightedIndex = index"
        class="w-full"
      >
        <a
          class="flex items-center gap-3 px-4 py-3 hover:bg-base-200 cursor-pointer w-full"
          :class="{ 'active': highlightedIndex === index }"
          @click="openBookmark(bookmark)"
        >
          <!-- 快捷键数字 -->
          <div class="flex-shrink-0">
            <kbd v-if="index < 9" class="kbd kbd-sm">{{ modifierKey }}+{{ index + 1 }}</kbd>
            <div v-else class="w-12"></div>
          </div>

          <!-- 书签图标 -->
          <div class="avatar">
            <div class="w-8 h-8 rounded">
              <img
                v-if="bookmark.icon"
                :src="bookmark.icon"
                :alt="bookmark.title"
                @error="handleIconError"
              />
              <div v-else class="flex items-center justify-center text-lg">🔖</div>
            </div>
          </div>

          <!-- 书签信息 -->
          <div class="flex-1 min-w-0">
            <div class="font-medium text-sm truncate" v-html="highlightText(bookmark.title || '', searchQuery)"></div>
            <div class="text-xs opacity-60 truncate" v-html="highlightText(bookmark.url || '', searchQuery)"></div>
            <!-- 标签 -->
            <div v-if="bookmark.tags && bookmark.tags.length > 0" class="flex gap-1 mt-1 flex-wrap">
              <div
                v-for="tag in bookmark.tags"
                :key="tag"
                class="badge badge-xs"
                :style="{ backgroundColor: getTagColor(tag), color: 'white' }"
              >
                {{ tag }}
              </div>
            </div>
          </div>

          <!-- 匹配类型指示器 -->
          <div class="flex-shrink-0 opacity-60">
            <span :title="getMatchTypeTitle(bookmark)">
              {{ getMatchTypeIcon(bookmark) }}
            </span>
          </div>
        </a>
      </li>
    </ul>

    <!-- 更多结果提示 -->
    <div v-if="searchResults.length > maxDisplayResults" class="px-4 py-2 text-center text-xs opacity-60 border-t border-base-300">
      还有 {{ searchResults.length - maxDisplayResults }} 个结果...
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { usePinyinSearch } from '../composables/usePinyinSearch'
import { useTagColors } from '../composables/useTagColors'
import type { BookmarkWithMeta } from '../types'

const props = defineProps<{
  searchQuery: string
  bookmarks: BookmarkWithMeta[]
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'open-bookmark', bookmark: BookmarkWithMeta): void
  (e: 'close'): void
}>()

const { highlightMatch } = usePinyinSearch()
const { getTagColor } = useTagColors()

// 搜索结果状态
const highlightedIndex = ref(0)
const maxDisplayResults = 9 // 最多显示9个结果，对应数字键1-9

// 检测操作系统并显示对应的修饰键
const modifierKey = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMac = userAgent.includes('mac') || userAgent.includes('darwin')
  return isMac ? '⌘' : 'Ctrl'
})

// 是否正在搜索
const isSearching = computed(() => {
  return props.visible && props.searchQuery.trim().length > 0
})

// 搜索结果
const searchResults = computed(() => {
  if (!isSearching.value) return []
  return props.bookmarks
})

// 显示的搜索结果（限制数量）
const displayedResults = computed(() => {
  return searchResults.value.slice(0, maxDisplayResults)
})

// 高亮搜索关键词 - 使用新的拼音搜索高亮功能
const highlightText = (text: string, query: string): string => {
  if (!query.trim()) return text

  // 使用 usePinyinSearch 的 highlightMatch 函数
  const highlighted = highlightMatch(text, query)

  // 将 <mark> 标签替换为带样式类的标签
  return highlighted.replace(/<mark>/g, '<mark class="search-highlight">').replace(/<\/mark>/g, '</mark>')
}

// 获取匹配类型图标
const getMatchTypeIcon = (bookmark: BookmarkWithMeta): string => {
  // 这里可以根据匹配类型返回不同的图标
  // 暂时返回一个通用图标
  return '🎯'
}

// 获取匹配类型标题
const getMatchTypeTitle = (bookmark: BookmarkWithMeta): string => {
  return '匹配结果'
}

// 打开书签
const openBookmark = (bookmark: BookmarkWithMeta) => {
  if (bookmark.url) {
    window.open(bookmark.url, '_blank')
    emit('open-bookmark', bookmark)
    emit('close')
  }
}

// 处理图标加载错误
const handleIconError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!isSearching.value || displayedResults.value.length === 0) return

  // 数字键 1-9 快速访问（需要按住 Cmd/Ctrl，只支持前9个结果）
  if (event.key >= '1' && event.key <= '9' && (event.metaKey || event.ctrlKey)) {
    event.preventDefault()
    const index = parseInt(event.key) - 1
    if (index < Math.min(displayedResults.value.length, 9)) {
      openBookmark(displayedResults.value[index])
    }
    return
  }

  // 方向键导航
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, displayedResults.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      if (displayedResults.value[highlightedIndex.value]) {
        openBookmark(displayedResults.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      event.preventDefault()
      emit('close')
      break
  }
}

// 重置高亮索引
watch(() => props.searchQuery, () => {
  highlightedIndex.value = 0
})

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 暴露键盘处理方法
defineExpose({
  handleKeydown
})
</script>

<style scoped>
/* 确保搜索结果纵向排列 */
.dropdown-content {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.dropdown-content ul {
  display: flex;
  flex-direction: column;
  width: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}

.dropdown-content li {
  display: block;
  width: 100%;
  margin: 0;
  padding: 0;
}

.dropdown-content li a {
  display: flex;
  width: 100%;
  text-decoration: none;
  border-radius: 0;
  transition: background-color 0.2s ease;
}

.dropdown-content li:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

[data-theme="dark"] .dropdown-content li:not(:last-child) {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

/* 使用daisyUI样式，保留必要的自定义样式 */
:deep(.search-highlight) {
  background: hsl(var(--p));
  color: hsl(var(--pc));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 600;
}

/* 优化标签显示 */
.badge {
  font-size: 0.625rem;
  padding: 0.125rem 0.375rem;
}
</style>
