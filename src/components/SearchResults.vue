<template>
  <!-- 搜索结果下拉框 -->
  <div v-if="isSearching && searchResults.length > 0" class="search-results-dropdown">
    <!-- 搜索结果头部 -->
    <div class="search-results-header">
      <div class="flex justify-between items-center">
        <span class="font-semibold text-sm text-gray-900">搜索结果 ({{ searchResults.length }})</span>
        <span class="text-xs text-gray-500">{{ modifierKey }}+数字键快速访问</span>
      </div>
    </div>

    <!-- 搜索结果列表 - 纵向排列 -->
    <ul class="search-results-list">
      <li
        v-for="(bookmark, index) in displayedResults"
        :key="bookmark.id"
        :class="{ 'search-result-highlighted': highlightedIndex === index }"
        @mouseenter="highlightedIndex = index"
        class="search-result-item"
      >
        <a
          class="search-result-link"
          :class="{ 'search-result-active': highlightedIndex === index }"
          @click="openBookmark(bookmark)"
        >
          <!-- 快捷键数字 -->
          <div class="flex-shrink-0">
            <kbd v-if="index < 9" class="search-shortcut-key">{{ modifierKey }}+{{ index + 1 }}</kbd>
            <div v-else class="w-12"></div>
          </div>

          <!-- 书签图标 -->
          <div class="search-result-avatar">
            <div class="search-result-icon">
              <img
                v-if="bookmark.icon"
                :src="bookmark.icon"
                :alt="bookmark.title"
                @error="handleIconError"
              />
              <div v-else class="flex items-center justify-center text-lg">🔖</div>
            </div>
          </div>

          <!-- 书签信息 -->
          <div class="flex-1 min-w-0">
            <div class="font-medium text-sm truncate" v-html="highlightText(bookmark.title || '', searchQuery)"></div>
            <div class="text-xs opacity-60 truncate" v-html="highlightText(bookmark.url || '', searchQuery)"></div>
            <!-- 标签 -->
            <div v-if="bookmark.tags && bookmark.tags.length > 0" class="flex gap-1 mt-1 flex-wrap">
              <div
                v-for="tag in bookmark.tags"
                :key="tag"
                class="search-result-tag"
                :style="{ backgroundColor: getTagColor(tag), color: 'white' }"
              >
                {{ tag }}
              </div>
            </div>
          </div>

          <!-- 匹配类型指示器 -->
          <div class="flex-shrink-0 opacity-60">
            <span :title="getMatchTypeTitle(bookmark)">
              {{ getMatchTypeIcon(bookmark) }}
            </span>
          </div>
        </a>
      </li>
    </ul>

    <!-- 更多结果提示 -->
    <div v-if="searchResults.length > maxDisplayResults" class="search-results-footer">
      还有 {{ searchResults.length - maxDisplayResults }} 个结果...
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { usePinyinSearch } from '../composables/usePinyinSearch'
import { useTagColors } from '../composables/useTagColors'
import type { BookmarkWithMeta } from '../types'

const props = defineProps<{
  searchQuery: string
  bookmarks: BookmarkWithMeta[]
  visible: boolean
}>()

const emit = defineEmits<{
  (e: 'open-bookmark', bookmark: BookmarkWithMeta): void
  (e: 'close'): void
}>()

const { highlightMatch } = usePinyinSearch()
const { getTagColor } = useTagColors()

// 搜索结果状态
const highlightedIndex = ref(0)
const maxDisplayResults = 9 // 最多显示9个结果，对应数字键1-9

// 检测操作系统并显示对应的修饰键
const modifierKey = computed(() => {
  const userAgent = navigator.userAgent.toLowerCase()
  const isMac = userAgent.includes('mac') || userAgent.includes('darwin')
  return isMac ? '⌘' : 'Ctrl'
})

// 是否正在搜索
const isSearching = computed(() => {
  return props.visible && props.searchQuery.trim().length > 0
})

// 搜索结果
const searchResults = computed(() => {
  if (!isSearching.value) return []
  return props.bookmarks
})

// 显示的搜索结果（限制数量）
const displayedResults = computed(() => {
  return searchResults.value.slice(0, maxDisplayResults)
})

// 高亮搜索关键词 - 使用新的拼音搜索高亮功能
const highlightText = (text: string, query: string): string => {
  if (!query.trim()) return text

  // 使用 usePinyinSearch 的 highlightMatch 函数
  const highlighted = highlightMatch(text, query)

  // 将 <mark> 标签替换为带样式类的标签
  return highlighted.replace(/<mark>/g, '<mark class="search-highlight">').replace(/<\/mark>/g, '</mark>')
}

// 获取匹配类型图标
const getMatchTypeIcon = (bookmark: BookmarkWithMeta): string => {
  // 这里可以根据匹配类型返回不同的图标
  // 暂时返回一个通用图标
  return '🎯'
}

// 获取匹配类型标题
const getMatchTypeTitle = (bookmark: BookmarkWithMeta): string => {
  return '匹配结果'
}

// 打开书签
const openBookmark = (bookmark: BookmarkWithMeta) => {
  if (bookmark.url) {
    window.open(bookmark.url, '_blank')
    emit('open-bookmark', bookmark)
    emit('close')
  }
}

// 处理图标加载错误
const handleIconError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.style.display = 'none'
}

// 键盘事件处理
const handleKeydown = (event: KeyboardEvent) => {
  if (!isSearching.value || displayedResults.value.length === 0) return

  // 数字键 1-9 快速访问（需要按住 Cmd/Ctrl，只支持前9个结果）
  if (event.key >= '1' && event.key <= '9' && (event.metaKey || event.ctrlKey)) {
    event.preventDefault()
    const index = parseInt(event.key) - 1
    if (index < Math.min(displayedResults.value.length, 9)) {
      openBookmark(displayedResults.value[index])
    }
    return
  }

  // 方向键导航
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault()
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, displayedResults.value.length - 1)
      break
    case 'ArrowUp':
      event.preventDefault()
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, 0)
      break
    case 'Enter':
      event.preventDefault()
      if (displayedResults.value[highlightedIndex.value]) {
        openBookmark(displayedResults.value[highlightedIndex.value])
      }
      break
    case 'Escape':
      event.preventDefault()
      emit('close')
      break
  }
}

// 重置高亮索引
watch(() => props.searchQuery, () => {
  highlightedIndex.value = 0
})

// 监听键盘事件
onMounted(() => {
  document.addEventListener('keydown', handleKeydown)
})

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown)
})

// 暴露键盘处理方法
defineExpose({
  handleKeydown
})
</script>

<style scoped>
/* 搜索结果下拉框 */
.search-results-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 1000;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  margin-top: 0.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* 搜索结果头部 */
.search-results-header {
  padding: 0.5rem 1rem;
  border-bottom: 1px solid #e5e7eb;
}

/* 搜索结果列表 */
.search-results-list {
  display: flex;
  flex-direction: column;
  width: 100%;
  list-style: none;
  margin: 0;
  padding: 0;
}

.search-result-item {
  display: block;
  width: 100%;
  margin: 0;
  padding: 0;
}

.search-result-item:not(:last-child) {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.search-result-link {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  width: 100%;
  text-decoration: none;
  color: inherit;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.search-result-link:hover,
.search-result-active {
  background-color: #f3f4f6;
}

/* 快捷键样式 */
.search-shortcut-key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 1.25rem;
  height: 1.25rem;
  padding: 0 0.25rem;
  font-size: 0.625rem;
  font-weight: 600;
  color: #6b7280;
  background: #f3f4f6;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  line-height: 1;
}

/* 书签图标 */
.search-result-avatar {
  flex-shrink: 0;
}

.search-result-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  overflow: hidden;
}

.search-result-icon img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 标签样式 */
.search-result-tag {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.375rem;
  font-size: 0.625rem;
  font-weight: 500;
  border-radius: 9999px;
}

/* 搜索结果底部 */
.search-results-footer {
  padding: 0.5rem 1rem;
  text-align: center;
  font-size: 0.75rem;
  color: #6b7280;
  border-top: 1px solid #e5e7eb;
}

/* 搜索高亮 */
:deep(.search-highlight) {
  background: #3b82f6;
  color: white;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-weight: 600;
}

/* 暗色主题 */
[data-theme="dark"] .search-results-dropdown {
  background: #1e293b;
  border-color: #475569;
}

[data-theme="dark"] .search-results-header {
  border-bottom-color: #475569;
}

[data-theme="dark"] .search-result-item:not(:last-child) {
  border-bottom-color: rgba(255, 255, 255, 0.05);
}

[data-theme="dark"] .search-result-link:hover,
[data-theme="dark"] .search-result-active {
  background-color: #334155;
}

[data-theme="dark"] .search-shortcut-key {
  color: #94a3b8;
  background: #334155;
  border-color: #475569;
}

[data-theme="dark"] .search-results-footer {
  color: #94a3b8;
  border-top-color: #475569;
}
</style>
