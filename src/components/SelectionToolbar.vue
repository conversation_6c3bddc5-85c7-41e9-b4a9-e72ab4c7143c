<template>
  <div
    v-if="selectedCount > 0"
    class="selection-toolbar"
  >
    <div class="flex items-center justify-between gap-2 flex-nowrap">
      <!-- 选中数量 -->
      <div class="flex items-center gap-2">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
          <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
        </svg>
        <span class="font-mono text-sm font-medium">已选择 {{ selectedCount }} 个</span>
      </div>

      <!-- 操作按钮 -->
      <div class="btn-group flex flex-nowrap">
        <button @click="$emit('select-all')" class="btn btn-sm btn-ghost" title="全选">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2"/>
            <path d="M9 12l2 2 4-4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <button @click="$emit('invert-selection')" class="btn btn-sm btn-ghost" title="反选">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M9 9h6v6H9z" fill="currentColor"/>
            <rect x="3" y="3" width="18" height="18" rx="2" stroke="currentColor" stroke-width="2" fill="none"/>
          </svg>
        </button>

        <div class="divider divider-horizontal"></div>

        <button @click="$emit('move-selected')" class="btn btn-sm btn-ghost" title="移动">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M7 7h10v10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M7 17 17 7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <button @click="$emit('clear-icons')" class="btn btn-sm btn-ghost" title="清理图标">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M3 6h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2m3 0v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6h14z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <button @click="$emit('delete-selected')" class="btn btn-sm btn-ghost text-error hover:text-error" title="删除">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M3 6h18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        </button>

        <div class="divider divider-horizontal"></div>

        <button @click="$emit('clear-selection')" class="btn btn-sm btn-ghost" title="取消选择">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
            <path d="M18 6L6 18M6 6l12 12" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
          </svg>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  selectedCount: number
}>()

defineEmits<{
  (e: 'clear-selection'): void
  (e: 'select-all'): void
  (e: 'invert-selection'): void
  (e: 'move-selected'): void
  (e: 'clear-icons'): void
  (e: 'delete-selected'): void
}>()
</script>

<style scoped>
/* 工具栏动画 */
.selection-toolbar {
  animation: slideDown 0.3s ease-out;
  white-space: nowrap;
  min-width: fit-content;
}

/* 确保按钮组不换行 */
.btn-group {
  flex-shrink: 0;
}

/* 确保选中数量文本不换行 */
.selection-toolbar .flex {
  white-space: nowrap;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
}
</style>
