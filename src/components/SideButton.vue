<template>
  <button
    class="side-button"
    :class="{
      'side-button-active': isActive,
      'side-button-left': position === 'left',
      'side-button-right': position === 'right'
    }"
    @click="handleClick"
    :title="title"
  >
    <component :is="icon" class="side-button-icon" />
    <span v-if="showLabel" class="side-button-label">{{ label }}</span>
  </button>
</template>

<script setup lang="ts">
import { defineProps, defineEmits } from 'vue'

interface Props {
  icon: any
  label: string
  title?: string
  position: 'left' | 'right'
  isActive?: boolean
  showLabel?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isActive: false,
  showLabel: false,
  title: ''
})

const emit = defineEmits<{
  (e: 'click'): void
}>()

function handleClick() {
  emit('click')
}
</script>

<style scoped>
.side-button {
  position: relative;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    0 1px 3px rgba(0, 0, 0, 0.05);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

.side-button:hover {
  transform: scale(1.1);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    0 2px 6px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.side-button-active {
  background: rgba(59, 130, 246, 0.9);
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 
    0 4px 20px rgba(59, 130, 246, 0.3),
    0 1px 3px rgba(59, 130, 246, 0.1);
}

.side-button-active:hover {
  background: rgba(59, 130, 246, 0.95);
  box-shadow: 
    0 8px 32px rgba(59, 130, 246, 0.4),
    0 2px 6px rgba(59, 130, 246, 0.2);
}

.side-button-icon {
  width: 24px;
  height: 24px;
  color: var(--text-primary, #1f2937);
  transition: color 0.3s ease;
}

.side-button-active .side-button-icon {
  color: white;
}

.side-button-label {
  position: absolute;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--text-primary, #1f2937);
  white-space: nowrap;
  opacity: 0;
  transition: all 0.3s ease;
  pointer-events: none;
}

.side-button-left .side-button-label {
  left: 100%;
  margin-left: 12px;
}

.side-button-right .side-button-label {
  right: 100%;
  margin-right: 12px;
}

.side-button:hover .side-button-label {
  opacity: 1;
}

/* 暗色主题适配 */
[data-theme="dark"] .side-button {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .side-button:hover {
  background: rgba(30, 41, 59, 0.95);
}

[data-theme="dark"] .side-button-icon {
  color: var(--text-primary, #f8fafc);
}

[data-theme="dark"] .side-button-label {
  color: var(--text-primary, #f8fafc);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .side-button {
    width: 48px;
    height: 48px;
  }
  
  .side-button-icon {
    width: 20px;
    height: 20px;
  }
  
  .side-button-label {
    display: none;
  }
}
</style>
