<template>
  <div class="sortable-folder-item" :data-folder-id="folder.id">
    <!-- 当前文件夹项 -->
    <button
      class="modal-folder-btn"
      :style="{ paddingLeft: `${level * 16}px` }"
      @click="toggleExpanded"
    >
      <FolderIcon :type="getFolderIconType()" />
      <span class="truncate">{{ folder.title }}</span>

      <!-- 删除按钮（仅对可删除的文件夹显示） -->
      <button
        v-if="canDeleteFolder"
        class="delete-folder-btn"
        @click.stop="handleDeleteFolder"
        title="无书签，可删除"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-4 h-4">
          <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
        </svg>
      </button>
    </button>

    <!-- 子文件夹（递归显示） -->
    <template v-if="expandedFolders.has(folder.id)">
      <SortableFolderTreeItem
        v-for="subFolder in subFolders"
        :key="subFolder.id"
        :folder="subFolder"
        :level="level + 1"
        :all-folders="allFolders"
        :expanded-folders="expandedFolders"
        :bookmarks="bookmarks"
        @change="$emit('change')"
        @delete-folder="$emit('delete-folder', $event)"
      />
    </template>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { BookmarkGroup, BookmarkWithMeta } from '../types'
import FolderIcon from './FolderIcon.vue'
import { useBookmarks } from '../composables/useBookmarks'

const props = defineProps<{
  folder: BookmarkGroup
  level: number
  allFolders: BookmarkGroup[]
  expandedFolders: Set<string>
  bookmarks: BookmarkWithMeta[]
}>()

const emit = defineEmits<{
  (e: 'change'): void
  (e: 'delete-folder', folderId: string): void
}>()

// 使用书签数据
const { loadBookmarks } = useBookmarks()

// 获取当前文件夹的子文件夹
const subFolders = computed(() => {
  return props.allFolders.filter(group => 
    group.parentId === props.folder.id && 
    group.title && 
    group.title.trim() !== ''
  )
})

// 检查是否有子文件夹
const hasSubFolders = computed(() => {
  return subFolders.value.length > 0
})

// 获取文件夹图标类型
const getFolderIconType = (): 'empty' | 'closed' | 'open' => {
  if (!hasSubFolders.value) {
    return 'empty'
  }
  return props.expandedFolders.has(props.folder.id) ? 'open' : 'closed'
}

// 切换展开状态
const toggleExpanded = () => {
  if (hasSubFolders.value) {
    if (props.expandedFolders.has(props.folder.id)) {
      props.expandedFolders.delete(props.folder.id)
    } else {
      props.expandedFolders.add(props.folder.id)
    }
  }
}

// 检查文件夹是否可以删除
const canDeleteFolder = computed(() => {
  // parentId === '0' 的文件夹不可删除（顶层文件夹）
  if (props.folder.parentId === '0') {
    console.log(`📁 文件夹 "${props.folder.title}" 是顶层文件夹，不可删除`)
    return false
  }

  // 检查文件夹是否有书签
  const hasBookmarksInFolder = props.bookmarks.some(bookmark => bookmark.parentId === props.folder.id)
  if (hasBookmarksInFolder) {
    console.log(`📁 文件夹 "${props.folder.title}" 包含书签，不可删除`)
    return false
  }

  // 递归检查所有子文件夹是否都没有书签
  const hasBookmarksInSubFolders = checkSubFoldersForBookmarks(props.folder.id)
  const canDelete = !hasBookmarksInSubFolders

  console.log(`📁 文件夹 "${props.folder.title}" 删除检查结果:`, {
    parentId: props.folder.parentId,
    hasBookmarksInFolder,
    hasBookmarksInSubFolders,
    canDelete
  })

  return canDelete
})

// 递归检查子文件夹是否有书签
const checkSubFoldersForBookmarks = (folderId: string): boolean => {
  // 获取直接子文件夹
  const directSubFolders = props.allFolders.filter(folder => folder.parentId === folderId)

  for (const subFolder of directSubFolders) {
    // 检查子文件夹是否有书签
    const hasBookmarksInSubFolder = props.bookmarks.some(bookmark => bookmark.parentId === subFolder.id)
    if (hasBookmarksInSubFolder) {
      return true
    }

    // 递归检查子文件夹的子文件夹
    if (checkSubFoldersForBookmarks(subFolder.id)) {
      return true
    }
  }

  return false
}

// 处理删除文件夹
const handleDeleteFolder = async () => {
  if (!canDeleteFolder.value) {
    console.log('❌ 文件夹不可删除:', props.folder.title)
    return
  }

  try {
    console.log('🗑️ 开始删除文件夹:', props.folder.title, '(ID:', props.folder.id, ')')

    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // 使用 Chrome API 删除文件夹树
      await chrome.bookmarks.removeTree(props.folder.id)
      console.log(`✅ 文件夹 "${props.folder.title}" 删除成功`)

      // 重新加载数据
      await loadBookmarks()
      console.log('✅ 数据重新加载完成')

      // 发射删除事件，通知父组件
      emit('delete-folder', props.folder.id)
    } else {
      console.log('🛠️ 开发环境：模拟删除文件夹')
      // 发射删除事件，通知父组件
      emit('delete-folder', props.folder.id)
    }
  } catch (error) {
    console.error('删除文件夹失败:', error)
    alert(`删除文件夹失败: ${(error as Error).message || error}`)
  }
}



// 拖拽事件处理
const handleUpdate = async (evt: any) => {
  console.log('=== SortableFolderTreeItem Update 事件 ===')
  console.log('父文件夹:', props.folder.title, '(ID:', props.folder.id, ')')
  console.log('事件对象:', evt)
  console.log('oldIndex:', evt.oldIndex)
  console.log('newIndex:', evt.newIndex)

  // 更新当前文件夹下的子文件夹顺序
  await updateSubFolderOrder()
  emit('change')
}

const handleAdd = async (evt: any) => {
  console.log('=== SortableFolderTreeItem Add 事件 ===')
  console.log('目标父文件夹:', props.folder.title, '(ID:', props.folder.id, ')')
  console.log('事件对象:', evt)
  console.log('newIndex:', evt.newIndex)

  // 使用与书签拖移相同的方法获取文件夹ID
  const folderElement = evt.item
  const folderId = folderElement.dataset.folderId

  console.log('DOM 元素:', folderElement)
  console.log('文件夹ID:', folderId)

  if (!folderId) {
    console.error('❌ 无法获取文件夹ID')
    console.log('DOM 元素属性:', {
      id: folderElement.id,
      className: folderElement.className,
      dataset: folderElement.dataset,
      textContent: folderElement.textContent
    })
    return
  }

  // 从 allFolders 中找到对应的文件夹信息
  const folderInfo = props.allFolders.find(f => f.id === folderId)
  if (!folderInfo) {
    console.error('❌ 找不到文件夹信息:', folderId)
    return
  }

  console.log('Moving folder:', folderInfo.title, 'to parent:', props.folder.id, 'at index:', evt.newIndex)

  try {
    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // 移动文件夹到新的父级和位置
      await chrome.bookmarks.move(folderId, {
        parentId: props.folder.id,
        index: evt.newIndex
      })
      console.log(`✅ 文件夹 "${folderInfo.title}" 移动到父级 ${props.folder.title} 位置 ${evt.newIndex}`)

      // 发射变更事件，让父组件处理数据更新
      console.log('✅ 文件夹移动完成，发射变更事件')
    } else {
      console.log('🛠️ 开发环境：模拟文件夹移动')
    }
  } catch (error) {
    console.error('移动文件夹失败:', error)
  }

  emit('change')
}

const handleRemove = async (evt: any) => {
  console.log('Folder sort remove:', evt, 'from folder:', props.folder.title)
  // 移除事件通常不需要额外处理，因为 Add 事件会处理实际的移动
  emit('change')
}

// 更新子文件夹顺序
const updateSubFolderOrder = async () => {
  try {
    const currentSubFolders = subFolders.value
    console.log('Updating sub-folder order for:', props.folder.title, currentSubFolders.map(f => f.title))
    
    if (typeof chrome !== 'undefined' && chrome.bookmarks) {
      // 为每个子文件夹设置新的索引位置
      for (let i = 0; i < currentSubFolders.length; i++) {
        const folder = currentSubFolders[i]
        try {
          await chrome.bookmarks.move(folder.id, {
            parentId: props.folder.id,
            index: i
          })
          console.log(`✅ 子文件夹 "${folder.title}" 移动到位置 ${i}`)
        } catch (error) {
          console.error(`❌ 移动子文件夹 "${folder.title}" 失败:`, error)
        }
      }
      
      // 重新加载数据
      await loadBookmarks()
      console.log('✅ 子文件夹排序完成，数据已重新加载')
    } else {
      // 开发环境处理
      console.log('🛠️ 开发环境：模拟子文件夹排序')
    }
  } catch (error) {
    console.error('更新子文件夹顺序失败:', error)
  }
}
</script>

<style scoped>
/* 使用与MoveFolderModal相同的样式 */
.modal-folder-btn {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 10px 12px;
  text-align: left;
  background: transparent;
  border: 1px solid transparent;
  border-radius: 8px;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  color: var(--text-primary);
  font-size: 14px;
  position: relative;
  margin-bottom: 0.25rem;
}

.modal-folder-btn:hover:not(.selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.08) 0%, rgba(59, 130, 246, 0.04) 100%);
  color: var(--text-primary);
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.06),
    0 1px 2px rgba(0, 0, 0, 0.04);
  transform: translateY(-0.5px);
  border-color: rgba(59, 130, 246, 0.1);
}

.modal-folder-btn:active:not(.selected) {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.12) 0%, rgba(59, 130, 246, 0.06) 100%);
  transform: translateY(0);
  box-shadow:
    0 1px 4px rgba(0, 0, 0, 0.08),
    inset 0 1px 2px rgba(0, 0, 0, 0.04);
}

.delete-folder-btn {
  opacity: 0;
  transition: opacity 0.2s ease;
  background: none;
  border: none;
  color: #ef4444;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
}

.delete-folder-btn:hover {
  background-color: rgba(239, 68, 68, 0.1);
}

.modal-folder-btn:hover .delete-folder-btn {
  opacity: 1;
}
</style>
