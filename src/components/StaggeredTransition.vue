<!--
  交错动画组件
  为列表项提供优雅的入场动画
-->

<template>
  <div class="staggered-container">
    <TransitionGroup
      :name="transitionName"
      tag="div"
      class="staggered-list"
      @before-enter="onBeforeEnter"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div
        v-for="(item, index) in items"
        :key="getItemKey(item, index)"
        :data-index="index"
        class="staggered-item"
      >
        <slot :item="item" :index="index" />
      </div>
    </TransitionGroup>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'

// Props
interface Props {
  items: any[]
  staggerDelay?: number
  animationDuration?: number
  animationType?: 'fade' | 'slide' | 'scale' | 'bounce'
  direction?: 'up' | 'down' | 'left' | 'right'
  keyField?: string
}

const props = withDefaults(defineProps<Props>(), {
  staggerDelay: 50,
  animationDuration: 300,
  animationType: 'fade',
  direction: 'up',
  keyField: 'id'
})

// 响应式状态
const isAnimating = ref(false)

// 计算属性
const transitionName = computed(() => {
  return `stagger-${props.animationType}-${props.direction}`
})

// 方法
const getItemKey = (item: any, index: number) => {
  if (typeof item === 'object' && item !== null) {
    return item[props.keyField] || item.id || index
  }
  return index
}

const onBeforeEnter = (el: Element) => {
  const element = el as HTMLElement
  element.style.opacity = '0'
  element.style.transform = getInitialTransform()
}

const onEnter = (el: Element, done: () => void) => {
  const element = el as HTMLElement
  const index = parseInt(element.dataset.index || '0')
  
  // 计算延迟时间
  const delay = index * props.staggerDelay
  
  // 设置动画
  setTimeout(() => {
    element.style.transition = `all ${props.animationDuration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`
    element.style.opacity = '1'
    element.style.transform = 'none'
    
    // 动画完成后调用done
    setTimeout(done, props.animationDuration)
  }, delay)
}

const onLeave = (el: Element, done: () => void) => {
  const element = el as HTMLElement
  const index = parseInt(element.dataset.index || '0')
  
  // 反向延迟
  const delay = (props.items.length - index - 1) * (props.staggerDelay / 2)
  
  setTimeout(() => {
    element.style.transition = `all ${props.animationDuration / 2}ms ease-in`
    element.style.opacity = '0'
    element.style.transform = getExitTransform()
    
    setTimeout(done, props.animationDuration / 2)
  }, delay)
}

const getInitialTransform = (): string => {
  switch (props.animationType) {
    case 'slide':
      switch (props.direction) {
        case 'up':
          return 'translateY(20px)'
        case 'down':
          return 'translateY(-20px)'
        case 'left':
          return 'translateX(20px)'
        case 'right':
          return 'translateX(-20px)'
        default:
          return 'translateY(20px)'
      }
    case 'scale':
      return 'scale(0.8)'
    case 'bounce':
      return 'scale(0.3) translateY(20px)'
    case 'fade':
    default:
      return 'translateY(10px)'
  }
}

const getExitTransform = (): string => {
  switch (props.animationType) {
    case 'slide':
      switch (props.direction) {
        case 'up':
          return 'translateY(-20px)'
        case 'down':
          return 'translateY(20px)'
        case 'left':
          return 'translateX(-20px)'
        case 'right':
          return 'translateX(20px)'
        default:
          return 'translateY(-20px)'
      }
    case 'scale':
      return 'scale(0.8)'
    case 'bounce':
      return 'scale(0.3) translateY(-20px)'
    case 'fade':
    default:
      return 'translateY(-10px)'
  }
}

// 监听items变化，触发动画
watch(() => props.items.length, (newLength, oldLength) => {
  if (newLength > oldLength) {
    isAnimating.value = true
    setTimeout(() => {
      isAnimating.value = false
    }, newLength * props.staggerDelay + props.animationDuration)
  }
})
</script>

<style scoped>
.staggered-container {
  width: 100%;
}

.staggered-list {
  display: contents;
}

.staggered-item {
  will-change: transform, opacity;
}

/* Fade 动画 */
.stagger-fade-up-enter-active,
.stagger-fade-up-leave-active,
.stagger-fade-down-enter-active,
.stagger-fade-down-leave-active,
.stagger-fade-left-enter-active,
.stagger-fade-left-leave-active,
.stagger-fade-right-enter-active,
.stagger-fade-right-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Slide 动画 */
.stagger-slide-up-enter-active,
.stagger-slide-up-leave-active,
.stagger-slide-down-enter-active,
.stagger-slide-down-leave-active,
.stagger-slide-left-enter-active,
.stagger-slide-left-leave-active,
.stagger-slide-right-enter-active,
.stagger-slide-right-leave-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

/* Scale 动画 */
.stagger-scale-up-enter-active,
.stagger-scale-up-leave-active,
.stagger-scale-down-enter-active,
.stagger-scale-down-leave-active,
.stagger-scale-left-enter-active,
.stagger-scale-left-leave-active,
.stagger-scale-right-enter-active,
.stagger-scale-right-leave-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

/* Bounce 动画 */
.stagger-bounce-up-enter-active,
.stagger-bounce-up-leave-active,
.stagger-bounce-down-enter-active,
.stagger-bounce-down-leave-active,
.stagger-bounce-left-enter-active,
.stagger-bounce-left-leave-active,
.stagger-bounce-right-enter-active,
.stagger-bounce-right-leave-active {
  transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* 减少动画对性能的影响 */
@media (prefers-reduced-motion: reduce) {
  .staggered-item {
    transition: none !important;
  }
  
  .stagger-fade-up-enter-active,
  .stagger-fade-up-leave-active,
  .stagger-fade-down-enter-active,
  .stagger-fade-down-leave-active,
  .stagger-fade-left-enter-active,
  .stagger-fade-left-leave-active,
  .stagger-fade-right-enter-active,
  .stagger-fade-right-leave-active,
  .stagger-slide-up-enter-active,
  .stagger-slide-up-leave-active,
  .stagger-slide-down-enter-active,
  .stagger-slide-down-leave-active,
  .stagger-slide-left-enter-active,
  .stagger-slide-left-leave-active,
  .stagger-slide-right-enter-active,
  .stagger-slide-right-leave-active,
  .stagger-scale-up-enter-active,
  .stagger-scale-up-leave-active,
  .stagger-scale-down-enter-active,
  .stagger-scale-down-leave-active,
  .stagger-scale-left-enter-active,
  .stagger-scale-left-leave-active,
  .stagger-scale-right-enter-active,
  .stagger-scale-right-leave-active,
  .stagger-bounce-up-enter-active,
  .stagger-bounce-up-leave-active,
  .stagger-bounce-down-enter-active,
  .stagger-bounce-down-leave-active,
  .stagger-bounce-left-enter-active,
  .stagger-bounce-left-leave-active,
  .stagger-bounce-right-enter-active,
  .stagger-bounce-right-leave-active {
    transition: none !important;
  }
}
</style>
