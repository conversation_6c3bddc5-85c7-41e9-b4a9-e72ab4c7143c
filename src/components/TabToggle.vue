<template>
  <!-- 使用 daisyUI tabs 组件 -->
  <div role="tablist" class="tabs tabs-boxed">
    <a
      v-for="tab in tabs"
      :key="tab.id"
      role="tab"
      class="tab"
      :class="{ 'tab-active': activeTab === tab.id }"
      @click="$emit('update:activeTab', tab.id)"
    >
      {{ tab.label }}
    </a>
  </div>
</template>

<script setup lang="ts">
interface Tab {
  id: string
  label: string
}

defineProps<{
  tabs: Tab[]
  activeTab: string
}>()

defineEmits<{
  (e: 'update:activeTab', tabId: string): void
}>()
</script>

<style scoped>
/* 使用daisyUI样式，无需自定义样式 */
</style>
