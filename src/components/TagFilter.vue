<template>
  <div v-if="tags.length > 0" class="mb-6 flex flex-wrap gap-2 items-center px-8">
    <!-- 标签过滤器 - 使用 daisyUI badge -->
    <TransitionGroup
      name="tag-list"
      tag="div"
      class="flex flex-wrap gap-2 items-center"
    >
      <div
        v-for="tag in displayedTags"
        :key="tag"
        class="tag-filter-card cursor-pointer transition-all duration-300 hover:scale-105"
        :class="{
          'tag-active': isTagActive(tag),
          'tag-inactive': !isTagActive(tag)
        }"
        @click="handleTagClick(tag)"
      >
        <span class="tag-text">{{ tag }}</span>
        <div
          class="tag-underline"
          :style="getTagUnderlineStyle(tag)"
        ></div>
      </div>
    </TransitionGroup>

    <!-- 展开/收起按钮 -->
    <button
      v-if="shouldShowExpandButton"
      class="btn btn-sm btn-ghost ml-2 flex items-center gap-1 transition-all duration-200"
      @click="toggleExpanded"
    >
      <span class="text-xs">{{ isExpanded ? '收起' : `展开更多 (+${hiddenTagsCount})` }}</span>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
        stroke-width="1.5"
        stroke="currentColor"
        class="w-3 h-3 transition-transform duration-200"
        :class="{ 'rotate-180': isExpanded }"
      >
        <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5" />
      </svg>
    </button>

    <!-- AND/OR 筛选模式切换 - 使用 daisyUI btn -->
    <div v-if="hasActiveFilters && Array.isArray(active) && active.length > 1" class="inline-flex items-center">
      <button
        class="btn btn-sm"
        :class="{
          'btn-primary': filterMode === 'AND',
          'btn-secondary': filterMode !== 'AND'
        }"
        @click="toggleFilterMode"
      >
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-2">
          <path stroke-linecap="round" stroke-linejoin="round" d="M12 3c2.755 0 5.455.232 8.083.678.533.09.917.556.917 1.096v1.044a2.25 2.25 0 01-.659 1.591l-5.432 5.432a2.25 2.25 0 00-.659 1.591v2.927a2.25 2.25 0 01-1.244 2.013L9.75 21v-6.568a2.25 2.25 0 00-.659-1.591L3.659 7.409A2.25 2.25 0 013 5.818V4.774c0-.54.384-1.006.917-1.096A48.32 48.32 0 0112 3z" />
        </svg>
        {{ filterMode === 'AND' ? '包含全部' : '包含任一' }}
      </button>
    </div>

    <!-- 清除筛选按钮 - 使用 daisyUI btn -->
    <button
      v-if="hasActiveFilters"
      class="btn btn-sm btn-ghost"
      @click="$emit('clear')"
    >
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" class="w-4 h-4 mr-2">
        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" />
      </svg>
      清除筛选
    </button>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted } from 'vue'
import { useTagColors } from '../composables/useTagColors'

const { getTagColor } = useTagColors()

const props = defineProps({
  tags: Array,
  active: [String, Array], // 支持单选和多选
  filterMode: {
    type: String,
    default: 'OR' // 默认为OR筛选
  }
})
const emit = defineEmits(['select', 'clear', 'filter-mode-change'])

// 主题模式状态
const isDark = ref(false)

// 展开/收起状态
const isExpanded = ref(false)

// 响应式收起标签数量
const getCollapsedTagCount = () => {
  // 检测屏幕宽度，移动端显示更少标签
  if (window.innerWidth <= 768) {
    return 3 // 移动端显示3个标签
  }
  return 5 // 桌面端显示5个标签
}

const COLLAPSED_TAG_COUNT = ref(getCollapsedTagCount())

// 检测当前主题模式
const updateThemeMode = () => {
  isDark.value = document.documentElement.getAttribute('data-theme') === 'dark'
}

// 检查是否有激活的筛选
const hasActiveFilters = computed(() => {
  if (Array.isArray(props.active)) {
    return props.active.length > 0
  }
  return !!props.active
})

// 计算显示的标签列表
const displayedTags = computed(() => {
  if (isExpanded.value || props.tags.length <= COLLAPSED_TAG_COUNT.value) {
    return props.tags
  }
  return props.tags.slice(0, COLLAPSED_TAG_COUNT.value)
})

// 是否应该显示展开按钮
const shouldShowExpandButton = computed(() => {
  return props.tags.length > COLLAPSED_TAG_COUNT.value
})

// 隐藏的标签数量
const hiddenTagsCount = computed(() => {
  return Math.max(0, props.tags.length - COLLAPSED_TAG_COUNT.value)
})

// 检查标签是否被选中
function isTagActive(tag) {
  if (Array.isArray(props.active)) {
    return props.active.includes(tag)
  }
  return tag === props.active
}

// 处理标签点击 - 直接多选模式
function handleTagClick(tag) {
  console.log('标签点击事件:', { tag })

  // 直接使用多选模式
  emit('select', tag)
}

// 切换筛选模式
function toggleFilterMode() {
  const newMode = props.filterMode === 'AND' ? 'OR' : 'AND'
  emit('filter-mode-change', newMode)
}

// 切换展开/收起状态
function toggleExpanded() {
  isExpanded.value = !isExpanded.value
}

// 获取标签底部线条样式
function getTagUnderlineStyle(tag) {
  const tagColor = getTagColor(tag)
  const isActive = isTagActive(tag)

  return {
    backgroundColor: tagColor,
    opacity: isActive ? 0.8 : 0.6,
    transform: isActive ? 'scaleX(1) scaleY(1.5)' : 'scaleX(0.8) scaleY(1)',
    height: isActive ? '4px' : '3px'
  }
}

// 小圆点样式函数已移除，现在使用更简洁的标签设计

// 窗口大小变化处理
const handleResize = () => {
  const newCount = getCollapsedTagCount()
  if (COLLAPSED_TAG_COUNT.value !== newCount) {
    COLLAPSED_TAG_COUNT.value = newCount
    // 如果当前是展开状态且新的阈值更大，可能需要自动收起
    if (isExpanded.value && props.tags.length <= newCount) {
      isExpanded.value = false
    }
  }
}

// 主题变化监听
onMounted(() => {
  updateThemeMode()
  // 添加窗口大小变化监听
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  // 清理工作
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
/* 使用daisyUI样式，保留必要的自定义样式 */

/* 标签列表过渡动画 */
.tag-list-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  transition-delay: calc(var(--i, 0) * 0.05s);
}

.tag-list-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tag-list-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.9);
}

.tag-list-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.95);
}

.tag-list-move {
  transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 展开按钮样式优化 */
.btn:hover {
  transform: translateY(-1px);
}

.btn:active {
  transform: translateY(0);
}

/* 新的标签卡片样式 */
.tag-filter-card {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 1rem 0.25rem;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 0.75rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}

.tag-filter-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px) scale(1.02);
  background: rgba(255, 255, 255, 0.95);
}

.tag-filter-card.tag-active {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  transform: translateY(-1px);
}

.tag-filter-card.tag-inactive {
  opacity: 0.8;
}

.tag-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-primary, #1f2937);
  letter-spacing: 0.025em;
  margin-bottom: 0.25rem;
}

.tag-underline {
  width: 100%;
  height: 3px;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  transform-origin: center bottom;
}

/* 暗色主题下的标签卡片样式 */
[data-theme="dark"] .tag-filter-card {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(255, 255, 255, 0.1);
}

[data-theme="dark"] .tag-filter-card:hover {
  background: rgba(30, 41, 59, 0.95);
}

[data-theme="dark"] .tag-filter-card.tag-active {
  background: rgba(30, 41, 59, 0.95);
}

[data-theme="dark"] .tag-text {
  color: var(--text-primary, #f8fafc);
}

/* 移动端响应式优化 */
@media (max-width: 768px) {
  /* 移动端标签过滤器容器 */
  .mb-6 {
    margin-bottom: 1rem;
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 移动端标签卡片样式 */
  .tag-filter-card {
    padding: 0.375rem 0.75rem 0.25rem;
    border-radius: 0.5rem;
  }

  .tag-text {
    font-size: 0.75rem;
    margin-bottom: 0.2rem;
  }

  .tag-underline {
    height: 2px;
  }

  /* 移动端展开按钮 */
  .btn-sm {
    font-size: 0.7rem;
    padding: 0.25rem 0.5rem;
    min-height: 1.75rem;
  }

  /* 移动端图标大小 */
  .w-3 {
    width: 0.625rem;
    height: 0.625rem;
  }

  /* 移动端标签间距 */
  .gap-2 {
    gap: 0.375rem;
  }
}
</style>
