<!--
  智能标签输入组件
  支持自动完成、标签建议、快速添加
-->

<template>
  <div class="tag-input-container">
    <!-- 已选标签显示 -->
    <div v-if="selectedTags.length > 0" class="selected-tags">
      <div
        v-for="tag in selectedTags"
        :key="tag"
        class="selected-tag"
        :style="{ 
          backgroundColor: getTagColor(tag),
          color: getContrastColor(getTagColor(tag))
        }"
      >
        <span class="tag-text">{{ tag }}</span>
        <button
          class="tag-remove-btn"
          @click="removeTag(tag)"
          :style="{ color: getContrastColor(getTagColor(tag)) }"
        >
          ×
        </button>
      </div>
    </div>

    <!-- 标签输入框 -->
    <div class="input-wrapper">
      <input
        ref="inputRef"
        v-model="inputValue"
        type="text"
        class="tag-input"
        :placeholder="placeholder"
        @input="handleInput"
        @keydown="handleKeydown"
        @focus="showSuggestions = true"
        @blur="handleBlur"
      />
      
      <!-- 分隔符提示 */
      <div v-if="showSeparatorHint" class="separator-hint">
        使用 "{{ tagSeparator }}" 分隔多个标签
      </div>
    </div>

    <!-- 标签建议下拉框 -->
    <div v-if="showSuggestions && filteredSuggestions.length > 0" class="suggestions-dropdown">
      <div class="suggestions-header">
        <span class="suggestions-title">标签建议</span>
        <span class="suggestions-count">{{ filteredSuggestions.length }}</span>
      </div>
      
      <div class="suggestions-list">
        <div
          v-for="(suggestion, index) in filteredSuggestions"
          :key="suggestion.name"
          class="suggestion-item"
          :class="{ 'suggestion-highlighted': highlightedIndex === index }"
          @click="selectSuggestion(suggestion)"
          @mouseenter="highlightedIndex = index"
        >
          <div 
            class="suggestion-color"
            :style="{ backgroundColor: suggestion.color }"
          ></div>
          <div class="suggestion-content">
            <span class="suggestion-name">{{ suggestion.name }}</span>
            <span class="suggestion-count">{{ suggestion.count }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速标签按钮 -->
    <div v-if="showQuickTags && quickTags.length > 0" class="quick-tags">
      <div class="quick-tags-label">常用标签:</div>
      <div class="quick-tags-list">
        <button
          v-for="tag in quickTags"
          :key="tag.name"
          class="quick-tag-btn"
          :style="{ 
            backgroundColor: tag.color + '20',
            borderColor: tag.color,
            color: tag.color
          }"
          @click="addQuickTag(tag.name)"
        >
          {{ tag.name }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useTagStore } from '../stores/tags'

// Props
interface Props {
  modelValue: string[]
  placeholder?: string
  showQuickTags?: boolean
  showSeparatorHint?: boolean
  maxSuggestions?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入标签...',
  showQuickTags: true,
  showSeparatorHint: true,
  maxSuggestions: 8
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [tags: string[]]
}>()

// Store
const tagStore = useTagStore()

// 响应式状态
const inputRef = ref<HTMLInputElement>()
const inputValue = ref('')
const showSuggestions = ref(false)
const highlightedIndex = ref(0)

// 计算属性
const selectedTags = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const { availableTags, tagSeparator } = tagStore

// 过滤建议标签
const filteredSuggestions = computed(() => {
  if (!inputValue.value.trim()) {
    return availableTags.slice(0, props.maxSuggestions)
  }

  const query = inputValue.value.toLowerCase()
  return availableTags
    .filter(tag => 
      tag.name.toLowerCase().includes(query) && 
      !selectedTags.value.includes(tag.name)
    )
    .slice(0, props.maxSuggestions)
})

// 快速标签（使用频率最高的标签）
const quickTags = computed(() => {
  return availableTags
    .filter(tag => !selectedTags.value.includes(tag.name))
    .sort((a, b) => b.count - a.count)
    .slice(0, 6)
})

// 方法
const getTagColor = (tagName: string) => {
  const tag = availableTags.find(t => t.name === tagName)
  return tag?.color || tagStore.generateTagColor(tagName)
}

const getContrastColor = (backgroundColor: string) => {
  // 简单的对比度计算，返回黑色或白色
  const hex = backgroundColor.replace('#', '')
  const r = parseInt(hex.substr(0, 2), 16)
  const g = parseInt(hex.substr(2, 2), 16)
  const b = parseInt(hex.substr(4, 2), 16)
  const brightness = (r * 299 + g * 587 + b * 114) / 1000
  return brightness > 128 ? '#000000' : '#ffffff'
}

const handleInput = () => {
  showSuggestions.value = true
  highlightedIndex.value = 0
}

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter') {
    event.preventDefault()
    if (showSuggestions.value && filteredSuggestions.value.length > 0) {
      selectSuggestion(filteredSuggestions.value[highlightedIndex.value])
    } else if (inputValue.value.trim()) {
      addTag(inputValue.value.trim())
    }
  } else if (event.key === 'ArrowDown') {
    event.preventDefault()
    if (highlightedIndex.value < filteredSuggestions.value.length - 1) {
      highlightedIndex.value++
    }
  } else if (event.key === 'ArrowUp') {
    event.preventDefault()
    if (highlightedIndex.value > 0) {
      highlightedIndex.value--
    }
  } else if (event.key === 'Escape') {
    showSuggestions.value = false
  } else if (event.key === 'Backspace' && !inputValue.value && selectedTags.value.length > 0) {
    // 删除最后一个标签
    removeTag(selectedTags.value[selectedTags.value.length - 1])
  }
  
  // 检查分隔符
  if (inputValue.value.includes(tagSeparator)) {
    const tags = inputValue.value.split(tagSeparator).map(t => t.trim()).filter(Boolean)
    tags.forEach(tag => addTag(tag))
    inputValue.value = ''
  }
}

const handleBlur = () => {
  // 延迟隐藏建议，允许点击建议项
  setTimeout(() => {
    showSuggestions.value = false
  }, 200)
}

const addTag = (tagName: string) => {
  if (tagName && !selectedTags.value.includes(tagName)) {
    selectedTags.value = [...selectedTags.value, tagName]
    
    // 创建新标签（如果不存在）
    if (!availableTags.find(t => t.name === tagName)) {
      tagStore.createTag(tagName)
    }
  }
  inputValue.value = ''
  showSuggestions.value = false
}

const removeTag = (tagName: string) => {
  selectedTags.value = selectedTags.value.filter(tag => tag !== tagName)
}

const selectSuggestion = (suggestion: any) => {
  addTag(suggestion.name)
  nextTick(() => {
    inputRef.value?.focus()
  })
}

const addQuickTag = (tagName: string) => {
  addTag(tagName)
  nextTick(() => {
    inputRef.value?.focus()
  })
}

// 监听建议变化，重置高亮索引
watch(filteredSuggestions, () => {
  highlightedIndex.value = 0
})

// 生命周期
onMounted(() => {
  tagStore.loadTags()
})
</script>

<style scoped>
.tag-input-container {
  position: relative;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.selected-tag {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  border-radius: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.tag-text {
  line-height: 1;
}

.tag-remove-btn {
  background: none;
  border: none;
  font-size: 1.125rem;
  line-height: 1;
  cursor: pointer;
  padding: 0;
  margin-left: 0.25rem;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.tag-remove-btn:hover {
  opacity: 1;
}

.input-wrapper {
  position: relative;
}

.tag-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  outline: none;
  transition: border-color 0.2s ease;
}

.tag-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.separator-hint {
  position: absolute;
  top: 100%;
  left: 0;
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.suggestions-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 0.25rem;
  max-height: 200px;
  overflow-y: auto;
}

.suggestions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.suggestions-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: #374151;
}

.suggestions-count {
  font-size: 0.75rem;
  color: #6b7280;
}

.suggestions-list {
  padding: 0.25rem 0;
}

.suggestion-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem 0.75rem;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.suggestion-item:hover,
.suggestion-highlighted {
  background: #f3f4f6;
}

.suggestion-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.suggestion-name {
  font-size: 0.875rem;
  color: #374151;
}

.suggestion-count {
  font-size: 0.75rem;
  color: #6b7280;
  background: #f1f5f9;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
}

.quick-tags {
  margin-top: 0.75rem;
}

.quick-tags-label {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.quick-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.quick-tag-btn {
  padding: 0.25rem 0.5rem;
  border: 1px solid;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 500;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-tag-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 暗色主题 */
[data-theme="dark"] .tag-input {
  background: #1e293b;
  border-color: #475569;
  color: #f1f5f9;
}

[data-theme="dark"] .tag-input:focus {
  border-color: #3b82f6;
}

[data-theme="dark"] .suggestions-dropdown {
  background: #1e293b;
  border-color: #475569;
}

[data-theme="dark"] .suggestions-header {
  background: #334155;
  border-bottom-color: #475569;
}

[data-theme="dark"] .suggestion-item:hover,
[data-theme="dark"] .suggestion-highlighted {
  background: #334155;
}
</style>
