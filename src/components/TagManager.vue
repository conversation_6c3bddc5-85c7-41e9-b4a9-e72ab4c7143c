<!--
  标签管理器组件
  独立的标签管理系统，支持智能配色、批量操作
-->

<template>
  <div class="tag-manager">
    <!-- 标签统计概览 -->
    <div class="tag-stats">
      <div class="stat-item">
        <span class="stat-label">总标签数</span>
        <span class="stat-value">{{ availableTags.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">已激活</span>
        <span class="stat-value">{{ activeTags.length }}</span>
      </div>
      <div class="stat-item">
        <span class="stat-label">筛选模式</span>
        <span class="stat-value">{{ filterMode }}</span>
      </div>
    </div>

    <!-- 筛选模式切换 -->
    <div class="filter-mode-toggle">
      <label class="toggle-label">
        <input
          type="checkbox"
          class="toggle"
          :checked="filterMode === 'AND'"
          @change="handleFilterModeChange"
        />
        <span class="toggle-text">
          {{ filterMode === 'AND' ? 'AND 模式' : 'OR 模式' }}
        </span>
      </label>
    </div>

    <!-- 标签列表 -->
    <div class="tag-list">
      <div
        v-for="tag in displayedTags"
        :key="tag.name"
        class="tag-item"
        :class="{ 'tag-active': isTagActive(tag.name) }"
        @click="toggleTag(tag.name)"
      >
        <div 
          class="tag-color-indicator"
          :style="{ backgroundColor: tag.color }"
        ></div>
        
        <div class="tag-content">
          <span class="tag-name">{{ tag.name }}</span>
          <span class="tag-count">{{ tag.count }}</span>
        </div>
        
        <div class="tag-actions">
          <button
            class="tag-action-btn"
            @click.stop="editTag(tag)"
            title="编辑标签"
          >
            ✏️
          </button>
          <button
            class="tag-action-btn"
            @click.stop="deleteTag(tag.name)"
            title="删除标签"
          >
            🗑️
          </button>
        </div>
      </div>
    </div>

    <!-- 展开/收起按钮 -->
    <div v-if="availableTags.length > maxDisplayTags" class="expand-toggle">
      <button
        class="btn btn-ghost btn-sm"
        @click="isExpanded = !isExpanded"
      >
        {{ isExpanded ? '收起' : `显示全部 ${availableTags.length} 个标签` }}
      </button>
    </div>

    <!-- 标签编辑模态框 -->
    <div v-if="showEditModal" class="modal">
      <div class="modal-box" @click.stop>
        <h3 class="modal-title">编辑标签</h3>
        
        <div class="form-group">
          <label class="form-label">标签名称</label>
          <input
            v-model="editingTag.name"
            type="text"
            class="input"
            placeholder="输入标签名称"
            disabled
            readonly
          />
          <div class="form-hint">
            标签名称从书签标题中自动提取，不能直接修改
          </div>
        </div>
        
        <div class="form-group">
          <label class="form-label">标签颜色</label>
          <div class="color-picker">
            <input
              v-model="editingTag.color"
              type="color"
              class="color-input"
            />
            <button
              class="btn btn-sm btn-outline"
              @click="regenerateColor"
            >
              重新生成
            </button>
          </div>
        </div>
        
        <div class="modal-action">
          <button class="btn" @click="cancelEdit">取消</button>
          <button class="btn btn-primary" @click="saveTag">保存</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useTagStore } from '../stores/tags'

// Props
interface Props {
  maxDisplayTags?: number
}

const props = withDefaults(defineProps<Props>(), {
  maxDisplayTags: 5
})

// Store
const tagStore = useTagStore()

// 响应式状态
const isExpanded = ref(false)
const showEditModal = ref(false)
const editingTag = ref({
  name: '',
  color: '#3b82f6',
  originalName: ''
})

// 计算属性
const { availableTags, activeTags, filterMode } = tagStore

const displayedTags = computed(() => {
  if (isExpanded.value || availableTags.length <= props.maxDisplayTags) {
    return availableTags
  }
  return availableTags.slice(0, props.maxDisplayTags)
})

// 方法
const isTagActive = (tagName: string) => {
  return activeTags.includes(tagName)
}

const toggleTag = (tagName: string) => {
  tagStore.toggleTag(tagName)
}

const handleFilterModeChange = (event: Event) => {
  const target = event.target as HTMLInputElement
  tagStore.setFilterMode(target.checked ? 'AND' : 'OR')
}

const editTag = (tag: any) => {
  // 编辑标签实际上是编辑使用该标签的书签标题
  // 这里只允许修改标签颜色
  editingTag.value = {
    name: tag.name,
    color: tag.color,
    originalName: tag.name
  }
  showEditModal.value = true
}

const deleteTag = (tagName: string) => {
  if (confirm(`确定要删除标签 "${tagName}" 吗？`)) {
    tagStore.deleteTag(tagName)
  }
}

const regenerateColor = () => {
  editingTag.value.color = tagStore.generateTagColor(editingTag.value.name)
}

const cancelEdit = () => {
  showEditModal.value = false
  editingTag.value = {
    name: '',
    color: '#3b82f6',
    originalName: ''
  }
}

const saveTag = async () => {
  try {
    // 只更新标签颜色，标签名称从书签标题中提取，不能直接修改
    if (editingTag.value.originalName !== editingTag.value.name) {
      alert('标签名称不能直接修改，请编辑使用该标签的书签标题')
      return
    }

    // 更新标签颜色
    const existingTag = tagStore.tags.find(t => t.name === editingTag.value.originalName)
    if (existingTag) {
      existingTag.color = editingTag.value.color
    }

    showEditModal.value = false
  } catch (error) {
    console.error('保存标签失败:', error)
    alert('保存失败，请重试')
  }
}

// 生命周期
onMounted(() => {
  tagStore.loadTags()
})
</script>

<style scoped>
.tag-manager {
  padding: 1rem;
  background: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tag-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  padding: 0.75rem;
  background: #f8fafc;
  border-radius: 0.375rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #64748b;
  font-weight: 500;
}

.stat-value {
  font-size: 1.125rem;
  font-weight: 700;
  color: #1e293b;
}

.filter-mode-toggle {
  margin-bottom: 1rem;
}

.toggle-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.toggle-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.tag-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.tag-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-item:hover {
  border-color: #cbd5e1;
  background: #f8fafc;
}

.tag-active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.tag-color-indicator {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.tag-content {
  flex: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tag-name {
  font-weight: 500;
  color: #1e293b;
}

.tag-count {
  font-size: 0.75rem;
  color: #64748b;
  background: #f1f5f9;
  padding: 0.125rem 0.375rem;
  border-radius: 0.25rem;
}

.tag-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.tag-item:hover .tag-actions {
  opacity: 1;
}

.tag-action-btn {
  padding: 0.25rem;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 0.25rem;
  transition: background 0.2s ease;
}

.tag-action-btn:hover {
  background: #e2e8f0;
}

.expand-toggle {
  text-align: center;
}

.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-title {
  font-size: 1.125rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #1e293b;
}

.form-group {
  margin-bottom: 1rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-hint {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
  font-style: italic;
}

.color-picker {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-input {
  width: 3rem;
  height: 2rem;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  cursor: pointer;
}

/* 暗色主题 */
[data-theme="dark"] .tag-manager {
  background: #1e293b;
}

[data-theme="dark"] .tag-stats {
  background: #334155;
}

[data-theme="dark"] .stat-value {
  color: #f1f5f9;
}

[data-theme="dark"] .tag-item {
  border-color: #475569;
  color: #f1f5f9;
}

[data-theme="dark"] .tag-item:hover {
  border-color: #64748b;
  background: #334155;
}

[data-theme="dark"] .tag-active {
  border-color: #3b82f6;
  background: #1e40af;
}

[data-theme="dark"] .modal-box {
  background: #1e293b;
  color: #f1f5f9;
}
</style>
