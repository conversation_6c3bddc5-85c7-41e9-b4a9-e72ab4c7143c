<template>
  <!-- 垃圾桶区域 - 修复显示问题 -->
  <div
    v-if="isDragging"
    class="fixed bottom-4 right-4 z-50"
  >
    <div
      class="alert alert-error w-auto shadow-lg transition-all duration-300 dropzone min-h-20"
      :class="{ 'scale-110': isHovering }"
    >
      <!-- 垃圾桶图标和文字 -->
      <div class="flex flex-col items-center gap-1 p-2">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-8 h-8">
          <path stroke-linecap="round" stroke-linejoin="round" d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0" />
        </svg>
        <span class="text-sm font-medium">拖到此处删除</span>
      </div>

      <!-- 隐藏的 draggable 用于接收拖拽 -->
      <draggable
        v-model="trashItems"
        class="absolute inset-0 opacity-0"
        :group="{ name: 'bookmarks', pull: false, put: true }"
        @add="handleAdd"
        @dragenter="onDragEnter"
        @dragleave="onDragLeave"
        item-key="id"
      >
        <template #item="{}">
          <!-- 空模板 -->
        </template>
      </draggable>

      <!-- 文件夹拖拽区域 -->
      <draggable
        v-model="trashFolders"
        class="absolute inset-0 opacity-0"
        :group="{ name: 'folders', pull: false, put: true }"
        @add="handleFolderAdd"
        @dragenter="onDragEnter"
        @dragleave="onDragLeave"
        item-key="id"
      >
        <template #item="{}">
          <!-- 空模板 -->
        </template>
      </draggable>
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, ref } from 'vue'
import draggable from 'vuedraggable'
import type { BookmarkWithMeta } from '../types'

defineProps<{
  isDragging: boolean
}>()

const emit = defineEmits<{
  (e: 'drop', bookmarkId: string): void
  (e: 'folder-drop', folderId: string): void
}>()

const isHovering = ref(false)
const trashItems = ref<BookmarkWithMeta[]>([])
const trashFolders = ref<any[]>([])

// 处理拖拽到垃圾桶
function handleAdd(evt: any) {
  const bookmark = evt.item.__draggable_context?.element
  if (bookmark) {
    emit('drop', bookmark.id)
    // 清空垃圾桶内容
    trashItems.value = []
  }
}

// 处理文件夹拖拽到垃圾桶
function handleFolderAdd(evt: any) {
  const folder = evt.item.__draggable_context?.element
  if (folder) {
    emit('folder-drop', folder.id)
    // 清空垃圾桶内容
    trashFolders.value = []
  }
}

function onDragEnter() {
  isHovering.value = true
}

function onDragLeave() {
  isHovering.value = false
}
</script>

<style scoped>
/* DaisyUI 样式增强 */
.dropzone {
  backdrop-filter: blur(10px);
}
</style>
