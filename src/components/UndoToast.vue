<!--
  撤销提示组件
  5秒倒计时撤销机制
-->

<template>
  <Teleport to="body">
    <Transition
      enter-active-class="transition-all duration-300 ease-out"
      enter-from-class="opacity-0 translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-2"
    >
      <div
        v-if="isVisible"
        class="undo-toast"
        :class="[`toast-${type}`, { 'toast-paused': isPaused }]"
        @mouseenter="pauseTimer"
        @mouseleave="resumeTimer"
      >
        <!-- 图标 -->
        <div class="toast-icon">
          <component :is="iconComponent" class="w-5 h-5" />
        </div>

        <!-- 内容 -->
        <div class="toast-content">
          <div class="toast-message">{{ message }}</div>
          <div v-if="description" class="toast-description">{{ description }}</div>
        </div>

        <!-- 倒计时进度条 -->
        <div class="toast-progress">
          <div
            class="progress-bar"
            :style="{ width: `${progressPercentage}%` }"
          ></div>
        </div>

        <!-- 操作按钮 -->
        <div class="toast-actions">
          <button
            v-if="showUndoButton"
            class="undo-button"
            @click="handleUndo"
          >
            撤销
          </button>
          
          <button
            class="close-button"
            @click="handleClose"
          >
            ✕
          </button>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// Props
interface Props {
  message: string
  description?: string
  type?: 'success' | 'warning' | 'error' | 'info'
  duration?: number
  showUndoButton?: boolean
  autoClose?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'info',
  duration: 5000,
  showUndoButton: true,
  autoClose: true
})

// Emits
const emit = defineEmits<{
  'undo': []
  'close': []
  'timeout': []
}>()

// 响应式状态
const isVisible = ref(false)
const isPaused = ref(false)
const remainingTime = ref(props.duration)
const startTime = ref(0)
const pausedAt = ref(0)

let timerId: number | null = null

// 计算属性
const progressPercentage = computed(() => {
  return ((props.duration - remainingTime.value) / props.duration) * 100
})

const iconComponent = computed(() => {
  switch (props.type) {
    case 'success':
      return 'CheckCircleIcon'
    case 'warning':
      return 'ExclamationTriangleIcon'
    case 'error':
      return 'XCircleIcon'
    case 'info':
    default:
      return 'InformationCircleIcon'
  }
})

// 方法
const show = () => {
  isVisible.value = true
  if (props.autoClose) {
    startTimer()
  }
}

const hide = () => {
  isVisible.value = false
  clearTimer()
}

const startTimer = () => {
  if (!props.autoClose) return
  
  startTime.value = Date.now()
  
  const tick = () => {
    if (isPaused.value) return
    
    const elapsed = Date.now() - startTime.value
    remainingTime.value = Math.max(0, props.duration - elapsed)
    
    if (remainingTime.value <= 0) {
      handleTimeout()
      return
    }
    
    timerId = requestAnimationFrame(tick)
  }
  
  timerId = requestAnimationFrame(tick)
}

const clearTimer = () => {
  if (timerId) {
    cancelAnimationFrame(timerId)
    timerId = null
  }
}

const pauseTimer = () => {
  if (!props.autoClose) return
  
  isPaused.value = true
  pausedAt.value = Date.now()
  clearTimer()
}

const resumeTimer = () => {
  if (!props.autoClose || !isPaused.value) return
  
  isPaused.value = false
  
  // 调整开始时间，补偿暂停的时间
  const pauseDuration = Date.now() - pausedAt.value
  startTime.value += pauseDuration
  
  startTimer()
}

const handleUndo = () => {
  emit('undo')
  hide()
}

const handleClose = () => {
  emit('close')
  hide()
}

const handleTimeout = () => {
  emit('timeout')
  hide()
}

// 监听props变化
watch(() => props.duration, (newDuration) => {
  remainingTime.value = newDuration
  if (isVisible.value && props.autoClose) {
    clearTimer()
    startTimer()
  }
})

// 暴露方法给父组件
defineExpose({
  show,
  hide,
  pauseTimer,
  resumeTimer
})

// 生命周期
onMounted(() => {
  show()
})

onUnmounted(() => {
  clearTimer()
})
</script>

<style scoped>
.undo-toast {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  min-width: 320px;
  max-width: 480px;
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid #e5e7eb;
  overflow: hidden;
  z-index: 9999;
  cursor: pointer;
  transition: all 0.2s ease;
}

.undo-toast:hover {
  transform: translateY(-2px);
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.undo-toast.toast-paused {
  transform: translateY(-2px) scale(1.02);
}

/* 类型样式 */
.toast-success {
  border-left: 4px solid #10b981;
}

.toast-warning {
  border-left: 4px solid #f59e0b;
}

.toast-error {
  border-left: 4px solid #ef4444;
}

.toast-info {
  border-left: 4px solid #3b82f6;
}

/* 内容布局 */
.undo-toast {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  position: relative;
}

.toast-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.toast-success .toast-icon {
  color: #10b981;
}

.toast-warning .toast-icon {
  color: #f59e0b;
}

.toast-error .toast-icon {
  color: #ef4444;
}

.toast-info .toast-icon {
  color: #3b82f6;
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-message {
  font-weight: 500;
  color: #111827;
  line-height: 1.4;
}

.toast-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.25rem;
  line-height: 1.4;
}

.toast-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-shrink: 0;
}

.undo-button {
  padding: 0.375rem 0.75rem;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background 0.2s ease;
}

.undo-button:hover {
  background: #2563eb;
}

.close-button {
  padding: 0.25rem;
  background: transparent;
  color: #6b7280;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
}

.close-button:hover {
  background: #f3f4f6;
  color: #374151;
}

/* 进度条 */
.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: #f3f4f6;
}

.progress-bar {
  height: 100%;
  background: currentColor;
  transition: width 0.1s linear;
}

.toast-success .progress-bar {
  background: #10b981;
}

.toast-warning .progress-bar {
  background: #f59e0b;
}

.toast-error .progress-bar {
  background: #ef4444;
}

.toast-info .progress-bar {
  background: #3b82f6;
}

/* 暗色主题 */
[data-theme="dark"] .undo-toast {
  background: #1f2937;
  border-color: #374151;
}

[data-theme="dark"] .toast-message {
  color: #f9fafb;
}

[data-theme="dark"] .toast-description {
  color: #d1d5db;
}

[data-theme="dark"] .close-button:hover {
  background: #374151;
  color: #e5e7eb;
}

[data-theme="dark"] .toast-progress {
  background: #374151;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .undo-toast {
    bottom: 1rem;
    right: 1rem;
    left: 1rem;
    min-width: auto;
    max-width: none;
  }
  
  .toast-actions {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .undo-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
  }
}

/* 动画优化 */
@media (prefers-reduced-motion: reduce) {
  .undo-toast {
    transition: none;
  }
  
  .progress-bar {
    transition: none;
  }
}
</style>
