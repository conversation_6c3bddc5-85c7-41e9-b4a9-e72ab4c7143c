<!--
  虚拟化书签网格组件
  支持大量书签的高性能渲染
-->

<template>
  <div class="virtual-bookmark-grid" ref="containerRef">
    <!-- 虚拟化容器 -->
    <div
      :style="{
        height: `${virtualizer.getTotalSize()}px`,
        width: '100%',
        position: 'relative',
      }"
    >
      <!-- 渲染可见项目 -->
      <div
        v-for="virtualRow in virtualizer.getVirtualItems()"
        :key="virtualRow.index"
        :style="{
          position: 'absolute',
          top: 0,
          left: 0,
          width: '100%',
          height: `${virtualRow.size}px`,
          transform: `translateY(${virtualRow.start}px)`,
        }"
      >
        <!-- 书签行 -->
        <div class="bookmark-row">
          <div
            v-for="(bookmark, colIndex) in getRowBookmarks(virtualRow.index)"
            :key="bookmark?.id || `empty-${colIndex}`"
            class="bookmark-item"
            :class="{ 'bookmark-empty': !bookmark }"
            @click="bookmark && handleBookmarkClick(bookmark)"
            @contextmenu="bookmark && handleBookmarkRightClick($event, bookmark)"
          >
            <BookmarkCard
              v-if="bookmark"
              :bookmark="bookmark"
              :view-mode="viewMode"
              :is-selected="selectedBookmarkIds.includes(bookmark.id)"
              @select="handleBookmarkSelect"
              @edit="handleBookmarkEdit"
              @delete="handleBookmarkDelete"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 加载更多指示器 -->
    <div v-if="isLoading" class="loading-indicator">
      <div class="loading-spinner"></div>
      <span>加载中...</span>
    </div>

    <!-- 空状态 -->
    <div v-if="!isLoading && bookmarks.length === 0" class="empty-state">
      <div class="empty-icon">📚</div>
      <div class="empty-title">暂无书签</div>
      <div class="empty-description">开始添加您的第一个书签吧</div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import { useVirtualizer } from '@tanstack/vue-virtual'
import BookmarkCard from './BookmarkCard.vue'
import type { BookmarkWithMeta } from '../types'

// Props
interface Props {
  bookmarks: BookmarkWithMeta[]
  viewMode: 'card' | 'list'
  selectedBookmarkIds: string[]
  isLoading?: boolean
  itemsPerRow?: number
  itemHeight?: number
}

const props = withDefaults(defineProps<Props>(), {
  isLoading: false,
  itemsPerRow: 5,
  itemHeight: 200
})

// Emits
const emit = defineEmits<{
  'bookmark-click': [bookmark: BookmarkWithMeta]
  'bookmark-right-click': [event: MouseEvent, bookmark: BookmarkWithMeta]
  'bookmark-select': [bookmark: BookmarkWithMeta]
  'bookmark-edit': [bookmark: BookmarkWithMeta]
  'bookmark-delete': [bookmark: BookmarkWithMeta]
}>()

// 响应式状态
const containerRef = ref<HTMLElement>()

// 计算属性
const rowCount = computed(() => {
  return Math.ceil(props.bookmarks.length / props.itemsPerRow)
})

const bookmarkRows = computed(() => {
  const rows: (BookmarkWithMeta | null)[][] = []
  
  for (let i = 0; i < rowCount.value; i++) {
    const row: (BookmarkWithMeta | null)[] = []
    
    for (let j = 0; j < props.itemsPerRow; j++) {
      const index = i * props.itemsPerRow + j
      row.push(props.bookmarks[index] || null)
    }
    
    rows.push(row)
  }
  
  return rows
})

// 虚拟化配置
const virtualizer = useVirtualizer({
  count: rowCount,
  getScrollElement: () => containerRef.value,
  estimateSize: () => props.itemHeight,
  overscan: 5, // 预渲染5行
})

// 方法
const getRowBookmarks = (rowIndex: number) => {
  return bookmarkRows.value[rowIndex] || []
}

const handleBookmarkClick = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-click', bookmark)
}

const handleBookmarkRightClick = (event: MouseEvent, bookmark: BookmarkWithMeta) => {
  event.preventDefault()
  emit('bookmark-right-click', event, bookmark)
}

const handleBookmarkSelect = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-select', bookmark)
}

const handleBookmarkEdit = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-edit', bookmark)
}

const handleBookmarkDelete = (bookmark: BookmarkWithMeta) => {
  emit('bookmark-delete', bookmark)
}

// 监听书签变化，重新计算虚拟化
watch(() => props.bookmarks.length, () => {
  virtualizer.measure()
})

// 监听视图模式变化，调整行高
watch(() => props.viewMode, (newMode) => {
  const newHeight = newMode === 'card' ? 200 : 80
  // 这里需要更新虚拟化器的估算高度
  virtualizer.measure()
})

// 生命周期
onMounted(() => {
  // 初始化虚拟化器
  virtualizer.measure()
})

onUnmounted(() => {
  // 清理资源
})
</script>

<style scoped>
.virtual-bookmark-grid {
  height: 100%;
  overflow: auto;
  position: relative;
}

.bookmark-row {
  display: grid;
  grid-template-columns: repeat(v-bind('props.itemsPerRow'), 1fr);
  gap: 1rem;
  padding: 0 1rem;
  height: 100%;
  align-items: start;
}

.bookmark-item {
  height: 100%;
  transition: all 0.2s ease;
}

.bookmark-item:not(.bookmark-empty):hover {
  transform: translateY(-2px);
}

.bookmark-empty {
  visibility: hidden;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 2rem;
  color: #6b7280;
}

.loading-spinner {
  width: 1.5rem;
  height: 1.5rem;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  color: #374151;
}

.empty-description {
  font-size: 0.875rem;
  opacity: 0.8;
}

/* 响应式调整 */
@media (max-width: 1200px) {
  .bookmark-row {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 768px) {
  .bookmark-row {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
    padding: 0 0.75rem;
  }
}

@media (max-width: 480px) {
  .bookmark-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
    padding: 0 0.5rem;
  }
}

/* 暗色主题 */
[data-theme="dark"] .empty-title {
  color: #f1f5f9;
}

[data-theme="dark"] .loading-indicator {
  color: #9ca3af;
}
</style>
