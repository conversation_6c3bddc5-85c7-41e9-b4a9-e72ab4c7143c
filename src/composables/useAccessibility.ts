/**
 * 可访问性增强组合式函数
 * 提供ARIA属性、屏幕阅读器支持等功能
 */

import { ref, computed, nextTick } from 'vue'

export interface AriaLiveRegion {
  id: string
  politeness: 'polite' | 'assertive' | 'off'
  atomic?: boolean
  relevant?: string
}

export interface AccessibilityOptions {
  enableLiveRegions?: boolean
  enableFocusManagement?: boolean
  enableKeyboardTraps?: boolean
  announceChanges?: boolean
}

export function useAccessibility(options: AccessibilityOptions = {}) {
  const {
    enableLiveRegions = true,
    enableFocusManagement = true,
    enableKeyboardTraps = false,
    announceChanges = true
  } = options

  // 响应式状态
  const liveRegions = ref<Map<string, AriaLiveRegion>>(new Map())
  const announcements = ref<string[]>([])
  const focusStack = ref<HTMLElement[]>([])

  // 创建live region
  const createLiveRegion = (
    id: string,
    politeness: 'polite' | 'assertive' = 'polite',
    options: { atomic?: boolean; relevant?: string } = {}
  ) => {
    if (!enableLiveRegions) return

    const region: AriaLiveRegion = {
      id,
      politeness,
      atomic: options.atomic,
      relevant: options.relevant
    }

    liveRegions.value.set(id, region)

    // 创建DOM元素
    nextTick(() => {
      let element = document.getElementById(id)
      if (!element) {
        element = document.createElement('div')
        element.id = id
        element.setAttribute('aria-live', politeness)
        element.setAttribute('aria-atomic', String(options.atomic || false))
        if (options.relevant) {
          element.setAttribute('aria-relevant', options.relevant)
        }
        element.style.position = 'absolute'
        element.style.left = '-10000px'
        element.style.width = '1px'
        element.style.height = '1px'
        element.style.overflow = 'hidden'
        document.body.appendChild(element)
      }
    })
  }

  // 向live region发送消息
  const announce = (message: string, regionId = 'default-live-region', politeness: 'polite' | 'assertive' = 'polite') => {
    if (!enableLiveRegions || !announceChanges) return

    // 确保live region存在
    if (!liveRegions.value.has(regionId)) {
      createLiveRegion(regionId, politeness)
    }

    announcements.value.push(message)

    nextTick(() => {
      const element = document.getElementById(regionId)
      if (element) {
        element.textContent = message
        
        // 清理旧消息
        setTimeout(() => {
          if (element.textContent === message) {
            element.textContent = ''
          }
        }, 1000)
      }
    })
  }

  // 焦点管理
  const pushFocus = (element: HTMLElement) => {
    if (!enableFocusManagement) return

    const currentFocus = document.activeElement as HTMLElement
    if (currentFocus && currentFocus !== element) {
      focusStack.value.push(currentFocus)
    }
    element.focus()
  }

  const popFocus = () => {
    if (!enableFocusManagement) return

    const previousElement = focusStack.value.pop()
    if (previousElement) {
      previousElement.focus()
    }
  }

  const trapFocus = (container: HTMLElement) => {
    if (!enableKeyboardTraps) return

    const focusableElements = container.querySelectorAll(
      'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
    ) as NodeListOf<HTMLElement>

    const firstElement = focusableElements[0]
    const lastElement = focusableElements[focusableElements.length - 1]

    const handleTabKey = (event: KeyboardEvent) => {
      if (event.key !== 'Tab') return

      if (event.shiftKey) {
        if (document.activeElement === firstElement) {
          event.preventDefault()
          lastElement.focus()
        }
      } else {
        if (document.activeElement === lastElement) {
          event.preventDefault()
          firstElement.focus()
        }
      }
    }

    container.addEventListener('keydown', handleTabKey)

    // 返回清理函数
    return () => {
      container.removeEventListener('keydown', handleTabKey)
    }
  }

  // ARIA属性辅助函数
  const setAriaLabel = (element: HTMLElement, label: string) => {
    element.setAttribute('aria-label', label)
  }

  const setAriaDescribedBy = (element: HTMLElement, describedById: string) => {
    element.setAttribute('aria-describedby', describedById)
  }

  const setAriaExpanded = (element: HTMLElement, expanded: boolean) => {
    element.setAttribute('aria-expanded', String(expanded))
  }

  const setAriaSelected = (element: HTMLElement, selected: boolean) => {
    element.setAttribute('aria-selected', String(selected))
  }

  const setAriaChecked = (element: HTMLElement, checked: boolean | 'mixed') => {
    element.setAttribute('aria-checked', String(checked))
  }

  const setAriaDisabled = (element: HTMLElement, disabled: boolean) => {
    element.setAttribute('aria-disabled', String(disabled))
  }

  const setAriaHidden = (element: HTMLElement, hidden: boolean) => {
    element.setAttribute('aria-hidden', String(hidden))
  }

  // 角色设置
  const setRole = (element: HTMLElement, role: string) => {
    element.setAttribute('role', role)
  }

  // 创建可访问的按钮
  const createAccessibleButton = (
    element: HTMLElement,
    label: string,
    options: {
      describedBy?: string
      expanded?: boolean
      pressed?: boolean
      disabled?: boolean
    } = {}
  ) => {
    setRole(element, 'button')
    setAriaLabel(element, label)
    
    if (options.describedBy) {
      setAriaDescribedBy(element, options.describedBy)
    }
    
    if (options.expanded !== undefined) {
      setAriaExpanded(element, options.expanded)
    }
    
    if (options.pressed !== undefined) {
      element.setAttribute('aria-pressed', String(options.pressed))
    }
    
    if (options.disabled !== undefined) {
      setAriaDisabled(element, options.disabled)
    }

    // 确保可以通过键盘访问
    if (!element.hasAttribute('tabindex')) {
      element.setAttribute('tabindex', '0')
    }

    // 添加键盘事件支持
    const handleKeydown = (event: KeyboardEvent) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault()
        element.click()
      }
    }

    element.addEventListener('keydown', handleKeydown)

    // 返回清理函数
    return () => {
      element.removeEventListener('keydown', handleKeydown)
    }
  }

  // 创建可访问的列表
  const createAccessibleList = (
    container: HTMLElement,
    items: HTMLElement[],
    options: {
      multiselectable?: boolean
      orientation?: 'vertical' | 'horizontal'
    } = {}
  ) => {
    setRole(container, 'listbox')
    
    if (options.multiselectable) {
      container.setAttribute('aria-multiselectable', 'true')
    }
    
    if (options.orientation) {
      container.setAttribute('aria-orientation', options.orientation)
    }

    items.forEach((item, index) => {
      setRole(item, 'option')
      item.setAttribute('aria-posinset', String(index + 1))
      item.setAttribute('aria-setsize', String(items.length))
      
      if (!item.hasAttribute('tabindex')) {
        item.setAttribute('tabindex', index === 0 ? '0' : '-1')
      }
    })
  }

  // 屏幕阅读器检测
  const isScreenReaderActive = computed(() => {
    // 简单的屏幕阅读器检测
    return window.navigator.userAgent.includes('NVDA') ||
           window.navigator.userAgent.includes('JAWS') ||
           window.speechSynthesis?.speaking ||
           false
  })

  // 减少动画偏好检测
  const prefersReducedMotion = computed(() => {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  })

  // 高对比度模式检测
  const prefersHighContrast = computed(() => {
    return window.matchMedia('(prefers-contrast: high)').matches
  })

  // 初始化默认live region
  createLiveRegion('default-live-region', 'polite')

  return {
    // 状态
    liveRegions,
    announcements,
    focusStack,
    isScreenReaderActive,
    prefersReducedMotion,
    prefersHighContrast,

    // Live regions
    createLiveRegion,
    announce,

    // 焦点管理
    pushFocus,
    popFocus,
    trapFocus,

    // ARIA属性
    setAriaLabel,
    setAriaDescribedBy,
    setAriaExpanded,
    setAriaSelected,
    setAriaChecked,
    setAriaDisabled,
    setAriaHidden,
    setRole,

    // 高级功能
    createAccessibleButton,
    createAccessibleList
  }
}
