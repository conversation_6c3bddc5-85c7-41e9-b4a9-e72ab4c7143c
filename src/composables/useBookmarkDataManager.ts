import { ref, computed, nextTick } from 'vue'
import type { BookmarkWithMeta, BookmarkGroup } from '../types'

// 双数据层管理器
export function useBookmarkDataManager() {
  // 界面层数据（用于显示和交互）
  const uiBookmarks = ref<BookmarkWithMeta[]>([])
  const uiGroups = ref<BookmarkGroup[]>([])

  // Chrome API 数据状态
  const isChromeSyncing = ref(false)
  const chromeSyncQueue = ref<Array<{ operation: () => Promise<void>; rollback?: () => void }>>([])

  // 操作历史（用于回滚）
  const operationHistory = ref<Array<{
    type: 'move' | 'delete' | 'update' | 'create'
    bookmarkId: string
    beforeState: any
    afterState: any
    timestamp: number
  }>>([])

  // 从 Chrome API 加载数据到界面层
  const loadFromChrome = async () => {
    console.log('📥 从 Chrome API 加载数据到界面层')

    if (typeof chrome === 'undefined' || !chrome.bookmarks) {
      console.log('🛠️ 开发环境：使用模拟数据')
      // 开发环境的模拟数据
      const mockBookmarks = [
        {
          id: '1',
          title: 'Google',
          url: 'https://google.com',
          parentId: '2',
          index: 0,
          tags: ['搜索'],
          icon: '',
          dateAdded: Date.now(),
          dateGroupModified: Date.now()
        },
        {
          id: '3',
          title: 'GitHub',
          url: 'https://github.com',
          parentId: '2',
          index: 1,
          tags: ['开发', '代码'],
          icon: '',
          dateAdded: Date.now(),
          dateGroupModified: Date.now()
        },
        {
          id: '4',
          title: 'Vue.js',
          url: 'https://vuejs.org',
          parentId: '5',
          index: 0,
          tags: ['前端', '框架'],
          icon: '',
          dateAdded: Date.now(),
          dateGroupModified: Date.now()
        }
      ]

      const mockGroups = [
        {
          id: '2',
          title: '书签栏',
          parentId: '0',
          index: 0,
          path: '书签栏'
        },
        {
          id: '5',
          title: '开发工具',
          parentId: '0',
          index: 1,
          path: '开发工具'
        }
      ]

      uiBookmarks.value = mockBookmarks
      uiGroups.value = mockGroups

      console.log('✅ 开发环境模拟数据加载完成:', {
        bookmarks: mockBookmarks.length,
        groups: mockGroups.length
      })
      return
    }

    try {
      const bookmarkTree = await chrome.bookmarks.getTree()
      const { bookmarks, groups } = await processBookmarkTree(bookmarkTree)

      // 处理标签和图标
      const enrichedBookmarks = await enrichBookmarksWithMetadata(bookmarks)

      // 更新界面层数据
      uiBookmarks.value = enrichedBookmarks
      uiGroups.value = groups

      console.log('✅ 界面层数据加载完成:', {
        bookmarks: enrichedBookmarks.length,
        groups: groups.length
      })
    } catch (error) {
      console.error('❌ 从 Chrome API 加载数据失败:', error)
    }
  }

  // 立即更新界面层数据（用于拖拽等操作）
  const updateUIData = (updates: {
    bookmarks?: BookmarkWithMeta[]
    groups?: BookmarkGroup[]
  }) => {
    console.log('🔄 立即更新界面层数据')
    
    if (updates.bookmarks) {
      uiBookmarks.value = updates.bookmarks
    }
    
    if (updates.groups) {
      uiGroups.value = updates.groups
    }
    
    console.log('✅ 界面层数据更新完成')
  }

  // 异步同步到 Chrome API
  const syncToChrome = async (task: { operation: () => Promise<void>; rollback?: () => void }) => {
    console.log('📤 添加 Chrome API 同步操作到队列')

    // 添加到同步队列
    chromeSyncQueue.value.push(task)

    // 如果没有正在同步，开始处理队列
    if (!isChromeSyncing.value) {
      await processSyncQueue()
    }
  }

  // 处理 Chrome API 同步队列
  const processSyncQueue = async () => {
    if (isChromeSyncing.value || chromeSyncQueue.value.length === 0) {
      return
    }

    isChromeSyncing.value = true
    console.log('🔄 开始处理 Chrome API 同步队列，队列长度:', chromeSyncQueue.value.length)

    try {
      while (chromeSyncQueue.value.length > 0) {
        const task = chromeSyncQueue.value.shift()
        if (task) {
          try {
            await task.operation()
            console.log('✅ Chrome API 同步操作完成')
          } catch (error) {
            console.error('❌ Chrome API 同步操作失败:', error)

            // 如果有回滚函数，执行回滚
            if (task.rollback) {
              console.log('🔄 执行回滚操作')
              task.rollback()
            }

            // 可以选择继续处理队列或停止
            // 这里选择继续处理，但记录错误
            console.warn('⚠️ 继续处理队列中的其他操作')
          }
        }
      }
    } catch (error) {
      console.error('❌ Chrome API 同步队列处理失败:', error)
      // 队列处理失败时，重新从 Chrome 加载数据以保持一致性
      console.log('🔄 重新从 Chrome 加载数据以保持一致性')
      await loadFromChrome()
    } finally {
      isChromeSyncing.value = false
      console.log('✅ Chrome API 同步队列处理完成')
    }
  }

  // 书签拖拽移动（双数据层）- 使用原生Chrome索引
  const moveBookmarkDualLayer = async (
    bookmarkId: string,
    sourceParentId: string,
    targetParentId: string,
    sourceIndex: number,
    targetIndex: number
  ) => {
    console.log('🎯 双数据层书签移动开始 (使用原生索引):', {
      bookmarkId,
      sourceParentId,
      targetParentId,
      sourceIndex,
      targetIndex
    })

    // 保存操作前的状态（用于回滚）
    const originalBookmarks = [...uiBookmarks.value]

    try {
      // 1. 先调用 Chrome API 移动书签（使用原生索引）
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        const moveParams: chrome.bookmarks.MoveDestination = {
          parentId: targetParentId,
          index: targetIndex
        }

        console.log('📤 Chrome API 移动书签 (原生索引):', bookmarkId, moveParams)
        await chrome.bookmarks.move(bookmarkId, moveParams)
        console.log('✅ Chrome API 书签移动完成')

        // 2. 重新从 Chrome 加载最新数据到界面层
        console.log('🔄 重新从 Chrome 加载最新数据')
        await loadFromChrome()
        console.log('✅ 界面层数据已同步最新的 Chrome 数据')

        return true
      } else {
        // 开发环境：模拟移动操作
        console.log('🛠️ 开发环境：模拟书签移动')

        const newBookmarks = [...uiBookmarks.value]
        const targetBookmark = newBookmarks.find(b => b.id === bookmarkId)

        if (targetBookmark) {
          // 简单更新书签位置（开发环境模拟）
          targetBookmark.parentId = targetParentId
          targetBookmark.index = targetIndex

          // 立即更新界面层数据
          updateUIData({ bookmarks: newBookmarks })
          console.log('✅ 开发环境模拟移动完成')
          return true
        }
      }
    } catch (error) {
      console.error('❌ 双数据层书签移动失败:', error)

      // 发生错误时回滚界面数据
      updateUIData({ bookmarks: originalBookmarks })

      // 如果 Chrome API 调用失败，重新加载数据以保持一致性
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        console.log('🔄 Chrome API 失败，重新加载数据以保持一致性')
        await loadFromChrome()
      }

      return false
    }

    return false
  }

  // 书签删除（双数据层）
  const deleteBookmarkDualLayer = async (bookmarkId: string) => {
    console.log('🗑️ 双数据层书签删除开始:', bookmarkId)

    // 保存操作前的状态（用于回滚）
    const originalBookmarks = [...uiBookmarks.value]
    const bookmarkToDelete = originalBookmarks.find(b => b.id === bookmarkId)

    if (!bookmarkToDelete) {
      console.error('❌ 找不到要删除的书签:', bookmarkId)
      return false
    }

    try {
      // 1. 立即更新界面层数据（用户看到即时反馈）
      console.log('⚡ 立即从界面移除书签')
      const newBookmarks = uiBookmarks.value.filter(b => b.id !== bookmarkId)
      updateUIData({ bookmarks: newBookmarks })
      console.log('✅ 界面层数据更新完成，书签已从界面移除')

      // 记录操作历史
      operationHistory.value.push({
        type: 'delete',
        bookmarkId,
        beforeState: bookmarkToDelete,
        afterState: null,
        timestamp: Date.now()
      })

      // 2. 异步同步到 Chrome API
      const rollback = () => {
        console.log('🔄 执行删除回滚操作')
        updateUIData({ bookmarks: originalBookmarks })
      }

      await syncToChrome({
        operation: async () => {
          if (typeof chrome !== 'undefined' && chrome.bookmarks) {
            console.log('📤 Chrome API 删除书签:', bookmarkId)
            await chrome.bookmarks.remove(bookmarkId)
            console.log('✅ Chrome API 书签删除完成')
          } else {
            console.log('🛠️ 开发环境：跳过 Chrome API 调用')
          }
        },
        rollback
      })

      console.log('✅ 双数据层书签删除完成')
      return true
    } catch (error) {
      console.error('❌ 双数据层书签删除失败:', error)
      // 发生错误时回滚界面数据
      updateUIData({ bookmarks: originalBookmarks })
      return false
    }
  }

  // 书签更新（双数据层）
  const updateBookmarkDualLayer = async (
    bookmarkId: string,
    updates: { title?: string; url?: string }
  ) => {
    console.log('📝 双数据层书签更新开始:', bookmarkId, updates)

    // 保存操作前的状态（用于回滚）
    const originalBookmarks = [...uiBookmarks.value]
    const bookmarkToUpdate = originalBookmarks.find(b => b.id === bookmarkId)

    if (!bookmarkToUpdate) {
      console.error('❌ 找不到要更新的书签:', bookmarkId)
      return false
    }

    const beforeState = {
      title: bookmarkToUpdate.title,
      url: bookmarkToUpdate.url
    }

    try {
      // 1. 立即更新界面层数据（用户看到即时反馈）
      console.log('⚡ 立即更新界面层书签数据')
      const newBookmarks = [...uiBookmarks.value]
      const targetBookmark = newBookmarks.find(b => b.id === bookmarkId)

      if (targetBookmark) {
        if (updates.title !== undefined) {
          targetBookmark.title = updates.title
        }
        if (updates.url !== undefined) {
          targetBookmark.url = updates.url
        }

        // 处理标签（如果标题包含标签分隔符）
        if (updates.title !== undefined) {
          const { cleanTitle, tags } = extractTagsFromTitle(updates.title)
          targetBookmark.title = cleanTitle
          targetBookmark.tags = tags
        }

        updateUIData({ bookmarks: newBookmarks })
        console.log('✅ 界面层数据更新完成，用户看到即时反馈')

        // 记录操作历史
        operationHistory.value.push({
          type: 'update',
          bookmarkId,
          beforeState,
          afterState: {
            title: targetBookmark.title,
            url: targetBookmark.url
          },
          timestamp: Date.now()
        })

        // 2. 异步同步到 Chrome API
        const rollback = () => {
          console.log('🔄 执行更新回滚操作')
          updateUIData({ bookmarks: originalBookmarks })
        }

        await syncToChrome({
          operation: async () => {
            if (typeof chrome !== 'undefined' && chrome.bookmarks) {
              console.log('📤 Chrome API 更新书签:', bookmarkId, updates)
              await chrome.bookmarks.update(bookmarkId, updates)
              console.log('✅ Chrome API 书签更新完成')
            } else {
              console.log('🛠️ 开发环境：跳过 Chrome API 调用')
            }
          },
          rollback
        })

        console.log('✅ 双数据层书签更新完成')
        return true
      }
    } catch (error) {
      console.error('❌ 双数据层书签更新失败:', error)
      // 发生错误时回滚界面数据
      updateUIData({ bookmarks: originalBookmarks })
      return false
    }

    return false
  }

  // 书签创建（双数据层）
  const createBookmarkDualLayer = async (bookmark: {
    title: string
    url: string
    parentId: string
    index?: number
  }) => {
    console.log('➕ 双数据层书签创建开始:', bookmark)

    // 生成临时 ID（在 Chrome API 返回真实 ID 之前使用）
    const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`

    // 处理标签
    const { cleanTitle, tags } = extractTagsFromTitle(bookmark.title)

    // 创建新书签对象
    const newBookmark: BookmarkWithMeta = {
      id: tempId,
      title: cleanTitle,
      url: bookmark.url,
      parentId: bookmark.parentId,
      index: bookmark.index || 0,
      tags,
      icon: '',
      dateAdded: Date.now(),
      dateGroupModified: Date.now()
    }

    // 保存操作前的状态（用于回滚）
    const originalBookmarks = [...uiBookmarks.value]

    try {
      // 1. 立即更新界面层数据（用户看到即时反馈）
      console.log('⚡ 立即添加书签到界面')
      const newBookmarks = [...uiBookmarks.value]

      // 调整同一文件夹中其他书签的索引
      if (bookmark.index !== undefined) {
        newBookmarks
          .filter(b => b.parentId === bookmark.parentId && b.index >= bookmark.index!)
          .forEach(b => b.index++)
      }

      newBookmarks.push(newBookmark)
      updateUIData({ bookmarks: newBookmarks })
      console.log('✅ 界面层数据更新完成，新书签已显示')

      // 记录操作历史
      operationHistory.value.push({
        type: 'create',
        bookmarkId: tempId,
        beforeState: null,
        afterState: newBookmark,
        timestamp: Date.now()
      })

      // 2. 异步同步到 Chrome API
      const rollback = () => {
        console.log('🔄 执行创建回滚操作')
        updateUIData({ bookmarks: originalBookmarks })
      }

      await syncToChrome({
        operation: async () => {
          if (typeof chrome !== 'undefined' && chrome.bookmarks) {
            console.log('📤 Chrome API 创建书签:', bookmark)
            const createdBookmark = await chrome.bookmarks.create({
              title: bookmark.title, // 使用原始标题（包含标签）
              url: bookmark.url,
              parentId: bookmark.parentId,
              index: bookmark.index
            })

            // 更新书签的真实 ID
            const updatedBookmarks = uiBookmarks.value.map(b =>
              b.id === tempId ? { ...b, id: createdBookmark.id } : b
            )
            updateUIData({ bookmarks: updatedBookmarks })

            console.log('✅ Chrome API 书签创建完成，ID 已更新:', createdBookmark.id)
          } else {
            console.log('🛠️ 开发环境：跳过 Chrome API 调用')
          }
        },
        rollback
      })

      console.log('✅ 双数据层书签创建完成')
      return newBookmark
    } catch (error) {
      console.error('❌ 双数据层书签创建失败:', error)
      // 发生错误时回滚界面数据
      updateUIData({ bookmarks: originalBookmarks })
      return null
    }
  }

  // 计算属性：分组后的书签
  const groupedBookmarks = computed(() => {
    const groups = uiGroups.value.map(group => ({
      ...group,
      bookmarks: uiBookmarks.value
        .filter(bookmark => bookmark.parentId === group.id)
        .sort((a, b) => (a.index || 0) - (b.index || 0))
    })).filter(group => group.bookmarks.length > 0)

    return groups
  })

  // 计算属性：所有可用标签
  const availableTags = computed(() => {
    const tagSet = new Set<string>()
    uiBookmarks.value.forEach(bookmark => {
      bookmark.tags?.forEach(tag => tagSet.add(tag))
    })
    return Array.from(tagSet).sort()
  })

  return {
    // 数据
    uiBookmarks: computed(() => uiBookmarks.value),
    uiGroups: computed(() => uiGroups.value),
    groupedBookmarks,
    availableTags,
    isChromeSyncing: computed(() => isChromeSyncing.value),
    operationHistory: computed(() => operationHistory.value),

    // 方法
    loadFromChrome,
    updateUIData,
    syncToChrome,
    moveBookmarkDualLayer,
    deleteBookmarkDualLayer,
    updateBookmarkDualLayer,
    createBookmarkDualLayer
  }
}

// 处理书签树的辅助函数
async function processBookmarkTree(bookmarkTree: chrome.bookmarks.BookmarkTreeNode[]): Promise<{
  bookmarks: BookmarkWithMeta[]
  groups: BookmarkGroup[]
}> {
  const bookmarks: BookmarkWithMeta[] = []
  const groups: BookmarkGroup[] = []
  
  // 递归处理书签树
  function processNode(node: chrome.bookmarks.BookmarkTreeNode, parentPath: string = '') {
    if (node.children) {
      // 这是一个文件夹
      if (node.id !== '0') { // 排除根节点
        groups.push({
          id: node.id,
          title: node.title || '未命名文件夹',
          parentId: node.parentId,
          index: node.index || 0,
          path: parentPath ? `${parentPath} > ${node.title}` : node.title || '未命名文件夹'
        })
      }
      
      // 处理子节点
      node.children.forEach(child => {
        processNode(child, parentPath ? `${parentPath} > ${node.title}` : node.title || '')
      })
    } else {
      // 这是一个书签
      bookmarks.push({
        id: node.id,
        title: node.title || '未命名书签',
        url: node.url || '',
        parentId: node.parentId || '',
        index: node.index || 0,
        dateAdded: node.dateAdded,
        dateGroupModified: node.dateGroupModified,
        tags: [], // 标签将在后续处理
        icon: '' // 图标将在后续加载
      })
    }
  }
  
  bookmarkTree.forEach(node => processNode(node))
  
  return { bookmarks, groups }
}

// 处理书签的标签和图标
async function enrichBookmarksWithMetadata(bookmarks: BookmarkWithMeta[]): Promise<BookmarkWithMeta[]> {
  const enriched = [...bookmarks]

  for (const bookmark of enriched) {
    // 处理标签（从标题中提取）
    const { cleanTitle, tags } = extractTagsFromTitle(bookmark.title)
    bookmark.title = cleanTitle
    bookmark.tags = tags

    // 加载图标
    if (typeof chrome !== 'undefined' && chrome.storage) {
      try {
        const iconKey = `icon_${bookmark.id}`
        const result = await new Promise<{ [key: string]: string }>((resolve) => {
          chrome.storage.local.get([iconKey], resolve)
        })
        bookmark.icon = result[iconKey] || ''
      } catch (error) {
        console.warn('加载图标失败:', bookmark.id, error)
        bookmark.icon = ''
      }
    }
  }

  return enriched
}

// 从标题中提取标签
function extractTagsFromTitle(title: string): { cleanTitle: string; tags: string[] } {
  // 默认分隔符
  const separator = '#'

  if (!title.includes(separator)) {
    return { cleanTitle: title, tags: [] }
  }

  const parts = title.split(separator)
  const cleanTitle = parts[0].trim()
  const tags = parts.slice(1)
    .map(tag => tag.trim())
    .filter(tag => tag.length > 0)

  return { cleanTitle, tags }
}
