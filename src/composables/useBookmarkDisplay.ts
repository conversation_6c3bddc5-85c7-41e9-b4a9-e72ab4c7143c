import { ref, computed } from 'vue'

// 书签显示风格状态
const bookmarkStyle = ref<'square' | 'rectangle'>('square')
const iconShape = ref<'circle' | 'rounded'>('rounded')

export function useBookmarkDisplay() {
  // 从localStorage加载设置
  const loadDisplaySettings = () => {
    try {
      const savedStyle = localStorage.getItem('bookmarkStyle')
      if (savedStyle && (savedStyle === 'square' || savedStyle === 'rectangle')) {
        bookmarkStyle.value = savedStyle
      }

      const savedShape = localStorage.getItem('iconShape')
      if (savedShape && (savedShape === 'circle' || savedShape === 'rounded')) {
        iconShape.value = savedShape
      }
    } catch (error) {
      console.error('加载书签显示设置失败:', error)
    }
  }

  // 保存设置到localStorage
  const saveDisplaySettings = () => {
    try {
      localStorage.setItem('bookmarkStyle', bookmarkStyle.value)
      localStorage.setItem('iconShape', iconShape.value)
    } catch (error) {
      console.error('保存书签显示设置失败:', error)
    }
  }

  // 设置书签显示风格
  const setBookmarkStyle = (style: 'square' | 'rectangle') => {
    bookmarkStyle.value = style
    saveDisplaySettings()
  }

  // 设置图标形状
  const setIconShape = (shape: 'circle' | 'rounded') => {
    iconShape.value = shape
    saveDisplaySettings()
  }

  // 初始化
  loadDisplaySettings()

  return {
    bookmarkStyle: computed(() => bookmarkStyle.value),
    iconShape: computed(() => iconShape.value),
    setBookmarkStyle,
    setIconShape,
    loadDisplaySettings,
    saveDisplaySettings
  }
}
