import { ref, computed, watch } from 'vue'
import type { BookmarkWithMeta } from '../types'
import { useTagColors } from './useTagColors'
import { useTagSettings } from './useTagSettings'
import { isDevelopment, mockGroups, allMockBookmarks } from '../data/mockData'

export function useBookmarks() {
  const bookmarks = ref<chrome.bookmarks.BookmarkTreeNode[]>([])
  const groups = ref<{ id: string; title: string; parentId?: string; path?: string[] }[]>([])
  const allFolders = ref<{ id: string; title: string; parentId?: string; path?: string[] }[]>([]) // 所有文件夹，包括空文件夹
  const localMetaMap = ref<Record<string, Partial<BookmarkWithMeta>>>({})
  const availableTags = ref<string[]>([])

  // 使用标签颜色管理
  const { cleanupUnusedTagColors, ensureTagColors } = useTagColors()

  // 使用标签设置
  const { isTagsEnabled } = useTagSettings()

  const loadBookmarks = async () => {
    // 开发环境使用假数据
    if (isDevelopment || typeof chrome === 'undefined' || !chrome.bookmarks) {
      console.log('使用开发环境假数据')

      // 尝试从 localStorage 加载保存的书签数据
      const savedBookmarkData = localStorage.getItem('bookmarkData')
      let flat: chrome.bookmarks.BookmarkTreeNode[]

      if (savedBookmarkData) {
        try {
          flat = JSON.parse(savedBookmarkData)
          console.log('从localStorage加载书签基础数据:', flat)
        } catch (error) {
          console.error('解析保存的书签数据失败:', error)
          // 回退到假数据
          flat = allMockBookmarks.map(bookmark => ({
            id: bookmark.id,
            title: bookmark.title,
            url: bookmark.url,
            parentId: bookmark.parentId,
            dateAdded: Date.now(),
            index: 0,
            syncing: false
          }))
        }
      } else {
        // 使用假数据初始化
        flat = allMockBookmarks.map(bookmark => ({
          id: bookmark.id,
          title: bookmark.title,
          url: bookmark.url,
          parentId: bookmark.parentId,
          dateAdded: Date.now(),
          index: 0,
          syncing: false
        }))
        console.log('使用假数据初始化书签基础数据')
      }

      bookmarks.value = flat
      groups.value = mockGroups

      // 从localStorage加载已保存的元数据
      try {
        const savedMeta = localStorage.getItem('bookmarkMeta')

        if (savedMeta) {
          const parsedMeta = JSON.parse(savedMeta)

          // 修复损坏的标签数据
          let hasFixedData = false
          Object.keys(parsedMeta).forEach(id => {
            if (parsedMeta[id].tags) {
              if (!Array.isArray(parsedMeta[id].tags)) {
                // 如果是对象（如 {"0": "测试"}），转换为数组
                if (typeof parsedMeta[id].tags === 'object') {
                  const fixedTags = Object.values(parsedMeta[id].tags).filter(tag => typeof tag === 'string') as string[]
                  parsedMeta[id].tags = fixedTags
                  hasFixedData = true
                } else {
                  parsedMeta[id].tags = []
                  hasFixedData = true
                }
              }
            }
          })

          // 如果修复了数据，重新保存
          if (hasFixedData) {
            localStorage.setItem('bookmarkMeta', JSON.stringify(parsedMeta))
          }

          localMetaMap.value = parsedMeta


        } else {
          // 如果没有保存的数据，使用假数据初始化
          const mockMeta: Record<string, Partial<BookmarkWithMeta>> = {}
          allMockBookmarks.forEach(bookmark => {
            if (bookmark.tags || bookmark.icon) {
              mockMeta[bookmark.id] = {
                tags: bookmark.tags,
                icon: bookmark.icon
              }
            }
          })
          localMetaMap.value = mockMeta
        }
      } catch (error) {
        console.error('❌ 加载元数据失败:', error)
        // 出错时使用假数据
        const mockMeta: Record<string, Partial<BookmarkWithMeta>> = {}
        allMockBookmarks.forEach(bookmark => {
          if (bookmark.tags || bookmark.icon) {
            mockMeta[bookmark.id] = {
              tags: bookmark.tags,
              icon: bookmark.icon
            }
          }
        })
        localMetaMap.value = mockMeta
      }

      // 手动触发标签更新，确保在开发环境中标签能及时显示
      setTimeout(() => {
        updateAvailableTags()
      }, 100)

      return
    }

    // 生产环境使用Chrome API
    return new Promise<void>((resolve, reject) => {
      chrome.bookmarks.getTree((tree: chrome.bookmarks.BookmarkTreeNode[] | undefined) => {
        if (!tree) {
          console.error('Failed to load bookmarks: tree is undefined')
          reject(new Error('Failed to load bookmarks: tree is undefined'))
          return
        }
        const flat: chrome.bookmarks.BookmarkTreeNode[] = []
        const folders: { id: string; title: string; parentId?: string; path?: string[] }[] = []
        const folderMap = new Map<string, chrome.bookmarks.BookmarkTreeNode>()

        // 构建文件夹路径的辅助函数
        const buildFolderPath = (folderId: string): string[] => {
          const path: string[] = []
          let currentFolder = folderMap.get(folderId)

          while (currentFolder && currentFolder.id !== '0' && currentFolder.id !== '1') {
            path.unshift(currentFolder.title || '')
            if (currentFolder.parentId) {
              currentFolder = folderMap.get(currentFolder.parentId)
            } else {
              break
            }
          }

          return path
        }

        const allFoldersData: { id: string; title: string; parentId?: string; path?: string[] }[] = []

        const traverse = (
          nodes: chrome.bookmarks.BookmarkTreeNode[],
          parentId: string | undefined = undefined
        ) => {
          for (const node of nodes) {
            if (node.children) {
              // 先将文件夹添加到映射中
              folderMap.set(node.id, { ...node, parentId })

              // 收集所有文件夹，包括空文件夹
              if (node.title) {
                // 对于顶层文件夹（书签栏、其他书签），将parentId设置为'0'
                const actualParentId = (node.id === '1' || node.id === '2') ? '0' : parentId

                allFoldersData.push({
                  id: node.id,
                  title: node.title,
                  parentId: actualParentId
                })

                // 只有包含书签的文件夹才添加到groups中（排除根节点，但包括书签栏和其他书签）
                if (node.id !== '0') {
                  folders.push({
                    id: node.id,
                    title: node.title,
                    parentId: actualParentId
                  })
                }
              }
              traverse(node.children, node.id)
            } else {
              flat.push({ ...node, parentId })
            }
          }
        }

        traverse(tree)

        // 为每个文件夹构建路径
        folders.forEach(folder => {
          folder.path = buildFolderPath(folder.id)
        })

        // 为所有文件夹构建路径
        allFoldersData.forEach(folder => {
          folder.path = buildFolderPath(folder.id)
        })

        bookmarks.value = flat
        groups.value = folders
        allFolders.value = allFoldersData

        chrome.storage.local.get('bookmarkMeta', (result: { [key: string]: any }) => {
          localMetaMap.value = result.bookmarkMeta || {}
          console.log('✅ Chrome API 书签数据加载完成')

          // 手动触发标签更新，确保标签能及时显示
          setTimeout(() => {
            updateAvailableTags()
          }, 100)

          resolve()
        })
      })
    })
  }

  // 从书签标题中提取标签和显示名称
  const extractTagsFromTitle = (title: string) => {
    // 如果标签功能未开启，直接返回原标题，不提取标签
    if (!isTagsEnabled.value) {
      return { displayTitle: title, tags: [] }
    }

    const separator = localStorage.getItem('tagSeparator') || '#'

    if (!title.includes(separator)) {
      return { displayTitle: title, tags: [] }
    }

    const parts = title.split(separator)
    const displayTitle = parts[0].trim()
    const tags = parts.slice(1).map(tag => tag.trim()).filter(tag => tag.length > 0)

    return { displayTitle, tags }
  }

  const enrichedBookmarks = computed<BookmarkWithMeta[]>(() => {
    const result = bookmarks.value.map((b) => {
      const meta = localMetaMap.value[b.id] || {}

      // 修复标签数据：如果是对象，转换为数组
      let metaTags: string[] = []
      if (Array.isArray(meta.tags)) {
        metaTags = meta.tags
      } else if (meta.tags && typeof meta.tags === 'object') {
        // 如果是对象（如 {"0": "测试"}），转换为数组
        metaTags = Object.values(meta.tags).filter(tag => typeof tag === 'string') as string[]
      }

      // 从标题中提取标签
      const { displayTitle, tags: extractedTags } = extractTagsFromTitle(b.title || '')

      // 如果标签功能未开启，不使用任何标签
      // 如果标签功能开启，则：如果标题中包含标签，只使用标题中的标签（避免重复）
      // 如果标题中没有标签，使用元数据中的标签
      const allTags = isTagsEnabled.value
        ? (extractedTags.length > 0 ? extractedTags : metaTags)
        : []

      const enriched = {
        id: b.id,
        title: b.title || '', // 保持原始标题
        displayTitle, // 显示用的标题（不包含标签）
        url: b.url || '',
        parentId: b.parentId,
        index: b.index, // 保留原始索引
        icon: meta.icon,
        tags: allTags,
      }

      return enriched
    })
    return result
  })

  const updateAvailableTags = () => {
    const tagSet = new Set<string>()
    enrichedBookmarks.value.forEach((b) => {
      // 确保 tags 是数组
      const tags = Array.isArray(b.tags) ? b.tags : []
      tags.forEach((tag) => {
        if (typeof tag === 'string' && tag.trim()) {
          tagSet.add(tag.trim())
        }
      })
    })
    const newTags = [...tagSet]
    availableTags.value = newTags

    // 确保所有活跃标签都有颜色
    ensureTagColors(newTags)

    // 清理未使用的标签颜色
    cleanupUnusedTagColors(newTags)
  }

  watch(enrichedBookmarks, updateAvailableTags, { deep: true })
  watch(localMetaMap, updateAvailableTags, { deep: true })

  const saveBookmarkMeta = async (id: string, meta: Partial<BookmarkWithMeta>) => {
    console.log('💾 保存书签元数据:', id, meta)

    // 确保标签数据是数组，并且是真正的数组而不是类数组对象
    let tagsToSave: string[] = []
    if (Array.isArray(meta.tags)) {
      tagsToSave = [...meta.tags] // 创建新数组
    } else if (meta.tags && typeof meta.tags === 'object') {
      // 如果是对象，转换为数组
      tagsToSave = Object.values(meta.tags).filter(tag => typeof tag === 'string') as string[]
    }

    // 创建新的 localMetaMap 对象以确保响应式更新
    const newLocalMetaMap = { ...localMetaMap.value }

    // 如果要删除图标，确保设置为 undefined
    if (meta.icon === undefined) {
      console.log('🗑️ 删除书签图标:', id)
      newLocalMetaMap[id] = {
        ...(newLocalMetaMap[id] || {}),
        ...meta,
        icon: undefined, // 明确设置为 undefined
        tags: tagsToSave,
      }
    } else {
      newLocalMetaMap[id] = {
        ...(newLocalMetaMap[id] || {}),
        ...meta,
        tags: tagsToSave, // 确保保存的是数组
      }
    }

    // 重新赋值以触发响应式更新
    localMetaMap.value = newLocalMetaMap

    // 立即保存到存储
    if (isDevelopment || typeof chrome === 'undefined' || !chrome.storage) {
      localStorage.setItem('bookmarkMeta', JSON.stringify(localMetaMap.value))
      console.log('✅ 元数据已保存到 localStorage')
    } else {
      await new Promise<void>((resolve) => {
        chrome.storage.local.set({ bookmarkMeta: localMetaMap.value }, () => {
          console.log('✅ 元数据已保存到 Chrome storage')
          resolve()
        })
      })
    }

    // 手动触发标签更新
    updateAvailableTags()
  }

  // 新增：保存完整书签数据的函数（开发环境专用）
  const saveBookmarkData = (id: string, bookmarkData: Partial<BookmarkWithMeta>) => {
    if (isDevelopment || typeof chrome === 'undefined' || !chrome.bookmarks) {
      // 在开发环境中，我们需要同时更新基础书签数据和元数据

      // 创建新的书签数组以确保响应式更新
      const newBookmarks = [...bookmarks.value]
      const bookmarkIndex = newBookmarks.findIndex(b => b.id === id)

      if (bookmarkIndex !== -1) {
        // 更新现有书签
        newBookmarks[bookmarkIndex] = {
          ...newBookmarks[bookmarkIndex],
          title: bookmarkData.title || newBookmarks[bookmarkIndex].title,
          url: bookmarkData.url || newBookmarks[bookmarkIndex].url,
        }
      } else {
        // 添加新书签
        const newBookmark = {
          id: id,
          title: bookmarkData.title || '新书签',
          url: bookmarkData.url || 'https://example.com',
          parentId: bookmarkData.parentId,
          dateAdded: Date.now(),
          index: newBookmarks.length,
          syncing: false
        }
        newBookmarks.push(newBookmark)
      }

      // 更新响应式数据
      bookmarks.value = newBookmarks

      // 保存基础书签数据到 localStorage
      localStorage.setItem('bookmarkData', JSON.stringify(bookmarks.value))

      // 保存元数据（包括标签）
      const metaToSave = {
        icon: bookmarkData.icon,
        tags: Array.isArray(bookmarkData.tags) ? bookmarkData.tags : []
      }


      saveBookmarkMeta(id, metaToSave)
    }
  }

  return {
    bookmarks,
    groups,
    allFolders,
    enrichedBookmarks,
    availableTags,
    loadBookmarks,
    saveBookmarkMeta,
    saveBookmarkData,
  }
}


