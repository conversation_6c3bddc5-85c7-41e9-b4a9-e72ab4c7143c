/**
 * 图标预加载 Composable
 * 提供智能的图标预加载和缓存管理功能
 */

import { ref, computed, reactive } from 'vue'
import { enhancedIconService } from '../services/enhancedIconService'
import type { BookmarkWithMeta } from '../types'

export interface PreloadProgress {
  current: number
  total: number
  percentage: number
  currentUrl: string
  status: 'idle' | 'loading' | 'completed' | 'error'
}

export interface PreloadStats {
  totalRequested: number
  successful: number
  failed: number
  cached: number
  duration: number
}

export function useIconPreloader() {
  // 响应式状态
  const isPreloading = ref(false)
  const progress = reactive<PreloadProgress>({
    current: 0,
    total: 0,
    percentage: 0,
    currentUrl: '',
    status: 'idle'
  })

  const stats = reactive<PreloadStats>({
    totalRequested: 0,
    successful: 0,
    failed: 0,
    cached: 0,
    duration: 0
  })

  // 预加载队列
  const preloadQueue = ref<string[]>([])
  const failedUrls = ref<string[]>([])
  const successfulUrls = ref<string[]>([])

  // 计算属性
  const isCompleted = computed(() => progress.status === 'completed')
  const hasErrors = computed(() => failedUrls.value.length > 0)
  const successRate = computed(() => {
    if (stats.totalRequested === 0) return 0
    return Math.round((stats.successful / stats.totalRequested) * 100)
  })

  /**
   * 预加载书签图标
   * @param bookmarks 书签列表
   * @param options 预加载选项
   */
  const preloadBookmarkIcons = async (
    bookmarks: BookmarkWithMeta[],
    options: {
      batchSize?: number
      delayBetweenBatches?: number
      retryFailed?: boolean
    } = {}
  ) => {
    const {
      batchSize = 5,
      delayBetweenBatches = 100,
      retryFailed = false
    } = options

    // 提取所有有效的URL
    const urls = bookmarks
      .filter(bookmark => bookmark.url && bookmark.url.startsWith('http'))
      .map(bookmark => bookmark.url)

    if (urls.length === 0) {
      console.warn('没有找到有效的书签URL')
      return
    }

    // 初始化状态
    resetStats()
    progress.status = 'loading'
    progress.total = urls.length
    progress.current = 0
    isPreloading.value = true
    preloadQueue.value = [...urls]
    failedUrls.value = []
    successfulUrls.value = []

    const startTime = Date.now()

    try {
      // 分批处理URL
      for (let i = 0; i < urls.length; i += batchSize) {
        const batch = urls.slice(i, i + batchSize)
        
        // 并行处理当前批次
        const batchPromises = batch.map(async (url) => {
          progress.currentUrl = url
          
          try {
            const result = await enhancedIconService.getWebsiteIcon(url, !retryFailed)
            
            if (result.success) {
              stats.successful++
              successfulUrls.value.push(url)
              if (result.source === 'cache') {
                stats.cached++
              }
            } else {
              stats.failed++
              failedUrls.value.push(url)
            }
          } catch (error) {
            console.warn(`预加载图标失败: ${url}`, error)
            stats.failed++
            failedUrls.value.push(url)
          }
          
          progress.current++
          progress.percentage = Math.round((progress.current / progress.total) * 100)
        })

        await Promise.allSettled(batchPromises)

        // 批次间延迟，避免过度请求
        if (i + batchSize < urls.length && delayBetweenBatches > 0) {
          await new Promise(resolve => setTimeout(resolve, delayBetweenBatches))
        }
      }

      progress.status = 'completed'
    } catch (error) {
      console.error('预加载过程中出现错误:', error)
      progress.status = 'error'
    } finally {
      stats.duration = Date.now() - startTime
      stats.totalRequested = urls.length
      isPreloading.value = false
      progress.currentUrl = ''
    }
  }

  /**
   * 重试失败的图标
   */
  const retryFailedIcons = async () => {
    if (failedUrls.value.length === 0) {
      console.warn('没有失败的图标需要重试')
      return
    }

    const urlsToRetry = [...failedUrls.value]
    failedUrls.value = []

    // 创建临时书签对象用于重试
    const tempBookmarks = urlsToRetry.map(url => ({
      id: `temp-${Date.now()}-${Math.random()}`,
      url,
      title: '',
      tags: []
    })) as BookmarkWithMeta[]

    await preloadBookmarkIcons(tempBookmarks, {
      batchSize: 3,
      delayBetweenBatches: 200,
      retryFailed: true
    })
  }

  /**
   * 预加载指定域名的图标
   * @param domains 域名列表
   */
  const preloadDomainIcons = async (domains: string[]) => {
    const urls = domains.map(domain => `https://${domain}`)
    const tempBookmarks = urls.map(url => ({
      id: `domain-${Date.now()}-${Math.random()}`,
      url,
      title: '',
      tags: []
    })) as BookmarkWithMeta[]

    await preloadBookmarkIcons(tempBookmarks)
  }

  /**
   * 智能预加载 - 基于使用频率和重要性
   * @param bookmarks 书签列表
   */
  const smartPreload = async (bookmarks: BookmarkWithMeta[]) => {
    // 按优先级排序书签
    const prioritizedBookmarks = [...bookmarks].sort((a, b) => {
      // 优先级计算：最近访问 > 访问频率 > 标签数量
      const scoreA = calculatePriority(a)
      const scoreB = calculatePriority(b)
      return scoreB - scoreA
    })

    // 分阶段预加载
    const highPriority = prioritizedBookmarks.slice(0, 20) // 前20个高优先级
    const mediumPriority = prioritizedBookmarks.slice(20, 50) // 中等优先级
    const lowPriority = prioritizedBookmarks.slice(50) // 低优先级

    // 第一阶段：高优先级，快速加载
    if (highPriority.length > 0) {
      await preloadBookmarkIcons(highPriority, {
        batchSize: 8,
        delayBetweenBatches: 50
      })
    }

    // 第二阶段：中等优先级，适中速度
    if (mediumPriority.length > 0) {
      await preloadBookmarkIcons(mediumPriority, {
        batchSize: 5,
        delayBetweenBatches: 100
      })
    }

    // 第三阶段：低优先级，后台加载
    if (lowPriority.length > 0) {
      await preloadBookmarkIcons(lowPriority, {
        batchSize: 3,
        delayBetweenBatches: 200
      })
    }
  }

  /**
   * 计算书签优先级
   */
  const calculatePriority = (bookmark: BookmarkWithMeta): number => {
    let score = 0
    
    // 基础分数
    score += 10
    
    // 标签数量加分
    score += (bookmark.tags?.length || 0) * 5
    
    // URL长度影响（短URL通常是主要网站）
    if (bookmark.url.length < 30) score += 10
    
    // 常见域名加分
    const domain = extractDomain(bookmark.url)
    const popularDomains = ['github.com', 'google.com', 'stackoverflow.com', 'youtube.com']
    if (popularDomains.some(d => domain.includes(d))) {
      score += 20
    }
    
    return score
  }

  /**
   * 提取域名
   */
  const extractDomain = (url: string): string => {
    try {
      return new URL(url).hostname
    } catch {
      return url
    }
  }

  /**
   * 重置统计信息
   */
  const resetStats = () => {
    stats.totalRequested = 0
    stats.successful = 0
    stats.failed = 0
    stats.cached = 0
    stats.duration = 0
  }

  /**
   * 停止预加载
   */
  const stopPreloading = () => {
    isPreloading.value = false
    progress.status = 'idle'
    progress.current = 0
    progress.total = 0
    progress.percentage = 0
    progress.currentUrl = ''
  }

  /**
   * 获取预加载报告
   */
  const getPreloadReport = () => {
    return {
      progress: { ...progress },
      stats: { ...stats },
      failedUrls: [...failedUrls.value],
      successfulUrls: [...successfulUrls.value],
      successRate: successRate.value,
      isCompleted: isCompleted.value,
      hasErrors: hasErrors.value
    }
  }

  return {
    // 状态
    isPreloading,
    progress,
    stats,
    failedUrls,
    successfulUrls,
    
    // 计算属性
    isCompleted,
    hasErrors,
    successRate,
    
    // 方法
    preloadBookmarkIcons,
    retryFailedIcons,
    preloadDomainIcons,
    smartPreload,
    stopPreloading,
    resetStats,
    getPreloadReport
  }
}
