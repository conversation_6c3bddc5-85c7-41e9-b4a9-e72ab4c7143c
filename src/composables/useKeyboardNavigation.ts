/**
 * 键盘导航组合式函数
 * 提供完整的键盘导航支持
 */

import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface NavigationItem {
  id: string
  element?: HTMLElement
  focusable: boolean
  disabled?: boolean
  group?: string
}

export interface KeyboardNavigationOptions {
  enableArrowKeys?: boolean
  enableTabNavigation?: boolean
  enableEnterActivation?: boolean
  enableEscapeClose?: boolean
  wrapAround?: boolean
  skipDisabled?: boolean
}

export function useKeyboardNavigation(
  items: NavigationItem[],
  options: KeyboardNavigationOptions = {}
) {
  const {
    enableArrowKeys = true,
    enableTabNavigation = true,
    enableEnterActivation = true,
    enableEscapeClose = true,
    wrapAround = true,
    skipDisabled = true
  } = options

  // 响应式状态
  const currentIndex = ref(0)
  const isNavigating = ref(false)

  // 计算属性
  const focusableItems = computed(() => {
    return items.filter(item => item.focusable && (!skipDisabled || !item.disabled))
  })

  const currentItem = computed(() => {
    return focusableItems.value[currentIndex.value]
  })

  // 方法
  const focusItem = (index: number) => {
    const item = focusableItems.value[index]
    if (item?.element) {
      item.element.focus()
      currentIndex.value = index
    }
  }

  const focusNext = () => {
    const nextIndex = currentIndex.value + 1
    if (nextIndex < focusableItems.value.length) {
      focusItem(nextIndex)
    } else if (wrapAround) {
      focusItem(0)
    }
  }

  const focusPrevious = () => {
    const prevIndex = currentIndex.value - 1
    if (prevIndex >= 0) {
      focusItem(prevIndex)
    } else if (wrapAround) {
      focusItem(focusableItems.value.length - 1)
    }
  }

  const focusFirst = () => {
    if (focusableItems.value.length > 0) {
      focusItem(0)
    }
  }

  const focusLast = () => {
    if (focusableItems.value.length > 0) {
      focusItem(focusableItems.value.length - 1)
    }
  }

  const activateCurrentItem = () => {
    const item = currentItem.value
    if (item?.element) {
      // 触发点击事件
      item.element.click()
    }
  }

  // 键盘事件处理
  const handleKeydown = (event: KeyboardEvent) => {
    if (!isNavigating.value) return

    switch (event.key) {
      case 'ArrowDown':
      case 'ArrowRight':
        if (enableArrowKeys) {
          event.preventDefault()
          focusNext()
        }
        break

      case 'ArrowUp':
      case 'ArrowLeft':
        if (enableArrowKeys) {
          event.preventDefault()
          focusPrevious()
        }
        break

      case 'Home':
        event.preventDefault()
        focusFirst()
        break

      case 'End':
        event.preventDefault()
        focusLast()
        break

      case 'Enter':
      case ' ':
        if (enableEnterActivation) {
          event.preventDefault()
          activateCurrentItem()
        }
        break

      case 'Escape':
        if (enableEscapeClose) {
          event.preventDefault()
          stopNavigation()
        }
        break

      case 'Tab':
        if (!enableTabNavigation) {
          event.preventDefault()
        }
        break
    }
  }

  // 导航控制
  const startNavigation = (initialIndex = 0) => {
    isNavigating.value = true
    currentIndex.value = Math.max(0, Math.min(initialIndex, focusableItems.value.length - 1))
    
    if (focusableItems.value.length > 0) {
      focusItem(currentIndex.value)
    }
  }

  const stopNavigation = () => {
    isNavigating.value = false
  }

  // 查找项目索引
  const findItemIndex = (id: string): number => {
    return focusableItems.value.findIndex(item => item.id === id)
  }

  const focusItemById = (id: string) => {
    const index = findItemIndex(id)
    if (index !== -1) {
      focusItem(index)
    }
  }

  // 生命周期
  onMounted(() => {
    document.addEventListener('keydown', handleKeydown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeydown)
  })

  return {
    // 状态
    currentIndex,
    isNavigating,
    currentItem,
    focusableItems,

    // 方法
    focusItem,
    focusNext,
    focusPrevious,
    focusFirst,
    focusLast,
    activateCurrentItem,
    startNavigation,
    stopNavigation,
    findItemIndex,
    focusItemById
  }
}

/**
 * 网格导航组合式函数
 * 专门用于二维网格布局的键盘导航
 */
export function useGridNavigation(
  rows: number,
  columns: number,
  items: NavigationItem[],
  options: KeyboardNavigationOptions = {}
) {
  const baseNavigation = useKeyboardNavigation(items, options)

  // 计算当前位置
  const currentRow = computed(() => Math.floor(baseNavigation.currentIndex.value / columns))
  const currentColumn = computed(() => baseNavigation.currentIndex.value % columns)

  // 网格导航方法
  const moveUp = () => {
    const newRow = currentRow.value - 1
    if (newRow >= 0) {
      const newIndex = newRow * columns + currentColumn.value
      if (newIndex < baseNavigation.focusableItems.value.length) {
        baseNavigation.focusItem(newIndex)
      }
    }
  }

  const moveDown = () => {
    const newRow = currentRow.value + 1
    const newIndex = newRow * columns + currentColumn.value
    if (newIndex < baseNavigation.focusableItems.value.length) {
      baseNavigation.focusItem(newIndex)
    }
  }

  const moveLeft = () => {
    if (currentColumn.value > 0) {
      baseNavigation.focusPrevious()
    }
  }

  const moveRight = () => {
    if (currentColumn.value < columns - 1) {
      baseNavigation.focusNext()
    }
  }

  // 重写键盘事件处理
  const handleGridKeydown = (event: KeyboardEvent) => {
    if (!baseNavigation.isNavigating.value) return

    switch (event.key) {
      case 'ArrowUp':
        event.preventDefault()
        moveUp()
        break

      case 'ArrowDown':
        event.preventDefault()
        moveDown()
        break

      case 'ArrowLeft':
        event.preventDefault()
        moveLeft()
        break

      case 'ArrowRight':
        event.preventDefault()
        moveRight()
        break

      default:
        // 其他键由基础导航处理
        break
    }
  }

  // 替换基础导航的键盘处理
  onMounted(() => {
    document.addEventListener('keydown', handleGridKeydown)
  })

  onUnmounted(() => {
    document.removeEventListener('keydown', handleGridKeydown)
  })

  return {
    ...baseNavigation,
    
    // 网格特有属性
    currentRow,
    currentColumn,
    rows,
    columns,

    // 网格特有方法
    moveUp,
    moveDown,
    moveLeft,
    moveRight
  }
}
