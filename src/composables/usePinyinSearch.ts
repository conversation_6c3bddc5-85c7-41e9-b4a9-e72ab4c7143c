import { pinyin, match } from 'pinyin-pro'
import type { BookmarkWithMeta } from '../types'

export interface SearchOptions {
  enableInitials?: boolean    // 启用拼音首字母搜索
  enableFullPinyin?: boolean  // 启用全拼搜索
  enableFuzzy?: boolean       // 启用模糊匹配
  enableEnglish?: boolean     // 启用英文搜索
}

// 搜索结果缓存
const searchCache = new Map<string, BookmarkWithMeta[]>()
const MAX_CACHE_SIZE = 100

// 清理缓存
function clearOldCache() {
  if (searchCache.size > MAX_CACHE_SIZE) {
    const keys = Array.from(searchCache.keys())
    // 删除最旧的一半缓存
    for (let i = 0; i < keys.length / 2; i++) {
      searchCache.delete(keys[i])
    }
  }
}

// 生成缓存键
function getCacheKey(bookmarks: BookmarkWithMeta[], query: string, options: SearchOptions): string {
  const optionsStr = JSON.stringify(options)
  const bookmarksHash = bookmarks.length.toString() // 简单的书签数量作为哈希
  return `${query}-${optionsStr}-${bookmarksHash}`
}

// 检查文本是否包含中文字符
function containsChinese(text: string): boolean {
  return /[\u4e00-\u9fa5]/.test(text)
}

// 检查文本是否包含英文字符
function containsEnglish(text: string): boolean {
  return /[a-zA-Z]/.test(text)
}

// 标准化搜索查询
function normalizeQuery(query: string): string {
  return query.toLowerCase().trim()
}

// 使用 pinyin-pro 进行拼音匹配
function matchWithPinyin(text: string, query: string, options: SearchOptions): boolean {
  if (!text || !query) return false
  
  const normalizedQuery = normalizeQuery(query)
  const normalizedText = text.toLowerCase()
  
  // 1. 直接文本匹配（包括英文）
  if (normalizedText.includes(normalizedQuery)) {
    return true
  }
  
  // 如果查询不包含中文，且文本也不包含中文，只进行英文匹配
  if (!containsChinese(query) && !containsChinese(text)) {
    return normalizedText.includes(normalizedQuery)
  }
  
  // 如果文本不包含中文，跳过拼音匹配
  if (!containsChinese(text)) {
    return false
  }
  
  try {
    // 2. 拼音首字母匹配
    if (options.enableInitials) {
      const result = match(text, normalizedQuery, { 
        precision: 'first',
        continuous: false,
        space: 'ignore'
      })
      if (result !== null) return true
    }
    
    // 3. 全拼匹配
    if (options.enableFullPinyin) {
      const result = match(text, normalizedQuery, { 
        precision: 'every',
        continuous: false,
        space: 'ignore'
      })
      if (result !== null) return true
    }
    
    // 4. 模糊匹配
    if (options.enableFuzzy) {
      // 开始匹配：以查询开头
      const startResult = match(text, normalizedQuery, { 
        precision: 'start',
        continuous: false,
        space: 'ignore'
      })
      if (startResult !== null) return true
      
      // 任意匹配：包含查询的任意字符
      const anyResult = match(text, normalizedQuery, { 
        precision: 'any',
        continuous: false,
        space: 'ignore'
      })
      if (anyResult !== null) return true
    }
    
  } catch (error) {
    console.warn('拼音匹配出错:', error)
    // 如果拼音匹配出错，回退到简单的文本匹配
    return normalizedText.includes(normalizedQuery)
  }
  
  return false
}

// 搜索书签
function searchBookmarks(
  bookmarks: BookmarkWithMeta[], 
  query: string, 
  options: SearchOptions = {}
): BookmarkWithMeta[] {
  if (!query.trim()) return bookmarks
  
  // 设置默认选项
  const searchOptions: SearchOptions = {
    enableInitials: true,
    enableFullPinyin: true,
    enableFuzzy: true,
    enableEnglish: true,
    ...options
  }
  
  // 检查缓存
  const cacheKey = getCacheKey(bookmarks, query, searchOptions)
  if (searchCache.has(cacheKey)) {
    return searchCache.get(cacheKey)!
  }
  
  const normalizedQuery = normalizeQuery(query)
  
  const results = bookmarks.filter(bookmark => {
    // 搜索标题
    if (matchWithPinyin(bookmark.title, normalizedQuery, searchOptions)) {
      return true
    }
    
    // 搜索URL
    if (searchOptions.enableEnglish && bookmark.url) {
      if (bookmark.url.toLowerCase().includes(normalizedQuery)) {
        return true
      }
    }
    
    // 搜索标签
    if (bookmark.tags && bookmark.tags.length > 0) {
      for (const tag of bookmark.tags) {
        if (matchWithPinyin(tag, normalizedQuery, searchOptions)) {
          return true
        }
      }
    }
    
    return false
  })
  
  // 缓存结果
  searchCache.set(cacheKey, results)
  clearOldCache()
  
  return results
}

// 高亮匹配的文本
function highlightMatch(text: string, query: string): string {
  if (!text || !query) return text
  
  const normalizedQuery = normalizeQuery(query)
  
  // 直接文本匹配高亮
  const regex = new RegExp(`(${normalizedQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi')
  let highlighted = text.replace(regex, '<mark>$1</mark>')
  
  // 如果没有直接匹配，尝试拼音匹配高亮
  if (!highlighted.includes('<mark>') && containsChinese(text)) {
    try {
      // 尝试获取匹配的字符位置
      const matchResult = match(text, normalizedQuery, { 
        precision: 'first',
        continuous: false,
        space: 'ignore'
      })
      
      if (matchResult !== null && matchResult.length > 0) {
        // 高亮匹配的字符
        const chars = text.split('')
        matchResult.forEach(index => {
          if (index < chars.length) {
            chars[index] = `<mark>${chars[index]}</mark>`
          }
        })
        highlighted = chars.join('')
      }
    } catch (error) {
      console.warn('拼音高亮出错:', error)
    }
  }
  
  return highlighted
}

export function usePinyinSearch() {
  return {
    searchBookmarks,
    highlightMatch,
    clearCache: () => searchCache.clear()
  }
}
