/**
 * 智能图标预加载 Composable
 * 在应用启动时自动预加载重要书签的图标
 */

import { ref, onMounted, watch } from 'vue'
import { useBookmarkStore } from '../stores/bookmarks'
import { useSettingsStore } from '../stores/settings'
import { useIconPreloader } from './useIconPreloader'
import { enhancedIconService } from '../services/enhancedIconService'

export interface SmartPreloadConfig {
  enabled: boolean
  maxInitialLoad: number
  batchSize: number
  delayBetweenBatches: number
  priorityThreshold: number
  autoRetry: boolean
}

export function useSmartIconPreload() {
  const bookmarksStore = useBookmarkStore()
  const settingsStore = useSettingsStore()
  const iconPreloader = useIconPreloader()

  // 预加载状态
  const isAutoPreloading = ref(false)
  const preloadProgress = ref(0)
  const preloadTotal = ref(0)
  const lastPreloadTime = ref<number>(0)

  // 默认配置
  const defaultConfig: SmartPreloadConfig = {
    enabled: true,
    maxInitialLoad: 30, // 最多预加载30个图标
    batchSize: 5,       // 每批5个
    delayBetweenBatches: 200, // 批次间延迟200ms
    priorityThreshold: 15,    // 优先级阈值
    autoRetry: true     // 自动重试失败的图标
  }

  // 从设置中获取配置
  const getConfig = (): SmartPreloadConfig => {
    const userConfig = settingsStore.settings.iconPreload || {}
    return { ...defaultConfig, ...userConfig }
  }

  /**
   * 计算书签优先级
   * 基于多个因素：访问频率、标签数量、域名重要性等
   */
  const calculateBookmarkPriority = (bookmark: any): number => {
    let score = 0

    // 基础分数
    score += 5

    // 标签数量加分（有标签说明用户重视）
    const tagCount = bookmark.tags?.length || 0
    score += tagCount * 3

    // 标题长度影响（适中长度通常是有意义的书签）
    const titleLength = bookmark.title?.length || 0
    if (titleLength >= 10 && titleLength <= 50) {
      score += 5
    }

    // URL特征分析
    const url = bookmark.url || ''
    const domain = extractDomain(url)

    // 常见重要域名加分
    const importantDomains = [
      'github.com', 'stackoverflow.com', 'developer.mozilla.org',
      'google.com', 'youtube.com', 'twitter.com', 'linkedin.com',
      'medium.com', 'dev.to', 'codepen.io', 'jsfiddle.net',
      'npmjs.com', 'vuejs.org', 'reactjs.org', 'angular.io'
    ]

    if (importantDomains.some(d => domain.includes(d))) {
      score += 10
    }

    // 开发相关域名加分
    const devKeywords = ['api', 'docs', 'documentation', 'tutorial', 'guide']
    if (devKeywords.some(keyword => 
      url.toLowerCase().includes(keyword) || 
      bookmark.title?.toLowerCase().includes(keyword)
    )) {
      score += 8
    }

    // HTTPS加分（更安全的网站）
    if (url.startsWith('https://')) {
      score += 2
    }

    // 短URL通常是主要网站
    if (url.length < 50) {
      score += 3
    }

    return score
  }

  /**
   * 提取域名
   */
  const extractDomain = (url: string): string => {
    try {
      return new URL(url).hostname
    } catch {
      return url
    }
  }

  /**
   * 获取高优先级书签
   */
  const getHighPriorityBookmarks = (config: SmartPreloadConfig) => {
    const allBookmarks = bookmarksStore.enrichedBookmarks.filter(
      bookmark => bookmark.url && bookmark.url.startsWith('http')
    )

    // 计算优先级并排序
    const prioritizedBookmarks = allBookmarks
      .map(bookmark => ({
        ...bookmark,
        priority: calculateBookmarkPriority(bookmark)
      }))
      .filter(bookmark => bookmark.priority >= config.priorityThreshold)
      .sort((a, b) => b.priority - a.priority)
      .slice(0, config.maxInitialLoad)

    return prioritizedBookmarks
  }

  /**
   * 执行智能预加载
   */
  const executeSmartPreload = async (force = false) => {
    const config = getConfig()
    
    if (!config.enabled && !force) {
      console.log('智能图标预加载已禁用')
      return
    }

    // 检查是否需要预加载（避免频繁预加载）
    const now = Date.now()
    const timeSinceLastPreload = now - lastPreloadTime.value
    const minInterval = 5 * 60 * 1000 // 5分钟最小间隔

    if (!force && timeSinceLastPreload < minInterval) {
      console.log('距离上次预加载时间太短，跳过')
      return
    }

    isAutoPreloading.value = true
    lastPreloadTime.value = now

    try {
      const highPriorityBookmarks = getHighPriorityBookmarks(config)
      
      if (highPriorityBookmarks.length === 0) {
        console.log('没有找到高优先级书签')
        return
      }

      console.log(`开始智能预加载 ${highPriorityBookmarks.length} 个高优先级书签图标`)

      preloadTotal.value = highPriorityBookmarks.length
      preloadProgress.value = 0

      // 使用图标预加载器
      await iconPreloader.preloadBookmarkIcons(highPriorityBookmarks, {
        batchSize: config.batchSize,
        delayBetweenBatches: config.delayBetweenBatches
      })

      // 如果启用自动重试，重试失败的图标
      if (config.autoRetry && iconPreloader.failedUrls.value.length > 0) {
        console.log(`重试 ${iconPreloader.failedUrls.value.length} 个失败的图标`)
        await iconPreloader.retryFailedIcons()
      }

      const report = iconPreloader.getPreloadReport()
      console.log('智能预加载完成:', {
        总数: report.stats.totalRequested,
        成功: report.stats.successful,
        失败: report.stats.failed,
        缓存命中: report.stats.cached,
        成功率: `${report.successRate}%`,
        耗时: `${report.stats.duration}ms`
      })

    } catch (error) {
      console.error('智能预加载失败:', error)
    } finally {
      isAutoPreloading.value = false
      preloadProgress.value = 0
      preloadTotal.value = 0
    }
  }

  /**
   * 预加载特定域名的图标
   */
  const preloadPopularDomains = async () => {
    const popularDomains = [
      'github.com', 'google.com', 'stackoverflow.com',
      'youtube.com', 'twitter.com', 'linkedin.com',
      'medium.com', 'dev.to', 'npmjs.com'
    ]

    console.log('预加载热门域名图标')
    await iconPreloader.preloadDomainIcons(popularDomains)
  }

  /**
   * 获取预加载统计
   */
  const getPreloadStats = () => {
    const cacheStats = enhancedIconService.getCacheStats()
    const config = getConfig()
    
    return {
      isEnabled: config.enabled,
      isAutoPreloading: isAutoPreloading.value,
      cacheSize: cacheStats.size,
      lastPreloadTime: lastPreloadTime.value,
      config
    }
  }

  /**
   * 更新预加载配置
   */
  const updateConfig = (newConfig: Partial<SmartPreloadConfig>) => {
    const currentSettings = settingsStore.settings
    const updatedConfig = {
      ...getConfig(),
      ...newConfig
    }
    
    settingsStore.updateSettings({
      ...currentSettings,
      iconPreload: updatedConfig
    })
  }

  // 监听书签变化，触发预加载
  watch(
    () => bookmarksStore.enrichedBookmarks.length,
    (newLength, oldLength) => {
      // 当书签数量增加时，延迟执行预加载
      if (newLength > oldLength) {
        setTimeout(() => {
          executeSmartPreload()
        }, 2000) // 延迟2秒，让UI先稳定
      }
    }
  )

  // 应用启动时执行预加载
  onMounted(() => {
    // 延迟执行，确保应用完全加载
    setTimeout(() => {
      executeSmartPreload()
    }, 3000) // 延迟3秒
  })

  return {
    // 状态
    isAutoPreloading,
    preloadProgress,
    preloadTotal,
    lastPreloadTime,

    // 方法
    executeSmartPreload,
    preloadPopularDomains,
    getPreloadStats,
    updateConfig,
    getConfig,
    calculateBookmarkPriority,
    getHighPriorityBookmarks
  }
}
