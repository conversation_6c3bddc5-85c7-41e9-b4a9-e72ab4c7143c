import { ref, onMounted, onUnmounted } from 'vue'

export interface TabInfo {
  id: number
  title: string
  url: string
  favIconUrl?: string
  active: boolean
  windowId: number
}

export function useTabsList() {
  const tabs = ref<TabInfo[]>([])
  const isLoading = ref(false)
  const error = ref<string>('')

  // 获取所有标签页
  const loadTabs = async () => {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      // 开发环境模拟数据
      tabs.value = [
        {
          id: 1,
          title: 'GitHub',
          url: 'https://github.com',
          favIconUrl: 'https://github.com/favicon.ico',
          active: false,
          windowId: 1
        },
        {
          id: 2,
          title: 'Vue.js',
          url: 'https://vuejs.org',
          favIconUrl: 'https://vuejs.org/logo.svg',
          active: false,
          windowId: 1
        },
        {
          id: 3,
          title: 'MDN Web Docs',
          url: 'https://developer.mozilla.org',
          favIconUrl: 'https://developer.mozilla.org/favicon-48x48.cbbd161b.png',
          active: false,
          windowId: 1
        }
      ]
      return
    }

    isLoading.value = true
    error.value = ''

    try {
      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent()
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id })
      
      tabs.value = allTabs
        .filter(tab => tab.id !== undefined && tab.url && !tab.url.startsWith('chrome://'))
        .map(tab => ({
          id: tab.id!,
          title: tab.title || '无标题',
          url: tab.url!,
          favIconUrl: tab.favIconUrl,
          active: tab.active || false,
          windowId: tab.windowId || currentWindow.id!
        }))
        .filter(tab => tab.url !== window.location.href) // 排除当前页面
    } catch (err) {
      console.error('获取标签页失败:', err)
      error.value = '获取标签页失败'
    } finally {
      isLoading.value = false
    }
  }

  // 创建书签
  const createBookmarkFromTab = async (tab: TabInfo, parentId: string = '1') => {
    if (typeof chrome === 'undefined' || !chrome.bookmarks) {
      console.log('开发环境：模拟创建书签', { tab, parentId })
      return
    }

    try {
      const bookmark = await chrome.bookmarks.create({
        parentId,
        title: tab.title,
        url: tab.url
      })

      // 如果有图标，保存到元数据
      if (bookmark.id && tab.favIconUrl) {
        const metaKey = `icon_${bookmark.id}`
        chrome.storage.local.set({ [metaKey]: tab.favIconUrl })
      }

      console.log('书签创建成功:', bookmark)
      return bookmark
    } catch (err) {
      console.error('创建书签失败:', err)
      throw err
    }
  }

  // 切换到指定标签页
  const switchToTab = async (tabId: number) => {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      console.log('开发环境：模拟切换标签页', tabId)
      return
    }

    try {
      await chrome.tabs.update(tabId, { active: true })
      const tab = await chrome.tabs.get(tabId)
      if (tab.windowId) {
        await chrome.windows.update(tab.windowId, { focused: true })
      }
    } catch (err) {
      console.error('切换标签页失败:', err)
    }
  }

  // 关闭标签页
  const closeTab = async (tabId: number) => {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      console.log('开发环境：模拟关闭标签页', tabId)
      tabs.value = tabs.value.filter(tab => tab.id !== tabId)
      return
    }

    try {
      await chrome.tabs.remove(tabId)
      // 重新加载标签页列表
      await loadTabs()
    } catch (err) {
      console.error('关闭标签页失败:', err)
    }
  }

  // 监听标签页变化
  const setupTabListeners = () => {
    if (typeof chrome === 'undefined' || !chrome.tabs) {
      return
    }

    // 监听标签页创建
    const onCreated = () => loadTabs()
    // 监听标签页移除
    const onRemoved = () => loadTabs()
    // 监听标签页更新
    const onUpdated = () => loadTabs()
    // 监听标签页激活
    const onActivated = () => loadTabs()

    chrome.tabs.onCreated.addListener(onCreated)
    chrome.tabs.onRemoved.addListener(onRemoved)
    chrome.tabs.onUpdated.addListener(onUpdated)
    chrome.tabs.onActivated.addListener(onActivated)

    // 返回清理函数
    return () => {
      chrome.tabs.onCreated.removeListener(onCreated)
      chrome.tabs.onRemoved.removeListener(onRemoved)
      chrome.tabs.onUpdated.removeListener(onUpdated)
      chrome.tabs.onActivated.removeListener(onActivated)
    }
  }

  let cleanupListeners: (() => void) | undefined

  onMounted(() => {
    loadTabs()
    cleanupListeners = setupTabListeners()
  })

  onUnmounted(() => {
    if (cleanupListeners) {
      cleanupListeners()
    }
  })

  return {
    tabs,
    isLoading,
    error,
    loadTabs,
    createBookmarkFromTab,
    switchToTab,
    closeTab
  }
}
