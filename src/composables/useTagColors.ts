import { ref, computed } from 'vue'

// 精心设计的鲜明颜色调色板（突出且美观，避免红色系）
const PREDEFINED_COLORS = [
  '#4ECDC4', // 青绿色
  '#45B7D1', // 天空蓝
  '#FFA726', // 橙色
  '#AB47BC', // 紫色
  '#26A69A', // 蓝绿色
  '#66BB6A', // 绿色
  '#42A5F5', // 蓝色
  '#FFCA28', // 黄色
  '#8E24AA', // 深紫色
  '#00ACC1', // 青色
  '#7CB342', // 浅绿色
  '#5C6BC0', // 靛蓝色
  '#FFB74D', // 琥珀色
  '#9CCC65', // 浅绿色
  '#BA68C8', // 中紫色
  '#A1887F', // 棕色
  '#78909C', // 蓝灰色
  '#81C784', // 中绿色
  '#64B5F6', // 浅蓝色
  '#FFD54F'  // 浅黄色
]

// 标签颜色映射
const tagColorMap = ref<Record<string, string>>({})

export function useTagColors() {
  // 从localStorage加载标签颜色
  const loadTagColors = () => {
    try {
      const saved = localStorage.getItem('tagColors')
      if (saved) {
        tagColorMap.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载标签颜色失败:', error)
    }
  }

  // 保存标签颜色到localStorage
  const saveTagColors = () => {
    try {
      localStorage.setItem('tagColors', JSON.stringify(tagColorMap.value))
    } catch (error) {
      console.error('保存标签颜色失败:', error)
    }
  }

  // 为标签分配颜色
  const assignColorToTag = (tag: string): string => {
    if (tagColorMap.value[tag]) {
      return tagColorMap.value[tag]
    }

    // 获取已使用的颜色
    const usedColors = Object.values(tagColorMap.value)
    
    // 找到第一个未使用的预定义颜色
    let newColor = PREDEFINED_COLORS.find(color => !usedColors.includes(color))
    
    // 如果所有预定义颜色都用完了，生成随机颜色
    if (!newColor) {
      newColor = generateRandomColor()
    }

    tagColorMap.value[tag] = newColor
    saveTagColors()
    return newColor
  }

  // 检测当前主题模式
  const isDarkMode = (): boolean => {
    return document.documentElement.classList.contains('dark') ||
           document.documentElement.getAttribute('data-theme')?.includes('dark') ||
           false
  }

  // 生成随机柔和颜色（避免红色系）
  const generateRandomColor = (): string => {
    let hue: number

    // 避免红色系：0-30度（红色）和 330-360度（红紫色）
    // 选择安全的色相范围：60-300度
    const safeHueRanges = [
      { min: 60, max: 300 }  // 从黄色到紫色，避开红色系
    ]

    const range = safeHueRanges[Math.floor(Math.random() * safeHueRanges.length)]
    hue = range.min + Math.floor(Math.random() * (range.max - range.min))

    // 生成柔和的颜色：中等饱和度，适中亮度
    const saturation = 45 + Math.floor(Math.random() * 25) // 45-70%
    const lightness = 55 + Math.floor(Math.random() * 20)  // 55-75%
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`
  }

  // 根据主题调整颜色 - 优化版本
  const adjustColorForTheme = (color: string): string => {
    const isDark = isDarkMode()

    // 如果是HSL格式，直接返回
    if (color.startsWith('hsl')) {
      return color
    }

    // 转换十六进制颜色为HSL
    const hex = color.replace('#', '')
    const r = parseInt(hex.substring(0, 2), 16) / 255
    const g = parseInt(hex.substring(2, 4), 16) / 255
    const b = parseInt(hex.substring(4, 6), 16) / 255

    const max = Math.max(r, g, b)
    const min = Math.min(r, g, b)
    let h = 0, s = 0, l = (max + min) / 2

    if (max !== min) {
      const d = max - min
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min)

      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break
        case g: h = (b - r) / d + 2; break
        case b: h = (r - g) / d + 4; break
      }
      h /= 6
    }

    // 转换为度数
    h = Math.round(h * 360)
    s = Math.round(s * 100)
    l = Math.round(l * 100)

    // 根据主题调整亮度和饱和度 - 优化暗色模式对比度
    if (isDark) {
      // 暗色模式：使用更亮、更饱和的颜色，确保与暗色背景形成强烈对比
      s = Math.max(80, Math.min(100, s + 15))
      l = Math.max(70, Math.min(90, l + 25))
    } else {
      // 亮色模式：使用较深的颜色，确保与浅色背景形成良好对比
      s = Math.max(70, Math.min(95, s + 5))
      l = Math.max(30, Math.min(60, l - 5))
    }

    return `hsl(${h}, ${s}%, ${l}%)`
  }

  // 获取标签颜色（主题感知）
  const getTagColor = (tag: string): string => {
    const baseColor = tagColorMap.value[tag] || assignColorToTag(tag)
    return adjustColorForTheme(baseColor)
  }

  // 清理未使用的标签颜色
  const cleanupUnusedTagColors = (activeTags: string[]) => {
    const currentColors = { ...tagColorMap.value }
    let hasChanges = false

    // 删除不在活跃标签列表中的颜色
    Object.keys(currentColors).forEach(tag => {
      if (!activeTags.includes(tag)) {
        delete currentColors[tag]
        hasChanges = true
      }
    })

    if (hasChanges) {
      tagColorMap.value = currentColors
      saveTagColors()
      console.log('清理未使用的标签颜色:', Object.keys(currentColors))
    }
  }

  // 批量确保标签颜色存在
  const ensureTagColors = (tags: string[]) => {
    tags.forEach(tag => {
      if (!tagColorMap.value[tag]) {
        assignColorToTag(tag)
      }
    })
  }

  // 获取所有标签及其颜色
  const allTagColors = computed(() => tagColorMap.value)

  // 设置标签颜色
  const setTagColor = (tag: string, color: string) => {
    tagColorMap.value[tag] = color
    saveTagColors()
  }

  // 重置标签颜色为默认
  const resetTagColor = (tag: string) => {
    if (tagColorMap.value[tag]) {
      delete tagColorMap.value[tag]
      // 重新分配颜色
      assignColorToTag(tag)
    }
  }

  // 重置所有标签颜色
  const resetAllTagColors = () => {
    tagColorMap.value = {}
    saveTagColors()
  }

  // 获取预设颜色列表
  const getPredefinedColors = () => [...PREDEFINED_COLORS]

  // 初始化
  loadTagColors()

  return {
    getTagColor,
    assignColorToTag,
    setTagColor,
    resetTagColor,
    resetAllTagColors,
    allTagColors,
    getPredefinedColors,
    loadTagColors,
    saveTagColors,
    cleanupUnusedTagColors,
    ensureTagColors
  }
}
