import { ref, computed } from 'vue'

// 标签圆点显示状态
const showTagDots = ref(true)

export function useTagDots() {
  // 从localStorage加载设置
  const loadTagDotsSettings = () => {
    try {
      const saved = localStorage.getItem('showTagDots')
      if (saved !== null) {
        showTagDots.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载标签圆点设置失败:', error)
      showTagDots.value = true // 默认显示
    }
  }

  // 保存设置到localStorage
  const saveTagDotsSettings = () => {
    try {
      localStorage.setItem('showTagDots', JSON.stringify(showTagDots.value))
    } catch (error) {
      console.error('保存标签圆点设置失败:', error)
    }
  }

  // 切换标签圆点显示
  const toggleTagDots = () => {
    showTagDots.value = !showTagDots.value
    saveTagDotsSettings()
  }

  // 设置标签圆点显示状态
  const setTagDotsEnabled = (enabled: boolean) => {
    showTagDots.value = enabled
    saveTagDotsSettings()
  }

  // 初始化
  loadTagDotsSettings()

  return {
    showTagDots: computed(() => showTagDots.value),
    toggleTagDots,
    setTagDotsEnabled,
    loadTagDotsSettings,
    saveTagDotsSettings
  }
}
