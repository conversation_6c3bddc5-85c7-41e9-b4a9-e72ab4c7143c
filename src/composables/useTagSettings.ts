import { ref, computed } from 'vue'

// 标签功能开关状态
const isTagsEnabled = ref(true)

export function useTagSettings() {
  // 从localStorage加载标签功能设置
  const loadTagSettings = () => {
    try {
      const saved = localStorage.getItem('tagsEnabled')
      if (saved !== null) {
        isTagsEnabled.value = JSON.parse(saved)
      }
    } catch (error) {
      console.error('加载标签设置失败:', error)
      isTagsEnabled.value = true // 默认开启
    }
  }

  // 保存标签功能设置到localStorage
  const saveTagSettings = () => {
    try {
      localStorage.setItem('tagsEnabled', JSON.stringify(isTagsEnabled.value))
      console.log('标签功能设置已保存:', isTagsEnabled.value)
    } catch (error) {
      console.error('保存标签设置失败:', error)
    }
  }

  // 切换标签功能
  const toggleTagsEnabled = () => {
    isTagsEnabled.value = !isTagsEnabled.value
    saveTagSettings()
  }

  // 设置标签功能状态
  const setTagsEnabled = (enabled: boolean) => {
    isTagsEnabled.value = enabled
    saveTagSettings()
  }

  // 获取标签分隔符（仅在标签功能开启时有效）
  const tagSeparator = computed(() => {
    return isTagsEnabled.value ? (localStorage.getItem('tagSeparator') || '#') : ''
  })

  // 初始化
  loadTagSettings()

  return {
    isTagsEnabled: computed(() => isTagsEnabled.value),
    tagSeparator,
    toggleTagsEnabled,
    setTagsEnabled,
    loadTagSettings,
    saveTagSettings
  }
}
