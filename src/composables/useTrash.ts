import type { BookmarkWithMeta } from '../types'

/**
 * 标记书签为删除状态，添加 deletedAt 时间戳
 */
export function markAsDeleted(target: Partial<BookmarkWithMeta>) {
  target.deletedAt = Date.now()
}

/**
 * 还原软删除状态
 */
export function undoDeleted(target: Partial<BookmarkWithMeta>) {
  delete target.deletedAt
}

/**
 * 批量清除已过期删除的书签
 * @param list 书签数组
 * @param threshold 超过该毫秒数后视为永久删除（默认 5000 毫秒）
 */
export function purgeExpiredBookmarks(list: BookmarkWithMeta[], threshold = 5000): BookmarkWithMeta[] {
  const now = Date.now()
  return list.filter(b => !(b.deletedAt && now - b.deletedAt > threshold))
}
