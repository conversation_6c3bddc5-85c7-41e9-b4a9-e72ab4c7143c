import type { BookmarkWithMeta, BookmarkGroup } from '../types'

// 检测是否为开发环境
export const isDevelopment = import.meta.env.DEV

// 假数据 - 书签组/文件夹
export const mockGroups: BookmarkGroup[] = [
  { id: 'folder1', title: '开发工具', path: ['开发工具'] },
  { id: 'folder2', title: '设计资源', path: ['设计资源'] },
  { id: 'folder3', title: '学习资料', path: ['学习资料'] },
  { id: 'folder4', title: '娱乐', path: ['娱乐'] },
  { id: 'folder5', title: '购物', path: ['购物'] },
  { id: 'folder6', title: '空文件夹1', path: ['空文件夹1'] }, // 空文件夹用于测试移动功能
  { id: 'folder7', title: '空文件夹2', path: ['空文件夹2'] }, // 空文件夹用于测试移动功能
  { id: 'folder8', title: 'Vue.js', parentId: 'folder1', path: ['开发工具', 'Vue.js'] }, // 子文件夹
  { id: 'folder9', title: 'React', parentId: 'folder1', path: ['开发工具', 'React'] }, // 子文件夹
]

// 假数据 - 书签
export const mockBookmarks: BookmarkWithMeta[] = [
  // 开发工具文件夹
  {
    id: 'bm1',
    title: 'GitHub',
    url: 'https://github.com',
    parentId: 'folder1',
    icon: 'https://github.com/favicon.ico',
    tags: ['开发', '代码'],
  },
  {
    id: 'bm2',
    title: 'Stack Overflow',
    url: 'https://stackoverflow.com',
    parentId: 'folder1',
    icon: 'https://stackoverflow.com/favicon.ico',
    tags: ['开发', '问答'],
  },
  {
    id: 'bm3',
    title: 'MDN Web Docs',
    url: 'https://developer.mozilla.org',
    parentId: 'folder1',
    icon: 'https://developer.mozilla.org/favicon.ico',
    tags: ['开发', '文档'],
  },
  {
    id: 'bm4',
    title: 'Vue.js',
    url: 'https://vuejs.org',
    parentId: 'folder1',
    icon: 'https://vuejs.org/favicon.ico',
    tags: ['开发', 'Vue'],
  },
  {
    id: 'bm5',
    title: 'TypeScript',
    url: 'https://www.typescriptlang.org',
    parentId: 'folder1',
    icon: 'https://www.typescriptlang.org/favicon.ico',
    tags: ['开发', 'TypeScript'],
  },
  {
    id: 'bm6',
    title: 'Vite',
    url: 'https://vitejs.dev',
    parentId: 'folder1',
    icon: 'https://vitejs.dev/favicon.ico',
    tags: ['开发', '构建工具'],
  },
  {
    id: 'bm7',
    title: 'Tailwind CSS',
    url: 'https://tailwindcss.com',
    parentId: 'folder1',
    icon: 'https://tailwindcss.com/favicon.ico',
    tags: ['开发', 'CSS'],
  },
  {
    id: 'bm8',
    title: 'npm',
    url: 'https://www.npmjs.com',
    parentId: 'folder1',
    icon: 'https://www.npmjs.com/favicon.ico',
    tags: ['开发', '包管理'],
  },
  {
    id: 'bm9',
    title: 'CodePen',
    url: 'https://codepen.io',
    parentId: 'folder1',
    icon: 'https://codepen.io/favicon.ico',
    tags: ['开发', '在线编辑'],
  },
  {
    id: 'bm10',
    title: 'VS Code',
    url: 'https://code.visualstudio.com',
    parentId: 'folder1',
    icon: 'https://code.visualstudio.com/favicon.ico',
    tags: ['开发', '编辑器'],
  },
  {
    id: 'bm11',
    title: 'Chrome DevTools',
    url: 'https://developer.chrome.com/docs/devtools',
    parentId: 'folder1',
    icon: 'https://developer.chrome.com/favicon.ico',
    tags: ['开发', '调试'],
  },
  {
    id: 'bm12',
    title: 'Postman',
    url: 'https://www.postman.com',
    parentId: 'folder1',
    icon: 'https://www.postman.com/favicon.ico',
    tags: ['开发', 'API'],
  },

  // 设计资源文件夹
  {
    id: 'bm13',
    title: 'Figma',
    url: 'https://www.figma.com',
    parentId: 'folder2',
    icon: 'https://www.figma.com/favicon.ico',
    tags: ['设计', 'UI'],
  },
  {
    id: 'bm14',
    title: 'Dribbble',
    url: 'https://dribbble.com',
    parentId: 'folder2',
    icon: 'https://dribbble.com/favicon.ico',
    tags: ['设计', '灵感'],
  },
  {
    id: 'bm15',
    title: 'Behance',
    url: 'https://www.behance.net',
    parentId: 'folder2',
    icon: 'https://www.behance.net/favicon.ico',
    tags: ['设计', '作品集'],
  },
  {
    id: 'bm16',
    title: 'Unsplash',
    url: 'https://unsplash.com',
    parentId: 'folder2',
    icon: 'https://unsplash.com/favicon.ico',
    tags: ['设计', '图片'],
  },
  {
    id: 'bm17',
    title: 'Iconify',
    url: 'https://iconify.design',
    parentId: 'folder2',
    icon: 'https://iconify.design/favicon.ico',
    tags: ['设计', '图标'],
  },
  {
    id: 'bm18',
    title: 'Google Fonts',
    url: 'https://fonts.google.com',
    parentId: 'folder2',
    icon: 'https://fonts.google.com/favicon.ico',
    tags: ['设计', '字体'],
  },
  {
    id: 'bm19',
    title: 'Coolors',
    url: 'https://coolors.co',
    parentId: 'folder2',
    icon: 'https://coolors.co/favicon.ico',
    tags: ['设计', '配色'],
  },
  {
    id: 'bm20',
    title: 'Adobe Color',
    url: 'https://color.adobe.com',
    parentId: 'folder2',
    icon: 'https://color.adobe.com/favicon.ico',
    tags: ['设计', '配色'],
  },

  // 学习资料文件夹
  {
    id: 'bm21',
    title: 'MDN',
    url: 'https://developer.mozilla.org',
    parentId: 'folder3',
    icon: 'https://developer.mozilla.org/favicon.ico',
    tags: ['学习', 'Web'],
  },
  {
    id: 'bm22',
    title: 'freeCodeCamp',
    url: 'https://www.freecodecamp.org',
    parentId: 'folder3',
    icon: 'https://www.freecodecamp.org/favicon.ico',
    tags: ['学习', '编程'],
  },
  {
    id: 'bm23',
    title: 'Coursera',
    url: 'https://www.coursera.org',
    parentId: 'folder3',
    icon: 'https://www.coursera.org/favicon.ico',
    tags: ['学习', '课程'],
  },

  // 娱乐文件夹
  {
    id: 'bm24',
    title: 'YouTube',
    url: 'https://www.youtube.com',
    parentId: 'folder4',
    icon: 'https://www.youtube.com/favicon.ico',
    tags: ['娱乐', '视频'],
  },
  {
    id: 'bm25',
    title: 'Netflix',
    url: 'https://www.netflix.com',
    parentId: 'folder4',
    icon: 'https://www.netflix.com/favicon.ico',
    tags: ['娱乐', '电影'],
  },

  // 购物文件夹
  {
    id: 'bm26',
    title: '淘宝',
    url: 'https://www.taobao.com',
    parentId: 'folder5',
    icon: 'https://www.taobao.com/favicon.ico',
    tags: ['购物'],
  },
  {
    id: 'bm27',
    title: '京东',
    url: 'https://www.jd.com',
    parentId: 'folder5',
    icon: 'https://www.jd.com/favicon.ico',
    tags: ['购物'],
  },
  {
    id: 'bm27_1',
    title: '固态硬盘',
    url: 'https://www.jd.com/ssd',
    parentId: 'folder5',
    icon: 'https://www.jd.com/favicon.ico',
    tags: ['购物', '硬件'],
  },
  {
    id: 'bm27_2',
    title: '知乎',
    url: 'https://www.zhihu.com',
    parentId: 'folder4',
    icon: 'https://www.zhihu.com/favicon.ico',
    tags: ['娱乐', '问答'],
  },
  {
    id: 'bm27_3',
    title: '微信',
    url: 'https://weixin.qq.com',
    parentId: 'folder4',
    icon: 'https://weixin.qq.com/favicon.ico',
    tags: ['娱乐', '社交'],
  },

  // Vue.js 子文件夹 (folder8) - 添加更多书签用于测试展开/折叠
  {
    id: 'bm28',
    title: 'Vue Router',
    url: 'https://router.vuejs.org',
    parentId: 'folder8',
    icon: 'https://router.vuejs.org/favicon.ico',
    tags: ['Vue', '路由'],
  },
  {
    id: 'bm29',
    title: 'Vuex',
    url: 'https://vuex.vuejs.org',
    parentId: 'folder8',
    icon: 'https://vuex.vuejs.org/favicon.ico',
    tags: ['Vue', '状态管理'],
  },
  {
    id: 'bm30',
    title: 'Pinia',
    url: 'https://pinia.vuejs.org',
    parentId: 'folder8',
    icon: 'https://pinia.vuejs.org/favicon.ico',
    tags: ['Vue', '状态管理'],
  },
  {
    id: 'bm31',
    title: 'Nuxt.js',
    url: 'https://nuxtjs.org',
    parentId: 'folder8',
    icon: 'https://nuxtjs.org/favicon.ico',
    tags: ['Vue', '框架'],
  },
  {
    id: 'bm32',
    title: 'Quasar',
    url: 'https://quasar.dev',
    parentId: 'folder8',
    icon: 'https://quasar.dev/favicon.ico',
    tags: ['Vue', 'UI'],
  },
  {
    id: 'bm33',
    title: 'Element Plus',
    url: 'https://element-plus.org',
    parentId: 'folder8',
    icon: 'https://element-plus.org/favicon.ico',
    tags: ['Vue', 'UI'],
  },
  {
    id: 'bm34',
    title: 'Ant Design Vue',
    url: 'https://antdv.com',
    parentId: 'folder8',
    icon: 'https://antdv.com/favicon.ico',
    tags: ['Vue', 'UI'],
  },
  {
    id: 'bm35',
    title: 'Vuetify',
    url: 'https://vuetifyjs.com',
    parentId: 'folder8',
    icon: 'https://vuetifyjs.com/favicon.ico',
    tags: ['Vue', 'UI'],
  },
  {
    id: 'bm36',
    title: 'Vue DevTools',
    url: 'https://devtools.vuejs.org',
    parentId: 'folder8',
    icon: 'https://devtools.vuejs.org/favicon.ico',
    tags: ['Vue', '调试'],
  },
  {
    id: 'bm37',
    title: 'Vue Test Utils',
    url: 'https://vue-test-utils.vuejs.org',
    parentId: 'folder8',
    icon: 'https://vue-test-utils.vuejs.org/favicon.ico',
    tags: ['Vue', '测试'],
  },
  {
    id: 'bm38',
    title: 'Vue CLI',
    url: 'https://cli.vuejs.org',
    parentId: 'folder8',
    icon: 'https://cli.vuejs.org/favicon.ico',
    tags: ['Vue', '脚手架'],
  },
  {
    id: 'bm39',
    title: 'VueUse',
    url: 'https://vueuse.org',
    parentId: 'folder8',
    icon: 'https://vueuse.org/favicon.ico',
    tags: ['Vue', '工具库'],
  },
  {
    id: 'bm40',
    title: 'Vue Composition API',
    url: 'https://composition-api.vuejs.org',
    parentId: 'folder8',
    icon: 'https://composition-api.vuejs.org/favicon.ico',
    tags: ['Vue', 'API'],
  },
  {
    id: 'bm41',
    title: 'Vue 3 Migration Guide',
    url: 'https://v3-migration.vuejs.org',
    parentId: 'folder8',
    icon: 'https://v3-migration.vuejs.org/favicon.ico',
    tags: ['Vue', '迁移'],
  },

  // React 子文件夹 (folder9) - 添加一些书签
  {
    id: 'bm42',
    title: 'React Router',
    url: 'https://reactrouter.com',
    parentId: 'folder9',
    icon: 'https://reactrouter.com/favicon.ico',
    tags: ['React', '路由'],
  },
  {
    id: 'bm43',
    title: 'Redux',
    url: 'https://redux.js.org',
    parentId: 'folder9',
    icon: 'https://redux.js.org/favicon.ico',
    tags: ['React', '状态管理'],
  },
  {
    id: 'bm44',
    title: 'Next.js',
    url: 'https://nextjs.org',
    parentId: 'folder9',
    icon: 'https://nextjs.org/favicon.ico',
    tags: ['React', '框架'],
  },
]

// 生成更多书签以测试超过9个的情况
export const generateMoreBookmarks = (folderId: string, count: number): BookmarkWithMeta[] => {
  const moreBookmarks: BookmarkWithMeta[] = []
  for (let i = 1; i <= count; i++) {
    moreBookmarks.push({
      id: `${folderId}_extra_${i}`,
      title: `测试书签 ${i}`,
      url: `https://example${i}.com`,
      parentId: folderId,
      icon: `https://example${i}.com/favicon.ico`,
      tags: ['测试'],
    })
  }
  return moreBookmarks
}

// 为第一个文件夹添加更多书签以测试四宫格效果
export const allMockBookmarks = [
  ...mockBookmarks,
  ...generateMoreBookmarks('folder1', 5) // 总共17个书签，测试四宫格
]
