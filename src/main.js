import { createApp } from 'vue'
import './style.css'
import './styles/theme.css'
import App from './App.vue'
import { pinia } from './stores'
import { StorageService } from './services/storageService'

const app = createApp(App, { chrome: chrome })

// 安装 Pinia
app.use(pinia)

// 初始化存储服务
StorageService.initialize().then(() => {
  console.log('存储服务初始化完成')
}).catch(error => {
  console.error('存储服务初始化失败:', error)
})

app.mount('#app')
