/**
 * IndexedDB 数据库管理
 * 使用 Dexie.js 管理书签元数据、标签、图标缓存
 */

import Dexie, { Table } from 'dexie'

// 数据库表结构定义
export interface BookmarkMeta {
  bookmarkId: string
  customIconUrl?: string
  customIconData?: string // Base64 编码的图标数据
  tags: string[]
  notes?: string
  createdAt: number
  updatedAt: number
}

export interface TagDefinition {
  name: string
  color: string
  count: number
  createdAt: number
  updatedAt: number
}

export interface IconCache {
  domain: string
  iconUrl?: string
  iconData?: string // Base64 编码
  timestamp: number
  expiresAt: number
}

export interface SearchHistory {
  id?: number
  query: string
  timestamp: number
  resultCount: number
}

// 数据库类
export class BookmarkDatabase extends Dexie {
  // 表定义
  bookmarkMeta!: Table<BookmarkMeta>
  tags!: Table<TagDefinition>
  iconCache!: Table<IconCache>
  searchHistory!: Table<SearchHistory>

  constructor() {
    super('BookmarkManagerDB')
    
    this.version(1).stores({
      bookmarkMeta: 'bookmarkId, tags, createdAt, updatedAt',
      tags: 'name, count, createdAt, updatedAt',
      iconCache: 'domain, timestamp, expiresAt',
      searchHistory: '++id, query, timestamp'
    })
  }
}

// 创建数据库实例
export const db = new BookmarkDatabase()

// 数据库操作类
export class DatabaseService {
  // 书签元数据操作
  static async getBookmarkMeta(bookmarkId: string): Promise<BookmarkMeta | undefined> {
    return await db.bookmarkMeta.get(bookmarkId)
  }

  static async setBookmarkMeta(meta: BookmarkMeta): Promise<void> {
    meta.updatedAt = Date.now()
    await db.bookmarkMeta.put(meta)
  }

  static async deleteBookmarkMeta(bookmarkId: string): Promise<void> {
    await db.bookmarkMeta.delete(bookmarkId)
  }

  static async getAllBookmarkMeta(): Promise<BookmarkMeta[]> {
    return await db.bookmarkMeta.toArray()
  }

  // 批量更新书签元数据
  static async bulkUpdateBookmarkMeta(metas: BookmarkMeta[]): Promise<void> {
    const now = Date.now()
    const updatedMetas = metas.map(meta => ({
      ...meta,
      updatedAt: now
    }))
    await db.bookmarkMeta.bulkPut(updatedMetas)
  }

  // 标签操作
  static async getTag(name: string): Promise<TagDefinition | undefined> {
    return await db.tags.get(name)
  }

  static async setTag(tag: TagDefinition): Promise<void> {
    tag.updatedAt = Date.now()
    await db.tags.put(tag)
  }

  static async deleteTag(name: string): Promise<void> {
    await db.tags.delete(name)
  }

  static async getAllTags(): Promise<TagDefinition[]> {
    return await db.tags.orderBy('count').reverse().toArray()
  }

  static async updateTagCount(name: string, count: number): Promise<void> {
    const tag = await db.tags.get(name)
    if (tag) {
      tag.count = count
      tag.updatedAt = Date.now()
      await db.tags.put(tag)
    }
  }

  // 批量更新标签统计
  static async bulkUpdateTagCounts(tagCounts: Record<string, number>): Promise<void> {
    const now = Date.now()
    const updates: TagDefinition[] = []

    for (const [name, count] of Object.entries(tagCounts)) {
      const existingTag = await db.tags.get(name)
      if (existingTag) {
        updates.push({
          ...existingTag,
          count,
          updatedAt: now
        })
      }
    }

    if (updates.length > 0) {
      await db.tags.bulkPut(updates)
    }
  }

  // 图标缓存操作
  static async getIconCache(domain: string): Promise<IconCache | undefined> {
    const cache = await db.iconCache.get(domain)
    
    // 检查是否过期
    if (cache && cache.expiresAt < Date.now()) {
      await db.iconCache.delete(domain)
      return undefined
    }
    
    return cache
  }

  static async setIconCache(cache: IconCache): Promise<void> {
    await db.iconCache.put(cache)
  }

  static async deleteIconCache(domain: string): Promise<void> {
    await db.iconCache.delete(domain)
  }

  // 清理过期的图标缓存
  static async cleanExpiredIconCache(): Promise<void> {
    const now = Date.now()
    await db.iconCache.where('expiresAt').below(now).delete()
  }

  // 搜索历史操作
  static async addSearchHistory(query: string, resultCount: number): Promise<void> {
    await db.searchHistory.add({
      query,
      timestamp: Date.now(),
      resultCount
    })

    // 限制历史记录数量
    const count = await db.searchHistory.count()
    if (count > 100) {
      const oldestRecords = await db.searchHistory
        .orderBy('timestamp')
        .limit(count - 100)
        .toArray()
      
      const idsToDelete = oldestRecords.map(record => record.id!).filter(id => id !== undefined)
      await db.searchHistory.bulkDelete(idsToDelete)
    }
  }

  static async getSearchHistory(limit: number = 20): Promise<SearchHistory[]> {
    return await db.searchHistory
      .orderBy('timestamp')
      .reverse()
      .limit(limit)
      .toArray()
  }

  static async clearSearchHistory(): Promise<void> {
    await db.searchHistory.clear()
  }

  // 数据库维护操作
  static async exportData(): Promise<{
    bookmarkMeta: BookmarkMeta[]
    tags: TagDefinition[]
    iconCache: IconCache[]
    searchHistory: SearchHistory[]
  }> {
    const [bookmarkMeta, tags, iconCache, searchHistory] = await Promise.all([
      db.bookmarkMeta.toArray(),
      db.tags.toArray(),
      db.iconCache.toArray(),
      db.searchHistory.toArray()
    ])

    return {
      bookmarkMeta,
      tags,
      iconCache,
      searchHistory
    }
  }

  static async importData(data: {
    bookmarkMeta?: BookmarkMeta[]
    tags?: TagDefinition[]
    iconCache?: IconCache[]
    searchHistory?: SearchHistory[]
  }): Promise<void> {
    await db.transaction('rw', [db.bookmarkMeta, db.tags, db.iconCache, db.searchHistory], async () => {
      if (data.bookmarkMeta) {
        await db.bookmarkMeta.bulkPut(data.bookmarkMeta)
      }
      if (data.tags) {
        await db.tags.bulkPut(data.tags)
      }
      if (data.iconCache) {
        await db.iconCache.bulkPut(data.iconCache)
      }
      if (data.searchHistory) {
        await db.searchHistory.bulkPut(data.searchHistory)
      }
    })
  }

  static async clearAllData(): Promise<void> {
    await db.transaction('rw', [db.bookmarkMeta, db.tags, db.iconCache, db.searchHistory], async () => {
      await Promise.all([
        db.bookmarkMeta.clear(),
        db.tags.clear(),
        db.iconCache.clear(),
        db.searchHistory.clear()
      ])
    })
  }

  // 获取数据库统计信息
  static async getStats(): Promise<{
    bookmarkMetaCount: number
    tagsCount: number
    iconCacheCount: number
    searchHistoryCount: number
    dbSize: number
  }> {
    const [bookmarkMetaCount, tagsCount, iconCacheCount, searchHistoryCount] = await Promise.all([
      db.bookmarkMeta.count(),
      db.tags.count(),
      db.iconCache.count(),
      db.searchHistory.count()
    ])

    // 估算数据库大小（简单估算）
    const dbSize = (bookmarkMetaCount + tagsCount + iconCacheCount + searchHistoryCount) * 1024 // 粗略估算

    return {
      bookmarkMetaCount,
      tagsCount,
      iconCacheCount,
      searchHistoryCount,
      dbSize
    }
  }
}
