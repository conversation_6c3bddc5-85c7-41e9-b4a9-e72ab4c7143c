/**
 * 增强版图标获取服务
 * 基于 /icon-system 目录的代码优化，适用于书签管理器项目
 */

export interface IconResult {
  success: boolean
  data?: string // base64 或 URL
  type?: 'base64' | 'url'
  source?: string
  error?: string
}

export interface IconSource {
  name: string
  description: string
  getUrl: (domain: string) => string
  timeout: number
  headers?: Record<string, string>
  features: string[]
  isHtmlParsing?: boolean
}

/**
 * 网站图标API源列表 - 优化版
 * 基于用户建议，排除Direct Favicon，优化获取策略
 */
const SITE_ICON_SOURCES: IconSource[] = [
  {
    name: 'Logo.dev API',
    description: '现代化的Logo API服务，高质量图标',
    getUrl: (domain: string) => `https://img.logo.dev/${domain}?token=pk_GhxeJ8RST3qlWB6d6MGLFw&format=png&retina=true&fallback=404`,
    timeout: 6000,
    headers: {
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Referer': 'https://www.logo.dev/'
    },
    features: ['modern', 'high-quality', 'reliable']
  },
  {
    name: 'Clearbit Logo API',
    description: '高质量商业图标，彩色支持，响应快速',
    getUrl: (domain: string) => `https://logo.clearbit.com/${domain}`,
    timeout: 6000,
    headers: {
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Referer': 'https://clearbit.com/'
    },
    features: ['color', 'high-quality', 'commercial']
  },
  {
    name: 'Unavatar API',
    description: '通用头像和图标服务，支持多种源',
    getUrl: (domain: string) => `https://unavatar.io/${domain}`,
    timeout: 4000,
    headers: {
      'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8',
      'Referer': 'https://unavatar.io/'
    },
    features: ['universal', 'reliable', 'fast']
  }
]

/**
 * 增强版图标获取服务类
 */
export class EnhancedIconService {
  private cache = new Map<string, IconResult>()
  private readonly CACHE_EXPIRY = 7 * 24 * 60 * 60 * 1000 // 7天

  /**
   * 获取网站图标 - 优化版获取策略
   * @param url 网站URL
   * @param useCache 是否使用缓存
   * @returns 图标结果
   */
  async getWebsiteIcon(url: string, useCache = true): Promise<IconResult> {
    const domain = this.extractDomain(url)
    const cacheKey = `website:${domain}`

    // 检查缓存
    if (useCache && this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      return cached
    }

    // 第一步：尝试内置图标API源
    for (const source of SITE_ICON_SOURCES) {
      try {
        const result = await this.fetchIconFromSource(source, domain)
        if (result.success) {
          this.cache.set(cacheKey, result)
          return result
        }
      } catch (error) {
        console.warn(`从 ${source.name} 获取图标失败:`, error)
      }
    }

    // 第二步：从HTML中提取favicon URL
    try {
      const htmlResult = await this.extractFaviconFromHtml(url, domain)
      if (htmlResult.success) {
        this.cache.set(cacheKey, htmlResult)
        return htmlResult
      }
    } catch (error) {
      console.warn('从HTML提取favicon失败:', error)
    }

    // 第三步：使用Chrome内置favicon API
    try {
      const chromeResult = await this.getChromeIconUrl(url)
      if (chromeResult.success) {
        this.cache.set(cacheKey, chromeResult)
        return chromeResult
      }
    } catch (error) {
      console.warn('从Chrome获取favicon失败:', error)
    }

    // 第四步：生成默认字母图标
    const fallbackResult: IconResult = {
      success: true, // 改为true，因为我们总是能生成默认图标
      data: this.generateDefaultIcon(domain),
      type: 'base64',
      source: 'fallback'
    }

    this.cache.set(cacheKey, fallbackResult)
    return fallbackResult
  }

  /**
   * 从HTML中提取favicon URL并获取图标
   * 基于 icon-system/2download.ts 的 parseHtmlForFavicon 方法
   */
  private async extractFaviconFromHtml(url: string, domain: string): Promise<IconResult> {
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 8000)

      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        return { success: false, error: `HTTP ${response.status}` }
      }

      const html = await response.text()
      const faviconUrls = this.extractFaviconUrls(html, domain)

      // 尝试获取每个favicon URL
      for (const faviconUrl of faviconUrls) {
        try {
          const iconResult = await this.fetchIconFromUrl(faviconUrl)
          if (iconResult.success) {
            return {
              ...iconResult,
              source: 'html-favicon'
            }
          }
        } catch (error) {
          continue
        }
      }

      return { success: false, error: '未找到有效的favicon' }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 从HTML中提取favicon URL列表
   * 基于 icon-system 的 extractFaviconUrls 方法
   */
  private extractFaviconUrls(html: string, domain: string): string[] {
    const urls: string[] = []

    // 匹配各种favicon链接的正则表达式
    const patterns = [
      /<link[^>]*rel=["'](?:shortcut )?icon["'][^>]*href=["']([^"']+)["']/gi,
      /<link[^>]*href=["']([^"']+)["'][^>]*rel=["'](?:shortcut )?icon["']/gi,
      /<link[^>]*rel=["']apple-touch-icon["'][^>]*href=["']([^"']+)["']/gi,
      /<meta[^>]*property=["']og:image["'][^>]*content=["']([^"']+)["']/gi
    ]

    for (const pattern of patterns) {
      let match
      while ((match = pattern.exec(html)) !== null) {
        let url = match[1]

        // 处理相对URL
        if (url.startsWith('//')) {
          url = `https:${url}`
        } else if (url.startsWith('/')) {
          url = `https://${domain}${url}`
        } else if (!url.startsWith('http')) {
          url = `https://${domain}/${url}`
        }

        urls.push(url)
      }
    }

    // 添加默认favicon.ico路径
    urls.push(`https://${domain}/favicon.ico`)

    // 去重并返回
    return Array.from(new Set(urls))
  }

  /**
   * 使用Chrome内置favicon API获取图标
   */
  private async getChromeIconUrl(bookmarkUrl: string): Promise<IconResult> {
    try {
      // 检查是否在Chrome扩展环境中
      if (typeof chrome === 'undefined' || !chrome.runtime) {
        return { success: false, error: '不在Chrome扩展环境中' }
      }

      const url = new URL(chrome.runtime.getURL("/_favicon/"))
      url.searchParams.set("pageUrl", bookmarkUrl)
      url.searchParams.set("size", "32")

      const faviconUrl = url.toString()

      const result = await this.fetchIconFromUrl(faviconUrl)
      if (result.success) {
        return {
          ...result,
          source: 'chrome-favicon'
        }
      }

      return { success: false, error: 'Chrome favicon API失败' }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 从URL获取图标数据
   */
  private async fetchIconFromUrl(iconUrl: string): Promise<IconResult> {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), 5000)

    try {
      const response = await fetch(iconUrl, {
        headers: {
          'Accept': 'image/*,*/*;q=0.8'
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const blob = await response.blob()
        const base64 = await this.blobToBase64(blob)

        return {
          success: true,
          data: base64,
          type: 'base64'
        }
      }

      return { success: false, error: `HTTP ${response.status}` }
    } catch (error) {
      clearTimeout(timeoutId)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 从指定源获取图标
   */
  private async fetchIconFromSource(source: IconSource, domain: string): Promise<IconResult> {
    const url = source.getUrl(domain)
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), source.timeout)

    try {
      const response = await fetch(url, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
          'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
          'Cache-Control': 'no-cache',
          ...source.headers
        },
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (response.ok) {
        const blob = await response.blob()
        const base64 = await this.blobToBase64(blob)

        return {
          success: true,
          data: base64,
          type: 'base64',
          source: source.name
        }
      }

      return {
        success: false,
        error: `HTTP ${response.status}`
      }
    } catch (error) {
      clearTimeout(timeoutId)
      return {
        success: false,
        error: error instanceof Error ? error.message : '未知错误'
      }
    }
  }

  /**
   * 从URL提取域名
   */
  private extractDomain(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname
    } catch {
      // 如果URL格式不正确，尝试简单匹配
      const match = url.match(/https?:\/\/([^\/]+)/)
      return match ? match[1] : url
    }
  }

  /**
   * Blob转Base64
   */
  private async blobToBase64(blob: Blob): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.onload = () => resolve(reader.result as string)
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  }

  /**
   * 生成默认图标（字母图标）
   */
  private generateDefaultIcon(domain: string): string {
    const letter = domain.charAt(0).toUpperCase()
    const hash = domain.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0)
    
    // 基于域名生成颜色
    const hue = hash % 360
    const saturation = 60 + (hash % 40) // 60-100%
    const lightness = 45 + (hash % 20)  // 45-65%
    
    const svg = `
      <svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 64 64">
        <rect width="64" height="64" rx="8" fill="hsl(${hue}, ${saturation}%, ${lightness}%)"/>
        <text x="32" y="40" font-family="Arial, sans-serif" font-size="28" font-weight="bold" 
              text-anchor="middle" fill="white">${letter}</text>
      </svg>
    `.trim()

    return `data:image/svg+xml;base64,${btoa(svg)}`
  }

  /**
   * 清理过期缓存
   */
  clearExpiredCache(): void {
    // 在实际项目中，这里应该检查缓存时间戳
    // 简化实现：清理所有缓存
    this.cache.clear()
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }

  /**
   * 预加载图标
   * @param urls URL列表
   */
  async preloadIcons(urls: string[]): Promise<void> {
    const promises = urls.map(url => this.getWebsiteIcon(url, true))
    await Promise.allSettled(promises)
  }
}

// 导出单例实例
export const enhancedIconService = new EnhancedIconService()
