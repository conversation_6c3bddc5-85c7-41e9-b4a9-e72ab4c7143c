/**
 * 图标服务
 * 多级 fallback 策略获取高清图标，支持缓存
 */

import { DatabaseService } from './database'

export interface IconResult {
  url?: string
  data?: string // Base64 编码
  source: 'cache' | 'favicon' | 'google' | 'duckduckgo' | 'fallback'
  timestamp: number
}

export class IconService {
  private static readonly CACHE_DURATION = 7 * 24 * 60 * 60 * 1000 // 7天
  private static readonly TIMEOUT = 5000 // 5秒超时

  /**
   * 获取网站图标（多级 fallback 策略）
   */
  static async getIcon(url: string): Promise<IconResult> {
    const domain = this.extractDomain(url)
    
    // 1. 首先检查缓存
    const cached = await this.getCachedIcon(domain)
    if (cached) {
      return cached
    }

    // 2. 尝试多种图标源
    const strategies = [
      () => this.getFaviconFromSite(domain),
      () => this.getGoogleFavicon(domain),
      () => this.getDuckDuckGoFavicon(domain),
      () => this.getFallbackIcon(domain)
    ]

    for (const strategy of strategies) {
      try {
        const result = await strategy()
        if (result.url || result.data) {
          // 缓存成功获取的图标
          await this.cacheIcon(domain, result)
          return result
        }
      } catch (error) {
        console.warn(`图标获取策略失败:`, error)
        continue
      }
    }

    // 如果所有策略都失败，返回默认图标
    return this.getFallbackIcon(domain)
  }

  /**
   * 从缓存获取图标
   */
  private static async getCachedIcon(domain: string): Promise<IconResult | null> {
    try {
      const cache = await DatabaseService.getIconCache(domain)
      if (cache) {
        return {
          url: cache.iconUrl,
          data: cache.iconData,
          source: 'cache',
          timestamp: cache.timestamp
        }
      }
    } catch (error) {
      console.warn('读取图标缓存失败:', error)
    }
    return null
  }

  /**
   * 缓存图标
   */
  private static async cacheIcon(domain: string, result: IconResult): Promise<void> {
    try {
      const now = Date.now()
      await DatabaseService.setIconCache({
        domain,
        iconUrl: result.url,
        iconData: result.data,
        timestamp: now,
        expiresAt: now + this.CACHE_DURATION
      })
    } catch (error) {
      console.warn('缓存图标失败:', error)
    }
  }

  /**
   * 策略1: 从网站直接获取 favicon
   */
  private static async getFaviconFromSite(domain: string): Promise<IconResult> {
    const faviconUrls = [
      `https://${domain}/favicon.ico`,
      `https://${domain}/favicon.png`,
      `https://${domain}/apple-touch-icon.png`,
      `https://${domain}/apple-touch-icon-precomposed.png`
    ]

    for (const faviconUrl of faviconUrls) {
      try {
        const isValid = await this.validateImageUrl(faviconUrl)
        if (isValid) {
          return {
            url: faviconUrl,
            source: 'favicon',
            timestamp: Date.now()
          }
        }
      } catch (error) {
        continue
      }
    }

    throw new Error('无法从网站获取 favicon')
  }

  /**
   * 策略2: 使用 Google Favicon API
   */
  private static async getGoogleFavicon(domain: string): Promise<IconResult> {
    const googleUrl = `https://www.google.com/s2/favicons?domain=${domain}&sz=64`
    
    try {
      const isValid = await this.validateImageUrl(googleUrl)
      if (isValid) {
        return {
          url: googleUrl,
          source: 'google',
          timestamp: Date.now()
        }
      }
    } catch (error) {
      throw new Error('Google Favicon API 失败')
    }

    throw new Error('Google Favicon API 返回无效图标')
  }

  /**
   * 策略3: 使用 DuckDuckGo Favicon API
   */
  private static async getDuckDuckGoFavicon(domain: string): Promise<IconResult> {
    const duckduckgoUrl = `https://icons.duckduckgo.com/ip3/${domain}.ico`
    
    try {
      const isValid = await this.validateImageUrl(duckduckgoUrl)
      if (isValid) {
        return {
          url: duckduckgoUrl,
          source: 'duckduckgo',
          timestamp: Date.now()
        }
      }
    } catch (error) {
      throw new Error('DuckDuckGo Favicon API 失败')
    }

    throw new Error('DuckDuckGo Favicon API 返回无效图标')
  }

  /**
   * 策略4: 生成默认图标
   */
  private static getFallbackIcon(domain: string): IconResult {
    // 生成基于域名的默认图标（使用 Canvas 或 SVG）
    const canvas = document.createElement('canvas')
    canvas.width = 64
    canvas.height = 64
    const ctx = canvas.getContext('2d')!

    // 生成基于域名的颜色
    const color = this.generateColorFromDomain(domain)
    
    // 绘制圆形背景
    ctx.fillStyle = color
    ctx.beginPath()
    ctx.arc(32, 32, 32, 0, 2 * Math.PI)
    ctx.fill()

    // 绘制域名首字母
    const firstLetter = domain.charAt(0).toUpperCase()
    ctx.fillStyle = '#ffffff'
    ctx.font = 'bold 28px Arial'
    ctx.textAlign = 'center'
    ctx.textBaseline = 'middle'
    ctx.fillText(firstLetter, 32, 32)

    // 转换为 Base64
    const dataUrl = canvas.toDataURL('image/png')
    
    return {
      data: dataUrl,
      source: 'fallback',
      timestamp: Date.now()
    }
  }

  /**
   * 验证图片 URL 是否有效
   */
  private static validateImageUrl(url: string): Promise<boolean> {
    return new Promise((resolve) => {
      const img = new Image()
      const timeout = setTimeout(() => {
        resolve(false)
      }, this.TIMEOUT)

      img.onload = () => {
        clearTimeout(timeout)
        // 检查图片尺寸，过小的图片可能是占位符
        resolve(img.width >= 16 && img.height >= 16)
      }

      img.onerror = () => {
        clearTimeout(timeout)
        resolve(false)
      }

      img.src = url
    })
  }

  /**
   * 从域名生成确定性颜色
   */
  private static generateColorFromDomain(domain: string): string {
    let hash = 0
    for (let i = 0; i < domain.length; i++) {
      hash = domain.charCodeAt(i) + ((hash << 5) - hash)
    }

    // 生成 HSL 颜色，固定饱和度和亮度
    const hue = Math.abs(hash) % 360
    return `hsl(${hue}, 70%, 50%)`
  }

  /**
   * 从 URL 提取域名
   */
  private static extractDomain(url: string): string {
    try {
      const urlObj = new URL(url)
      return urlObj.hostname.replace(/^www\./, '')
    } catch (error) {
      // 如果 URL 解析失败，尝试简单的字符串处理
      const match = url.match(/^(?:https?:\/\/)?(?:www\.)?([^\/]+)/)
      return match ? match[1] : 'unknown'
    }
  }

  /**
   * 批量预加载图标
   */
  static async preloadIcons(urls: string[]): Promise<void> {
    const domains = urls.map(url => this.extractDomain(url))
    const uniqueDomains = [...new Set(domains)]

    // 并发加载，但限制并发数
    const concurrency = 5
    for (let i = 0; i < uniqueDomains.length; i += concurrency) {
      const batch = uniqueDomains.slice(i, i + concurrency)
      await Promise.allSettled(
        batch.map(domain => this.getIcon(`https://${domain}`))
      )
    }
  }

  /**
   * 清理过期缓存
   */
  static async cleanupCache(): Promise<void> {
    try {
      await DatabaseService.cleanExpiredIconCache()
    } catch (error) {
      console.warn('清理图标缓存失败:', error)
    }
  }

  /**
   * 获取缓存统计信息
   */
  static async getCacheStats(): Promise<{
    totalCached: number
    cacheSize: number
  }> {
    try {
      const stats = await DatabaseService.getStats()
      return {
        totalCached: stats.iconCacheCount,
        cacheSize: stats.dbSize
      }
    } catch (error) {
      console.warn('获取缓存统计失败:', error)
      return { totalCached: 0, cacheSize: 0 }
    }
  }
}
