/**
 * 混合存储策略服务
 * Chrome Storage Sync + IndexedDB
 * 
 * 策略：
 * - Chrome Storage Sync: 存储设置、轻量级数据（跨设备同步）
 * - IndexedDB: 存储书签元数据、标签、图标缓存（本地高性能）
 */

import { DatabaseService } from './database'
import type { AppSettings } from '../stores/settings'
import type { BookmarkMeta, TagDefinition } from './database'

export interface SyncData {
  settings: AppSettings
  lastSyncTime: number
  version: string
}

export class StorageService {
  private static readonly SYNC_KEY = 'bookmarkManagerSync'
  private static readonly VERSION = '1.0.0'

  /**
   * 初始化存储服务
   */
  static async initialize(): Promise<void> {
    try {
      // 检查并迁移旧数据
      await this.migrateOldData()
      
      // 清理过期缓存
      await DatabaseService.cleanExpiredIconCache()
      
      console.log('存储服务初始化完成')
    } catch (error) {
      console.error('存储服务初始化失败:', error)
    }
  }

  /**
   * 保存设置到 Chrome Storage Sync
   */
  static async saveSettings(settings: AppSettings): Promise<void> {
    try {
      const syncData: SyncData = {
        settings,
        lastSyncTime: Date.now(),
        version: this.VERSION
      }

      if (typeof chrome !== 'undefined' && chrome.storage?.sync) {
        await chrome.storage.sync.set({ [this.SYNC_KEY]: syncData })
      } else {
        // 开发环境回退到 localStorage
        localStorage.setItem(this.SYNC_KEY, JSON.stringify(syncData))
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      throw error
    }
  }

  /**
   * 从 Chrome Storage Sync 加载设置
   */
  static async loadSettings(): Promise<AppSettings | null> {
    try {
      let syncData: SyncData | null = null

      if (typeof chrome !== 'undefined' && chrome.storage?.sync) {
        const result = await chrome.storage.sync.get(this.SYNC_KEY)
        syncData = result[this.SYNC_KEY] || null
      } else {
        // 开发环境从 localStorage 加载
        const stored = localStorage.getItem(this.SYNC_KEY)
        syncData = stored ? JSON.parse(stored) : null
      }

      return syncData?.settings || null
    } catch (error) {
      console.error('加载设置失败:', error)
      return null
    }
  }

  /**
   * 保存书签元数据到 IndexedDB
   */
  static async saveBookmarkMeta(bookmarkId: string, meta: Partial<BookmarkMeta>): Promise<void> {
    try {
      const existing = await DatabaseService.getBookmarkMeta(bookmarkId)
      const now = Date.now()

      const bookmarkMeta: BookmarkMeta = {
        bookmarkId,
        customIconUrl: meta.customIconUrl,
        customIconData: meta.customIconData,
        tags: meta.tags || [],
        notes: meta.notes,
        createdAt: existing?.createdAt || now,
        updatedAt: now
      }

      await DatabaseService.setBookmarkMeta(bookmarkMeta)
    } catch (error) {
      console.error('保存书签元数据失败:', error)
      throw error
    }
  }

  /**
   * 加载书签元数据从 IndexedDB
   */
  static async loadBookmarkMeta(bookmarkId: string): Promise<BookmarkMeta | null> {
    try {
      return await DatabaseService.getBookmarkMeta(bookmarkId) || null
    } catch (error) {
      console.error('加载书签元数据失败:', error)
      return null
    }
  }

  /**
   * 批量加载所有书签元数据
   */
  static async loadAllBookmarkMeta(): Promise<Record<string, BookmarkMeta>> {
    try {
      const allMeta = await DatabaseService.getAllBookmarkMeta()
      const result: Record<string, BookmarkMeta> = {}
      
      allMeta.forEach(meta => {
        result[meta.bookmarkId] = meta
      })
      
      return result
    } catch (error) {
      console.error('批量加载书签元数据失败:', error)
      return {}
    }
  }

  /**
   * 删除书签元数据
   */
  static async deleteBookmarkMeta(bookmarkId: string): Promise<void> {
    try {
      await DatabaseService.deleteBookmarkMeta(bookmarkId)
    } catch (error) {
      console.error('删除书签元数据失败:', error)
      throw error
    }
  }

  /**
   * 保存标签定义到 IndexedDB
   */
  static async saveTag(tag: TagDefinition): Promise<void> {
    try {
      await DatabaseService.setTag(tag)
    } catch (error) {
      console.error('保存标签失败:', error)
      throw error
    }
  }

  /**
   * 加载所有标签
   */
  static async loadAllTags(): Promise<TagDefinition[]> {
    try {
      return await DatabaseService.getAllTags()
    } catch (error) {
      console.error('加载标签失败:', error)
      return []
    }
  }

  /**
   * 批量更新标签统计
   */
  static async updateTagStatistics(tagCounts: Record<string, number>): Promise<void> {
    try {
      await DatabaseService.bulkUpdateTagCounts(tagCounts)
    } catch (error) {
      console.error('更新标签统计失败:', error)
      throw error
    }
  }

  /**
   * 数据导出
   */
  static async exportAllData(): Promise<{
    sync: SyncData | null
    database: any
  }> {
    try {
      const [syncData, databaseData] = await Promise.all([
        this.loadSettings().then(settings => settings ? {
          settings,
          lastSyncTime: Date.now(),
          version: this.VERSION
        } : null),
        DatabaseService.exportData()
      ])

      return {
        sync: syncData,
        database: databaseData
      }
    } catch (error) {
      console.error('导出数据失败:', error)
      throw error
    }
  }

  /**
   * 数据导入
   */
  static async importAllData(data: {
    sync?: SyncData
    database?: any
  }): Promise<void> {
    try {
      // 导入设置
      if (data.sync?.settings) {
        await this.saveSettings(data.sync.settings)
      }

      // 导入数据库数据
      if (data.database) {
        await DatabaseService.importData(data.database)
      }
    } catch (error) {
      console.error('导入数据失败:', error)
      throw error
    }
  }

  /**
   * 清除所有数据
   */
  static async clearAllData(): Promise<void> {
    try {
      // 清除同步数据
      if (typeof chrome !== 'undefined' && chrome.storage?.sync) {
        await chrome.storage.sync.remove(this.SYNC_KEY)
      } else {
        localStorage.removeItem(this.SYNC_KEY)
      }

      // 清除数据库数据
      await DatabaseService.clearAllData()
    } catch (error) {
      console.error('清除数据失败:', error)
      throw error
    }
  }

  /**
   * 获取存储统计信息
   */
  static async getStorageStats(): Promise<{
    syncSize: number
    databaseStats: any
  }> {
    try {
      // 计算同步数据大小
      let syncSize = 0
      if (typeof chrome !== 'undefined' && chrome.storage?.sync) {
        const result = await chrome.storage.sync.get(this.SYNC_KEY)
        syncSize = JSON.stringify(result).length
      } else {
        const stored = localStorage.getItem(this.SYNC_KEY)
        syncSize = stored ? stored.length : 0
      }

      const databaseStats = await DatabaseService.getStats()

      return {
        syncSize,
        databaseStats
      }
    } catch (error) {
      console.error('获取存储统计失败:', error)
      return {
        syncSize: 0,
        databaseStats: {
          bookmarkMetaCount: 0,
          tagsCount: 0,
          iconCacheCount: 0,
          searchHistoryCount: 0,
          dbSize: 0
        }
      }
    }
  }

  /**
   * 迁移旧数据（如果存在）
   */
  private static async migrateOldData(): Promise<void> {
    try {
      // 检查是否有旧的 localStorage 数据需要迁移
      const oldSettings = localStorage.getItem('appSettings')
      if (oldSettings && typeof chrome !== 'undefined' && chrome.storage?.sync) {
        const settings = JSON.parse(oldSettings)
        await this.saveSettings(settings)
        localStorage.removeItem('appSettings')
        console.log('已迁移旧设置数据到 Chrome Storage Sync')
      }

      // 这里可以添加其他数据迁移逻辑
    } catch (error) {
      console.warn('数据迁移失败:', error)
    }
  }

  /**
   * 监听存储变化
   */
  static onStorageChanged(callback: (changes: any) => void): void {
    if (typeof chrome !== 'undefined' && chrome.storage?.onChanged) {
      chrome.storage.onChanged.addListener((changes, areaName) => {
        if (areaName === 'sync' && changes[this.SYNC_KEY]) {
          callback(changes[this.SYNC_KEY])
        }
      })
    }
  }
}
