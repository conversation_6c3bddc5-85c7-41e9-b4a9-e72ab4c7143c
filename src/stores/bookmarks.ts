/**
 * 书签状态管理 Store
 * 管理书签数据、文件夹结构、CRUD操作
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { BookmarkWithMeta, BookmarkGroup } from '../types'
import { StorageService } from '../services/storageService'
import { IconService } from '../services/iconService'

export const useBookmarkStore = defineStore('bookmarks', () => {
  // 状态
  const bookmarks = ref<BookmarkWithMeta[]>([])
  const groups = ref<BookmarkGroup[]>([])
  const isLoading = ref(false)
  const selectedBookmarkIds = ref<string[]>([])
  const pendingDeleteIds = ref<string[]>([])

  // 计算属性
  const allFolders = computed(() => {
    return groups.value.filter(group => !group.url)
  })

  const enrichedBookmarks = computed(() => {
    return bookmarks.value.map(bookmark => ({
      ...bookmark,
      // 这里可以添加额外的计算属性，如标签、图标等
    }))
  })

  const bookmarksByGroup = computed(() => {
    const result: Record<string, BookmarkWithMeta[]> = {}
    
    groups.value.forEach(group => {
      result[group.id] = bookmarks.value.filter(bookmark => 
        bookmark.parentId === group.id
      )
    })
    
    return result
  })

  // 多选相关计算属性
  const hasSelectedBookmarks = computed(() => selectedBookmarkIds.value.length > 0)
  const selectedBookmarksCount = computed(() => selectedBookmarkIds.value.length)

  // Actions
  const loadBookmarks = async () => {
    isLoading.value = true
    try {
      // 使用 Chrome Bookmarks API 加载数据
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        const tree = await chrome.bookmarks.getTree()
        const { bookmarks: loadedBookmarks, groups: loadedGroups } = parseBookmarkTree(tree)

        // 加载书签元数据
        const metaData = await StorageService.loadAllBookmarkMeta()

        // 合并书签数据和元数据
        const enrichedBookmarks = loadedBookmarks.map(bookmark => ({
          ...bookmark,
          tags: metaData[bookmark.id]?.tags || [],
          customIcon: metaData[bookmark.id]?.customIconUrl || metaData[bookmark.id]?.customIconData,
          notes: metaData[bookmark.id]?.notes
        }))

        bookmarks.value = enrichedBookmarks
        groups.value = loadedGroups

        // 预加载图标
        const urls = enrichedBookmarks.map(b => b.url).filter(Boolean)
        IconService.preloadIcons(urls).catch(console.warn)

      } else {
        // 开发环境使用模拟数据
        const { mockBookmarks, mockGroups } = await import('../data/mockData')
        bookmarks.value = mockBookmarks
        groups.value = mockGroups
      }
    } catch (error) {
      console.error('加载书签失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  const createBookmark = async (bookmark: Partial<BookmarkWithMeta>) => {
    try {
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        const created = await chrome.bookmarks.create({
          parentId: bookmark.parentId,
          title: bookmark.title,
          url: bookmark.url
        })
        
        // 添加到本地状态
        const newBookmark: BookmarkWithMeta = {
          ...created,
          tags: bookmark.tags || [],
          customIcon: bookmark.customIcon,
          dateAdded: created.dateAdded || Date.now()
        }
        
        bookmarks.value.push(newBookmark)
        return newBookmark
      }
    } catch (error) {
      console.error('创建书签失败:', error)
      throw error
    }
  }

  const updateBookmark = async (id: string, updates: Partial<BookmarkWithMeta>) => {
    try {
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        // 更新 Chrome 书签
        if (updates.title || updates.url) {
          await chrome.bookmarks.update(id, {
            title: updates.title,
            url: updates.url
          })
        }

        // 更新元数据到 IndexedDB
        if (updates.tags || updates.customIcon || updates.notes) {
          await StorageService.saveBookmarkMeta(id, {
            tags: updates.tags,
            customIconUrl: typeof updates.customIcon === 'string' ? updates.customIcon : undefined,
            customIconData: typeof updates.customIcon === 'string' && updates.customIcon.startsWith('data:') ? updates.customIcon : undefined,
            notes: updates.notes
          })
        }

        // 更新本地状态
        const index = bookmarks.value.findIndex(b => b.id === id)
        if (index !== -1) {
          bookmarks.value[index] = { ...bookmarks.value[index], ...updates }
        }
      }
    } catch (error) {
      console.error('更新书签失败:', error)
      throw error
    }
  }

  const deleteBookmark = async (id: string) => {
    try {
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        await chrome.bookmarks.remove(id)

        // 删除元数据
        await StorageService.deleteBookmarkMeta(id)

        // 从本地状态移除
        const index = bookmarks.value.findIndex(b => b.id === id)
        if (index !== -1) {
          bookmarks.value.splice(index, 1)
        }
      }
    } catch (error) {
      console.error('删除书签失败:', error)
      throw error
    }
  }

  const moveBookmark = async (id: string, newParentId: string) => {
    try {
      if (typeof chrome !== 'undefined' && chrome.bookmarks) {
        await chrome.bookmarks.move(id, { parentId: newParentId })
        
        // 更新本地状态
        const bookmark = bookmarks.value.find(b => b.id === id)
        if (bookmark) {
          bookmark.parentId = newParentId
        }
      }
    } catch (error) {
      console.error('移动书签失败:', error)
      throw error
    }
  }

  // 多选操作
  const toggleBookmarkSelection = (id: string) => {
    const index = selectedBookmarkIds.value.indexOf(id)
    if (index === -1) {
      selectedBookmarkIds.value.push(id)
    } else {
      selectedBookmarkIds.value.splice(index, 1)
    }
  }

  const selectAllBookmarks = (groupId?: string) => {
    if (groupId) {
      const groupBookmarks = bookmarks.value.filter(b => b.parentId === groupId)
      selectedBookmarkIds.value = groupBookmarks.map(b => b.id)
    } else {
      selectedBookmarkIds.value = bookmarks.value.map(b => b.id)
    }
  }

  const clearSelection = () => {
    selectedBookmarkIds.value = []
  }

  const deleteSelectedBookmarks = async () => {
    const idsToDelete = [...selectedBookmarkIds.value]
    
    try {
      for (const id of idsToDelete) {
        await deleteBookmark(id)
      }
      clearSelection()
    } catch (error) {
      console.error('批量删除书签失败:', error)
      throw error
    }
  }

  // 撤销删除相关
  const markForDeletion = (id: string) => {
    if (!pendingDeleteIds.value.includes(id)) {
      pendingDeleteIds.value.push(id)
    }
  }

  const unmarkForDeletion = (id: string) => {
    const index = pendingDeleteIds.value.indexOf(id)
    if (index !== -1) {
      pendingDeleteIds.value.splice(index, 1)
    }
  }

  const confirmDeletion = async (id: string) => {
    await deleteBookmark(id)
    unmarkForDeletion(id)
  }

  return {
    // 状态
    bookmarks,
    groups,
    isLoading,
    selectedBookmarkIds,
    pendingDeleteIds,
    
    // 计算属性
    allFolders,
    enrichedBookmarks,
    bookmarksByGroup,
    hasSelectedBookmarks,
    selectedBookmarksCount,
    
    // Actions
    loadBookmarks,
    createBookmark,
    updateBookmark,
    deleteBookmark,
    moveBookmark,
    toggleBookmarkSelection,
    selectAllBookmarks,
    clearSelection,
    deleteSelectedBookmarks,
    markForDeletion,
    unmarkForDeletion,
    confirmDeletion
  }
})

// 辅助函数：解析书签树
function parseBookmarkTree(tree: chrome.bookmarks.BookmarkTreeNode[]): {
  bookmarks: BookmarkWithMeta[]
  groups: BookmarkGroup[]
} {
  const bookmarks: BookmarkWithMeta[] = []
  const groups: BookmarkGroup[] = []

  function traverse(nodes: chrome.bookmarks.BookmarkTreeNode[]) {
    for (const node of nodes) {
      if (node.url) {
        // 这是一个书签
        bookmarks.push({
          id: node.id,
          title: node.title || '',
          url: node.url,
          parentId: node.parentId || '',
          dateAdded: node.dateAdded || Date.now(),
          tags: [], // 将从 IndexedDB 加载
          customIcon: undefined // 将从 IndexedDB 加载
        })
      } else {
        // 这是一个文件夹
        groups.push({
          id: node.id,
          title: node.title || '',
          parentId: node.parentId || '',
          dateAdded: node.dateAdded || Date.now()
        })
      }

      if (node.children) {
        traverse(node.children)
      }
    }
  }

  traverse(tree)
  return { bookmarks, groups }
}
