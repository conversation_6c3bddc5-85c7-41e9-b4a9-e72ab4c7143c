/**
 * 搜索状态管理 Store
 * 管理搜索功能、拼音搜索、搜索历史
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { BookmarkWithMeta } from '../types'

export interface SearchResult extends BookmarkWithMeta {
  matchType: 'title' | 'url' | 'tag' | 'pinyin'
  matchText: string
  score: number
}

export const useSearchStore = defineStore('search', () => {
  // 状态
  const query = ref('')
  const results = ref<SearchResult[]>([])
  const isSearching = ref(false)
  const searchHistory = ref<string[]>([])
  const selectedIndex = ref(0)

  // 计算属性
  const hasQuery = computed(() => query.value.trim().length > 0)
  const hasResults = computed(() => results.value.length > 0)
  const displayedResults = computed(() => results.value.slice(0, 9)) // 最多显示9个结果

  // Actions
  const setQuery = (newQuery: string) => {
    query.value = newQuery
    selectedIndex.value = 0
  }

  const clearQuery = () => {
    query.value = ''
    results.value = []
    selectedIndex.value = 0
  }

  const addToHistory = (searchQuery: string) => {
    const trimmed = searchQuery.trim()
    if (trimmed && !searchHistory.value.includes(trimmed)) {
      searchHistory.value.unshift(trimmed)
      // 限制历史记录数量
      if (searchHistory.value.length > 20) {
        searchHistory.value = searchHistory.value.slice(0, 20)
      }
    }
  }

  const clearHistory = () => {
    searchHistory.value = []
  }

  // 搜索功能（将在后续集成 pinyin-pro 时完善）
  const search = async (bookmarks: BookmarkWithMeta[], searchQuery?: string) => {
    const queryToSearch = searchQuery || query.value
    
    if (!queryToSearch.trim()) {
      results.value = []
      return
    }

    isSearching.value = true
    
    try {
      // 基础搜索逻辑（后续会用 pinyin-pro 增强）
      const searchResults: SearchResult[] = []
      const lowerQuery = queryToSearch.toLowerCase()

      for (const bookmark of bookmarks) {
        const titleMatch = bookmark.title?.toLowerCase().includes(lowerQuery)
        const urlMatch = bookmark.url?.toLowerCase().includes(lowerQuery)
        const tagMatch = bookmark.tags?.some(tag => 
          tag.toLowerCase().includes(lowerQuery)
        )

        if (titleMatch || urlMatch || tagMatch) {
          let matchType: SearchResult['matchType'] = 'title'
          let matchText = bookmark.title || ''
          let score = 0

          if (titleMatch) {
            matchType = 'title'
            matchText = bookmark.title || ''
            score = 100
          } else if (tagMatch) {
            matchType = 'tag'
            matchText = bookmark.tags?.find(tag => 
              tag.toLowerCase().includes(lowerQuery)
            ) || ''
            score = 80
          } else if (urlMatch) {
            matchType = 'url'
            matchText = bookmark.url || ''
            score = 60
          }

          searchResults.push({
            ...bookmark,
            matchType,
            matchText,
            score
          })
        }
      }

      // 按分数排序
      searchResults.sort((a, b) => b.score - a.score)
      results.value = searchResults

      // 添加到搜索历史
      if (searchResults.length > 0) {
        addToHistory(queryToSearch)
      }

    } catch (error) {
      console.error('搜索失败:', error)
      results.value = []
    } finally {
      isSearching.value = false
    }
  }

  // 键盘导航
  const selectNext = () => {
    if (selectedIndex.value < displayedResults.value.length - 1) {
      selectedIndex.value++
    }
  }

  const selectPrevious = () => {
    if (selectedIndex.value > 0) {
      selectedIndex.value--
    }
  }

  const selectByIndex = (index: number) => {
    if (index >= 0 && index < displayedResults.value.length) {
      selectedIndex.value = index
    }
  }

  const getSelectedResult = () => {
    return displayedResults.value[selectedIndex.value]
  }

  // 高亮匹配文本（简单版本，后续会增强）
  const highlightMatch = (text: string, query: string): string => {
    if (!query.trim()) return text
    
    const regex = new RegExp(`(${query})`, 'gi')
    return text.replace(regex, '<mark>$1</mark>')
  }

  return {
    // 状态
    query,
    results,
    isSearching,
    searchHistory,
    selectedIndex,
    
    // 计算属性
    hasQuery,
    hasResults,
    displayedResults,
    
    // Actions
    setQuery,
    clearQuery,
    addToHistory,
    clearHistory,
    search,
    selectNext,
    selectPrevious,
    selectByIndex,
    getSelectedResult,
    highlightMatch
  }
})
