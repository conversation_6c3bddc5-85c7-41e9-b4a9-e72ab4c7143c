/**
 * 搜索状态管理 Store
 * 管理搜索功能、拼音搜索、搜索历史
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { BookmarkWithMeta } from '../types'
import { pinyin } from 'pinyin-pro'

export interface SearchResult extends BookmarkWithMeta {
  matchType: 'title' | 'url' | 'tag' | 'pinyin'
  matchText: string
  score: number
}

export const useSearchStore = defineStore('search', () => {
  // 状态
  const query = ref('')
  const results = ref<SearchResult[]>([])
  const isSearching = ref(false)
  const searchHistory = ref<string[]>([])
  const selectedIndex = ref(0)

  // 计算属性
  const hasQuery = computed(() => query.value.trim().length > 0)
  const hasResults = computed(() => results.value.length > 0)
  const displayedResults = computed(() => results.value.slice(0, 9)) // 最多显示9个结果

  // Actions
  const setQuery = (newQuery: string) => {
    query.value = newQuery
    selectedIndex.value = 0
  }

  const clearQuery = () => {
    query.value = ''
    results.value = []
    selectedIndex.value = 0
  }

  const addToHistory = (searchQuery: string) => {
    const trimmed = searchQuery.trim()
    if (trimmed && !searchHistory.value.includes(trimmed)) {
      searchHistory.value.unshift(trimmed)
      // 限制历史记录数量
      if (searchHistory.value.length > 20) {
        searchHistory.value = searchHistory.value.slice(0, 20)
      }
    }
  }

  const clearHistory = () => {
    searchHistory.value = []
  }

  // 增强搜索功能（集成 pinyin-pro）
  const search = async (bookmarks: BookmarkWithMeta[], searchQuery?: string) => {
    const queryToSearch = searchQuery || query.value

    if (!queryToSearch.trim()) {
      results.value = []
      return
    }

    isSearching.value = true

    try {
      const searchResults: SearchResult[] = []
      const lowerQuery = queryToSearch.toLowerCase()

      for (const bookmark of bookmarks) {
        const matchResult = searchBookmark(bookmark, lowerQuery)
        if (matchResult) {
          searchResults.push(matchResult)
        }
      }

      // 按分数排序
      searchResults.sort((a, b) => b.score - a.score)
      results.value = searchResults

      // 添加到搜索历史
      if (searchResults.length > 0) {
        addToHistory(queryToSearch)
      }

    } catch (error) {
      console.error('搜索失败:', error)
      results.value = []
    } finally {
      isSearching.value = false
    }
  }

  // 搜索单个书签的匹配逻辑
  const searchBookmark = (bookmark: BookmarkWithMeta, query: string): SearchResult | null => {
    const title = bookmark.title || ''
    const url = bookmark.url || ''
    const tags = bookmark.tags || []

    // 1. 精确匹配（最高分）
    if (title.toLowerCase().includes(query)) {
      return {
        ...bookmark,
        matchType: 'title',
        matchText: title,
        score: 100
      }
    }

    // 2. 标签匹配
    const matchedTag = tags.find(tag => tag.toLowerCase().includes(query))
    if (matchedTag) {
      return {
        ...bookmark,
        matchType: 'tag',
        matchText: matchedTag,
        score: 90
      }
    }

    // 3. 拼音全拼匹配
    const titlePinyin = pinyin(title, { toneType: 'none', type: 'array' }).join('')
    if (titlePinyin.toLowerCase().includes(query)) {
      return {
        ...bookmark,
        matchType: 'pinyin',
        matchText: title,
        score: 80
      }
    }

    // 4. 拼音首字母匹配
    const titleInitials = pinyin(title, { pattern: 'first', toneType: 'none' }).toLowerCase()
    if (titleInitials.includes(query)) {
      return {
        ...bookmark,
        matchType: 'pinyin',
        matchText: title,
        score: 70
      }
    }

    // 5. URL匹配（最低分）
    if (url.toLowerCase().includes(query)) {
      return {
        ...bookmark,
        matchType: 'url',
        matchText: url,
        score: 50
      }
    }

    return null
  }

  // 键盘导航
  const selectNext = () => {
    if (selectedIndex.value < displayedResults.value.length - 1) {
      selectedIndex.value++
    }
  }

  const selectPrevious = () => {
    if (selectedIndex.value > 0) {
      selectedIndex.value--
    }
  }

  const selectByIndex = (index: number) => {
    if (index >= 0 && index < displayedResults.value.length) {
      selectedIndex.value = index
    }
  }

  const getSelectedResult = () => {
    return displayedResults.value[selectedIndex.value]
  }

  // 高亮匹配文本（增强版本，支持拼音匹配）
  const highlightMatch = (text: string, query: string): string => {
    if (!query.trim()) return text

    // 1. 直接文本匹配
    const directMatch = new RegExp(`(${escapeRegex(query)})`, 'gi')
    if (directMatch.test(text)) {
      return text.replace(directMatch, '<span class="search-highlight">$1</span>')
    }

    // 2. 拼音匹配高亮
    try {
      const textPinyin = pinyin(text, { toneType: 'none', type: 'array' })
      const textInitials = pinyin(text, { pattern: 'first', toneType: 'none' })

      // 检查是否是拼音首字母匹配
      if (textInitials.toLowerCase().includes(query.toLowerCase())) {
        // 简单高亮整个文本（拼音匹配时）
        return `<span class="search-highlight">${text}</span>`
      }
    } catch (error) {
      console.warn('拼音处理失败:', error)
    }

    return text
  }

  // 转义正则表达式特殊字符
  const escapeRegex = (string: string): string => {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
  }

  return {
    // 状态
    query,
    results,
    isSearching,
    searchHistory,
    selectedIndex,
    
    // 计算属性
    hasQuery,
    hasResults,
    displayedResults,
    
    // Actions
    setQuery,
    clearQuery,
    addToHistory,
    clearHistory,
    search,
    selectNext,
    selectPrevious,
    selectByIndex,
    getSelectedResult,
    highlightMatch
  }
})
