/**
 * 设置状态管理 Store
 * 管理用户设置、偏好配置
 */

import { defineStore } from 'pinia'
import { ref, watch } from 'vue'
import { StorageService } from '../services/storageService'

export interface AppSettings {
  // 显示设置
  viewMode: 'card' | 'list'
  bookmarkStyle: 'square' | 'rectangle'
  folderLayout: 'single' | 'double'
  autoCollapseFolders: boolean
  
  // 标签设置
  tagsEnabled: boolean
  tagSeparator: string
  
  // 搜索设置
  searchShortcut: string
  
  // 主题设置
  theme: 'light' | 'dark' | 'auto'
  backgroundImage: string
  backgroundOpacity: number
  backgroundBlur: number
  
  // 其他设置
  enableAnimations: boolean
  enableSounds: boolean
  autoRefresh: boolean
  
  // 首次运行标记
  isFirstRun: boolean
  onboardingCompleted: boolean
}

const DEFAULT_SETTINGS: AppSettings = {
  viewMode: 'card',
  bookmarkStyle: 'square',
  folderLayout: 'single',
  autoCollapseFolders: true,
  tagsEnabled: true,
  tagSeparator: '#',
  searchShortcut: 'cmd+/',
  theme: 'light',
  backgroundImage: '',
  backgroundOpacity: 100,
  backgroundBlur: 0,
  enableAnimations: true,
  enableSounds: false,
  autoRefresh: true,
  isFirstRun: true,
  onboardingCompleted: false
}

export const useSettingsStore = defineStore('settings', () => {
  // 状态
  const settings = ref<AppSettings>({ ...DEFAULT_SETTINGS })
  const isLoading = ref(false)

  // Actions
  const loadSettings = async () => {
    isLoading.value = true
    try {
      const loadedSettings = await StorageService.loadSettings()
      if (loadedSettings) {
        settings.value = { ...DEFAULT_SETTINGS, ...loadedSettings }
      }
    } catch (error) {
      console.error('加载设置失败:', error)
    } finally {
      isLoading.value = false
    }
  }

  const saveSettings = async () => {
    try {
      await StorageService.saveSettings(settings.value)
    } catch (error) {
      console.error('保存设置失败:', error)
      throw error
    }
  }

  const updateSetting = async <K extends keyof AppSettings>(
    key: K, 
    value: AppSettings[K]
  ) => {
    settings.value[key] = value
    await saveSettings()
  }

  const resetSettings = async () => {
    settings.value = { ...DEFAULT_SETTINGS }
    await saveSettings()
  }

  const completeOnboarding = async () => {
    await updateSetting('isFirstRun', false)
    await updateSetting('onboardingCompleted', true)
  }

  // 导出设置
  const exportSettings = (): string => {
    return JSON.stringify(settings.value, null, 2)
  }

  // 导入设置
  const importSettings = async (settingsJson: string) => {
    try {
      const importedSettings = JSON.parse(settingsJson)
      
      // 验证设置格式
      const validatedSettings = { ...DEFAULT_SETTINGS, ...importedSettings }
      
      settings.value = validatedSettings
      await saveSettings()
      
      return true
    } catch (error) {
      console.error('导入设置失败:', error)
      return false
    }
  }

  // 监听设置变化，自动保存
  watch(
    settings,
    () => {
      saveSettings()
    },
    { deep: true }
  )

  return {
    // 状态
    settings,
    isLoading,
    
    // Actions
    loadSettings,
    saveSettings,
    updateSetting,
    resetSettings,
    completeOnboarding,
    exportSettings,
    importSettings
  }
})
