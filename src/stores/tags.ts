/**
 * 标签状态管理 Store
 * 管理标签数据、颜色、筛选逻辑
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { StorageService } from '../services/storageService'

export interface Tag {
  name: string
  color: string
  count: number
  createdAt: number
}

export const useTagStore = defineStore('tags', () => {
  // 状态
  const tags = ref<Tag[]>([])
  const activeTags = ref<string[]>([])
  const filterMode = ref<'AND' | 'OR'>('OR')
  const tagSeparator = ref('#')

  // 计算属性
  const availableTags = computed(() => {
    return tags.value.filter(tag => tag.count > 0)
  })

  const hasActiveTags = computed(() => activeTags.value.length > 0)

  // 智能配色算法 - 基于标签名称哈希值生成确定性颜色
  const generateTagColor = (tagName: string): string => {
    let hash = 0
    for (let i = 0; i < tagName.length; i++) {
      hash = tagName.charCodeAt(i) + ((hash << 5) - hash)
    }
    
    // 将哈希值映射到 HSL 色彩空间
    // 固定饱和度和亮度，只改变色相，确保颜色和谐统一
    const hue = Math.abs(hash) % 360
    const saturation = 65 // 固定饱和度，避免过于鲜艳
    const lightness = 55  // 固定亮度，确保可读性
    
    return `hsl(${hue}, ${saturation}%, ${lightness}%)`
  }

  // Actions
  const loadTags = async () => {
    try {
      const loadedTags = await StorageService.loadAllTags()
      tags.value = loadedTags.map(tagDef => ({
        name: tagDef.name,
        color: tagDef.color,
        count: tagDef.count,
        createdAt: tagDef.createdAt
      }))
    } catch (error) {
      console.error('加载标签失败:', error)
    }
  }

  const createTag = (name: string): Tag => {
    const existingTag = tags.value.find(tag => tag.name === name)
    if (existingTag) {
      return existingTag
    }

    const newTag: Tag = {
      name,
      color: generateTagColor(name),
      count: 0,
      createdAt: Date.now()
    }

    tags.value.push(newTag)

    // 保存到存储
    StorageService.saveTag({
      name: newTag.name,
      color: newTag.color,
      count: newTag.count,
      createdAt: newTag.createdAt,
      updatedAt: Date.now()
    }).catch(console.error)

    return newTag
  }

  const updateTagCount = (tagName: string, count: number) => {
    const tag = tags.value.find(t => t.name === tagName)
    if (tag) {
      tag.count = count
    }
  }

  const deleteTag = (tagName: string) => {
    const index = tags.value.findIndex(t => t.name === tagName)
    if (index !== -1) {
      tags.value.splice(index, 1)
    }
  }

  const toggleTag = (tagName: string) => {
    const index = activeTags.value.indexOf(tagName)
    if (index === -1) {
      activeTags.value.push(tagName)
    } else {
      activeTags.value.splice(index, 1)
    }
  }

  const clearActiveTags = () => {
    activeTags.value = []
  }

  const setFilterMode = (mode: 'AND' | 'OR') => {
    filterMode.value = mode
  }

  const setTagSeparator = (separator: string) => {
    tagSeparator.value = separator
  }

  // 从书签标题中提取标签
  const extractTagsFromTitle = (title: string): { cleanTitle: string; tags: string[] } => {
    const separator = tagSeparator.value
    const parts = title.split(separator)
    
    if (parts.length === 1) {
      return { cleanTitle: title, tags: [] }
    }

    const cleanTitle = parts[0].trim()
    const tags = parts.slice(1)
      .map(tag => tag.trim())
      .filter(tag => tag.length > 0)

    return { cleanTitle, tags }
  }

  // 将标签添加到书签标题
  const addTagsToTitle = (title: string, tags: string[]): string => {
    if (tags.length === 0) {
      return title
    }

    const separator = tagSeparator.value
    return `${title}${separator}${tags.join(separator)}`
  }

  // 批量更新标签统计
  const updateTagStatistics = async (bookmarks: Array<{ tags?: string[] }>) => {
    // 重置所有标签计数
    tags.value.forEach(tag => {
      tag.count = 0
    })

    // 统计标签使用次数
    const tagCounts: Record<string, number> = {}

    bookmarks.forEach(bookmark => {
      if (bookmark.tags) {
        bookmark.tags.forEach(tagName => {
          tagCounts[tagName] = (tagCounts[tagName] || 0) + 1
        })
      }
    })

    // 更新现有标签计数，创建新标签
    Object.entries(tagCounts).forEach(([tagName, count]) => {
      const existingTag = tags.value.find(t => t.name === tagName)
      if (existingTag) {
        existingTag.count = count
      } else {
        createTag(tagName)
        updateTagCount(tagName, count)
      }
    })

    // 移除计数为0的标签
    tags.value = tags.value.filter(tag => tag.count > 0)

    // 批量更新到存储
    try {
      await StorageService.updateTagStatistics(tagCounts)
    } catch (error) {
      console.error('更新标签统计失败:', error)
    }
  }

  // 根据激活的标签筛选书签
  const filterBookmarksByTags = <T extends { tags?: string[] }>(bookmarks: T[]): T[] => {
    if (activeTags.value.length === 0) {
      return bookmarks
    }

    return bookmarks.filter(bookmark => {
      if (!bookmark.tags || bookmark.tags.length === 0) {
        return false
      }

      if (filterMode.value === 'AND') {
        // AND 模式：书签必须包含所有激活的标签
        return activeTags.value.every(activeTag => 
          bookmark.tags!.includes(activeTag)
        )
      } else {
        // OR 模式：书签包含任一激活标签即可
        return activeTags.value.some(activeTag => 
          bookmark.tags!.includes(activeTag)
        )
      }
    })
  }

  return {
    // 状态
    tags,
    activeTags,
    filterMode,
    tagSeparator,
    
    // 计算属性
    availableTags,
    hasActiveTags,
    
    // Actions
    loadTags,
    createTag,
    updateTagCount,
    deleteTag,
    toggleTag,
    clearActiveTags,
    setFilterMode,
    setTagSeparator,
    extractTagsFromTitle,
    addTagsToTitle,
    updateTagStatistics,
    filterBookmarksByTags,
    generateTagColor
  }
})
