/**
 * UI 状态管理 Store
 * 管理界面状态、模态框、抽屉、主题等
 */

import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

export type ViewMode = 'card' | 'list'
export type Theme = 'light' | 'dark' | 'auto'

export const useUIStore = defineStore('ui', () => {
  // 界面状态
  const viewMode = ref<ViewMode>('card')
  const theme = ref<Theme>('light')
  const showLeftDrawer = ref(false)
  const showRightDrawer = ref(false)
  
  // 搜索相关
  const searchQuery = ref('')
  const isSearchVisible = ref(true)
  const isSearchHidden = ref(false)
  
  // 模态框状态
  const showEditModal = ref(false)
  const showMoveModal = ref(false)
  const showConfirmModal = ref(false)
  const showQRCodeModal = ref(false)
  const showDeleteToast = ref(false)
  
  // 当前编辑的书签
  const currentEditBookmark = ref<any>(null)
  const currentQRCodeBookmark = ref<any>(null)
  
  // 确认模态框数据
  const confirmModalData = ref({
    type: 'info' as 'info' | 'warning' | 'danger',
    title: '',
    message: '',
    confirmText: '确定',
    cancelText: '取消',
    onConfirm: () => {}
  })
  
  // 删除提示数据
  const deleteToastMessage = ref('')
  
  // 计算属性
  const isDarkMode = computed(() => {
    if (theme.value === 'auto') {
      return window.matchMedia('(prefers-color-scheme: dark)').matches
    }
    return theme.value === 'dark'
  })
  
  const hasAnyModalOpen = computed(() => {
    return showEditModal.value || 
           showMoveModal.value || 
           showConfirmModal.value || 
           showQRCodeModal.value
  })
  
  // Actions
  const setViewMode = (mode: ViewMode) => {
    viewMode.value = mode
  }
  
  const setTheme = (newTheme: Theme) => {
    theme.value = newTheme
    applyTheme(newTheme)
  }
  
  const toggleTheme = () => {
    const themes: Theme[] = ['light', 'dark', 'auto']
    const currentIndex = themes.indexOf(theme.value)
    const nextIndex = (currentIndex + 1) % themes.length
    setTheme(themes[nextIndex])
  }
  
  const applyTheme = (themeValue: Theme) => {
    const html = document.documentElement
    
    // 移除所有主题类
    html.classList.remove('light', 'dark')
    
    if (themeValue === 'auto') {
      // 自动模式：根据系统偏好设置
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      html.classList.add(prefersDark ? 'dark' : 'light')
      html.setAttribute('data-theme', prefersDark ? 'dark' : 'light')
    } else {
      html.classList.add(themeValue)
      html.setAttribute('data-theme', themeValue)
    }
  }
  
  const toggleLeftDrawer = () => {
    showLeftDrawer.value = !showLeftDrawer.value
  }
  
  const toggleRightDrawer = () => {
    showRightDrawer.value = !showRightDrawer.value
  }
  
  const closeAllDrawers = () => {
    showLeftDrawer.value = false
    showRightDrawer.value = false
  }
  
  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }
  
  const setSearchVisible = (visible: boolean) => {
    isSearchVisible.value = visible
  }
  
  const setSearchHidden = (hidden: boolean) => {
    isSearchHidden.value = hidden
  }
  
  // 模态框控制
  const openEditModal = (bookmark?: any) => {
    currentEditBookmark.value = bookmark
    showEditModal.value = true
  }
  
  const closeEditModal = () => {
    showEditModal.value = false
    currentEditBookmark.value = null
  }
  
  const openMoveModal = () => {
    showMoveModal.value = true
  }
  
  const closeMoveModal = () => {
    showMoveModal.value = false
  }
  
  const openQRCodeModal = (bookmark: any) => {
    currentQRCodeBookmark.value = bookmark
    showQRCodeModal.value = true
  }
  
  const closeQRCodeModal = () => {
    showQRCodeModal.value = false
    currentQRCodeBookmark.value = null
  }
  
  const openConfirmModal = (data: {
    type?: 'info' | 'warning' | 'danger'
    title: string
    message: string
    confirmText?: string
    cancelText?: string
    onConfirm: () => void
  }) => {
    confirmModalData.value = {
      type: data.type || 'info',
      title: data.title,
      message: data.message,
      confirmText: data.confirmText || '确定',
      cancelText: data.cancelText || '取消',
      onConfirm: data.onConfirm
    }
    showConfirmModal.value = true
  }
  
  const closeConfirmModal = () => {
    showConfirmModal.value = false
  }
  
  const showDeleteToastMessage = (message: string) => {
    deleteToastMessage.value = message
    showDeleteToast.value = true
  }
  
  const hideDeleteToast = () => {
    showDeleteToast.value = false
    deleteToastMessage.value = ''
  }
  
  const closeAllModals = () => {
    closeEditModal()
    closeMoveModal()
    closeConfirmModal()
    closeQRCodeModal()
    hideDeleteToast()
  }
  
  return {
    // 状态
    viewMode,
    theme,
    showLeftDrawer,
    showRightDrawer,
    searchQuery,
    isSearchVisible,
    isSearchHidden,
    showEditModal,
    showMoveModal,
    showConfirmModal,
    showQRCodeModal,
    showDeleteToast,
    currentEditBookmark,
    currentQRCodeBookmark,
    confirmModalData,
    deleteToastMessage,
    
    // 计算属性
    isDarkMode,
    hasAnyModalOpen,
    
    // Actions
    setViewMode,
    setTheme,
    toggleTheme,
    applyTheme,
    toggleLeftDrawer,
    toggleRightDrawer,
    closeAllDrawers,
    setSearchQuery,
    setSearchVisible,
    setSearchHidden,
    openEditModal,
    closeEditModal,
    openMoveModal,
    closeMoveModal,
    openQRCodeModal,
    closeQRCodeModal,
    openConfirmModal,
    closeConfirmModal,
    showDeleteToastMessage,
    hideDeleteToast,
    closeAllModals
  }
})
