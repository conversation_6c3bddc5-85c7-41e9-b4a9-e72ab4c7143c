/* =================================
   基础重置和全局样式
   ================================= */

* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  color: #1f2937;
  background-color: #ffffff;
}

/* Tailwind CSS 工具类替代 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

.flex-1 { flex: 1 1 0%; }
.flex-shrink-0 { flex-shrink: 0; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }

.w-full { width: 100%; }
.h-full { height: 100%; }
.w-8 { width: 2rem; }
.h-8 { height: 2rem; }
.w-4 { width: 1rem; }
.h-4 { height: 1rem; }
.w-12 { width: 3rem; }
.w-16 { width: 4rem; }
.h-16 { height: 4rem; }

.p-0 { padding: 0; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.px-4 { padding-left: 1rem; padding-right: 1rem; }
.py-1 { padding-top: 0.25rem; padding-bottom: 0.25rem; }
.py-2 { padding-top: 0.5rem; padding-bottom: 0.5rem; }
.py-3 { padding-top: 0.75rem; padding-bottom: 0.75rem; }
.pb-4 { padding-bottom: 1rem; }

.m-0 { margin: 0; }
.mx-4 { margin-left: 1rem; margin-right: 1rem; }
.mx-6 { margin-left: 1.5rem; margin-right: 1.5rem; }
.mt-4 { margin-top: 1rem; }
.mt-6 { margin-top: 1.5rem; }
.mb-4 { margin-bottom: 1rem; }
.mr-2 { margin-right: 0.5rem; }

.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }

.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }

.font-medium { font-weight: 500; }
.font-bold { font-weight: 700; }

.text-center { text-align: center; }
.text-left { text-align: left; }

.rounded { border-radius: 0.25rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-full { border-radius: 9999px; }

.border { border-width: 1px; }
.border-b { border-bottom-width: 1px; }
.border-gray-200 { border-color: #e5e7eb; }
.border-gray-300 { border-color: #d1d5db; }

.bg-white { background-color: #ffffff; }
.bg-gray-100 { background-color: #f3f4f6; }
.bg-blue-100 { background-color: #dbeafe; }

.text-gray-500 { color: #6b7280; }
.text-gray-600 { color: #4b5563; }
.text-gray-700 { color: #374151; }
.text-gray-900 { color: #111827; }
.text-blue-600 { color: #2563eb; }
.text-blue-700 { color: #1d4ed8; }

.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

.fixed { position: fixed; }
.absolute { position: absolute; }
.relative { position: relative; }

.top-0 { top: 0; }
.left-0 { left: 0; }
.right-2 { right: 0.5rem; }
.top-2 { top: 0.5rem; }

.z-10 { z-index: 10; }

.cursor-pointer { cursor: pointer; }

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-4 > * + * { margin-top: 1rem; }

.min-w-0 { min-width: 0; }
.max-h-64 { max-height: 16rem; }
.overflow-y-auto { overflow-y: auto; }

.transition-colors { transition-property: color, background-color, border-color; }
.duration-200 { transition-duration: 200ms; }

/* =================================
   基础组件样式系统 (替代daisyUI)
   ================================= */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: 1px solid;
  transition: all 0.2s;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #4b5563;
  color: white;
  border-color: #4b5563;
}

.btn-secondary:hover {
  background-color: #374151;
}

.btn-outline {
  background-color: transparent;
  border-color: #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background-color: #f9fafb;
}

.btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: #374151;
}

.btn-ghost:hover {
  background-color: #f3f4f6;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-circle {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  padding: 0;
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.2s;
}

.input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  outline: none;
  transition: all 0.2s;
}

.select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 模态框组件 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10500;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
}

/* 模态框推移样式 - 已移除，因为模态框现在在main外部 */

.modal-box {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 1.5rem;
  max-width: 28rem;
  width: 100%;
  margin: 0 1rem;
}

.modal-action {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* 卡片组件 */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.card-body {
  padding: 1rem;
}

/* 标签页组件 */
.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  cursor: pointer;
}

.tab:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.tab-active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

/* 切换开关组件 */
.toggle {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  background-color: #e5e7eb;
  transition: all 0.2s;
  cursor: pointer;
  outline: none;
}

.toggle:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.toggle:checked {
  background-color: #2563eb;
}

.toggle::before {
  content: '';
  display: inline-block;
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background-color: white;
  transition: transform 0.2s;
  transform: translateX(2px);
}

.toggle:checked::before {
  transform: translateX(22px);
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.badge-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

/* 抽屉组件 */
.drawer {
  position: fixed;
  top: 0;
  height: 100vh;
  background-color: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 10100;
}

.drawer-left {
  left: 0;
}

.drawer-right {
  right: 0;
}

/* =================================
   应用布局样式
   ================================= */

.app-layout {
  min-height: 100vh;
  background-color: var(--bg-theme, #ffffff);
  transition: background-color 0.3s ease;
}

.main-content-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  transition: transform 0.3s ease-out;
}

.content-pushed {
  transform: translateX(45px);
}

@media (max-width: 768px) {
  .content-pushed {
    transform: translateX(30px);
  }
}

.search-container {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  width: 100%;
  max-width: 600px;
  padding: 0 1rem;
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.search-hidden {
  opacity: 0;
  transform: translateX(-50%) translateY(-20px);
  pointer-events: none;
}

.search-visible {
  opacity: 1;
  transform: translateX(-50%) translateY(0);
}

/* 搜索框抽屉偏移 */
.search-with-drawer-offset {
  transform: translateX(calc(-50% + var(--drawer-offset, 0px)));
}

.search-with-drawer-offset.search-hidden {
  transform: translateX(calc(-50% + var(--drawer-offset, 0px))) translateY(-20px);
}

.search-with-drawer-offset.search-visible {
  transform: translateX(calc(-50% + var(--drawer-offset, 0px))) translateY(0);
}

@media (max-width: 768px) {
  .search-with-drawer-offset {
    transform: translateX(calc(-50% + var(--drawer-offset-mobile, 0px)));
  }

  .search-with-drawer-offset.search-hidden {
    transform: translateX(calc(-50% + var(--drawer-offset-mobile, 0px))) translateY(-20px);
  }

  .search-with-drawer-offset.search-visible {
    transform: translateX(calc(-50% + var(--drawer-offset-mobile, 0px))) translateY(0);
  }
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 4rem 0;
}

.empty-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.empty-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: #111827;
}

.empty-description {
  color: #6b7280;
  font-size: 1rem;
}

/* 侧边按钮组 */
.side-buttons-left {
  position: fixed;
  bottom: 2rem;
  left: 2rem;
  z-index: 40;
}

.side-buttons-right {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 40;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

/* 加载骨架屏 */
.loading-skeleton {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.skeleton-search-bar {
  height: 3rem;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.75rem;
  margin-bottom: 2rem;
}

.skeleton-folders {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.skeleton-folder {
  width: 100%;
}

.skeleton-folder-title {
  height: 2rem;
  width: 200px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.5rem;
  margin-bottom: 1rem;
}

.skeleton-bookmarks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.skeleton-bookmark {
  height: 120px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 暗色主题 */
[data-theme="dark"] .app-layout {
  --bg-theme: #0f172a;
  color: #f1f5f9;
}

[data-theme="dark"] .empty-title {
  color: #f1f5f9;
}

[data-theme="dark"] .empty-description {
  color: #94a3b8;
}

[data-theme="dark"] .skeleton-search-bar,
[data-theme="dark"] .skeleton-folder-title,
[data-theme="dark"] .skeleton-bookmark {
  background: linear-gradient(90deg, #1e293b 25%, #334155 50%, #1e293b 75%);
  background-size: 200% 100%;
}

/* 防止拖拽时选中文字 - 针对 vue-draggable-plus */
.bookmarks-container,
.bookmarks-container *,
.draggable-item,
.draggable-item *,
.bookmark-card,
.bookmark-card *,
.folder-item,
.folder-item *,
[draggable="true"],
[draggable="true"] *,
.sortable-chosen,
.sortable-chosen *,
.sortable-ghost,
.sortable-ghost *,
.sortable-drag,
.sortable-drag * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* 拖拽时的光标样式 */
.bookmarks-container:active,
.draggable-item:active,
.bookmark-card:active,
[draggable="true"]:active,
.sortable-chosen,
.sortable-drag {
  cursor: grabbing !important;
}

/* 拖拽容器样式 */
.bookmarks-container {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 拖拽时禁用文本选择，但保持交互性 */
body.sortable-drag-active * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* 不禁用 pointer-events，保持拖拽功能正常 */

/* 强制禁用文本选择的全局样式 */
::selection {
  background: transparent;
}

::-moz-selection {
  background: transparent;
}

/* 拖拽时的额外保护 */
.sortable-chosen::selection,
.sortable-ghost::selection,
.sortable-drag::selection {
  background: transparent !important;
}

/* Z-INDEX 层级体系 */
/*
  10600+ - 抽屉内模态框 (最高优先级)
  10500+ - 主要模态框 (高优先级)
  10400  - 多选工具栏
  10300  - 搜索框
  10200  - 右侧抽屉
  10100  - 左侧抽屉
  100    - 其他固定元素
  50     - 侧边按钮
  40     - 侧边按钮组
  30     - 文件夹导航
*/

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#app {
  height: 100vh;
  overflow-y: auto;
}

/* 拖拽相关样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  opacity: 0.8;
}

.sortable-drag {
  opacity: 0.9;
}

/* 拖拽占位符样式 */
.drag-placeholder {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(59, 130, 246, 0.6);
  font-size: 0.875rem;
  margin: 0.25rem 0;
}

/* 选择状态样式 */
.bookmark-selected {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* 删除倒计时样式 */
.bookmark-deleting {
  opacity: 0.5;
  background-color: rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
}

/* 动画类 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from, .slide-leave-to {
  transform: translateX(100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bookmark-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

/* 搜索框样式 - 始终使用fixed定位 */
.search-container {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  max-width: 600px;
  z-index: 10300; /* 确保在左侧抽屉(z-index: 10201)之上 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-radius: 1rem;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 暗色主题 */
[data-theme="dark"] .search-container {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 使用CSS变量控制搜索框推移 */
.search-container {
  left: calc(50% + var(--drawer-offset, 0px));
}

/* 移动端使用不同的推移距离 */
@media (max-width: 768px) {
  .search-container {
    left: calc(50% + var(--drawer-offset-mobile, 0px));
  }
}

/* 搜索框隐藏状态 */
.search-container.search-hidden {
  transform: translateX(-50%) translateY(-120%);
  opacity: 0;
  pointer-events: none;
  scale: 0.95;
}

/* 搜索框显示状态 */
.search-container.search-visible {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
  pointer-events: auto;
  scale: 1;
  top: 1rem;
}

/* 搜索框交互效果 */
.search-container:hover {
  scale: 1.02;
}

[data-theme="dark"] .search-container:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* 搜索框聚焦状态 */
.search-container:focus-within {
  scale: 1.02;
}



/* 多选工具栏单行显示 */
.selection-toolbar {
  position: fixed;
  top: 1rem;
  left: calc(50% + var(--drawer-offset, 0px));
  transform: translateX(-50%);
  z-index: 10400; /* 确保在搜索框之上 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  white-space: nowrap;
}

[data-theme="dark"] .selection-toolbar {
  background: rgba(0, 0, 0, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 移动端多选工具栏推移调整 */
@media (max-width: 768px) {
  .selection-toolbar {
    left: calc(50% + var(--drawer-offset-mobile, 0px));
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
