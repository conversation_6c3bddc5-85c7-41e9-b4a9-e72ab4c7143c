@import "tailwindcss";

/* =================================
   基础组件样式系统 (替代daisyUI)
   ================================= */

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.5rem;
  border: 1px solid;
  transition: all 0.2s;
  cursor: pointer;
  text-decoration: none;
}

.btn-primary {
  background-color: #2563eb;
  color: white;
  border-color: #2563eb;
}

.btn-primary:hover {
  background-color: #1d4ed8;
}

.btn-secondary {
  background-color: #4b5563;
  color: white;
  border-color: #4b5563;
}

.btn-secondary:hover {
  background-color: #374151;
}

.btn-outline {
  background-color: transparent;
  border-color: #d1d5db;
  color: #374151;
}

.btn-outline:hover {
  background-color: #f9fafb;
}

.btn-ghost {
  background-color: transparent;
  border-color: transparent;
  color: #374151;
}

.btn-ghost:hover {
  background-color: #f3f4f6;
}

.btn-sm {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.btn-circle {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  padding: 0;
}

/* 输入框组件 */
.input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  outline: none;
  transition: all 0.2s;
}

.input:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.select {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background-color: white;
  outline: none;
  transition: all 0.2s;
}

.select:focus {
  border-color: #2563eb;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

/* 模态框组件 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 10500;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.4);
  backdrop-filter: blur(8px);
}

/* 模态框推移样式 - 当左侧抽屉展开时 */
.modal-with-drawer-offset {
  padding-left: var(--drawer-offset, 0px);
}

@media (max-width: 768px) {
  .modal-with-drawer-offset {
    padding-left: var(--drawer-offset-mobile, 0px);
  }
}

.modal-box {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  padding: 1.5rem;
  max-width: 28rem;
  width: 100%;
  margin: 0 1rem;
}

.modal-action {
  display: flex;
  justify-content: flex-end;
  gap: 0.5rem;
  margin-top: 1.5rem;
}

/* 卡片组件 */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  border: 1px solid #e5e7eb;
}

.card-body {
  padding: 1rem;
}

/* 标签页组件 */
.tabs {
  display: flex;
  border-bottom: 1px solid #e5e7eb;
}

.tab {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  cursor: pointer;
}

.tab:hover {
  color: #374151;
  border-bottom-color: #d1d5db;
}

.tab-active {
  color: #2563eb;
  border-bottom-color: #2563eb;
}

/* 切换开关组件 */
.toggle {
  position: relative;
  display: inline-flex;
  height: 1.5rem;
  width: 2.75rem;
  align-items: center;
  border-radius: 9999px;
  background-color: #e5e7eb;
  transition: all 0.2s;
  cursor: pointer;
  outline: none;
}

.toggle:focus {
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.toggle:checked {
  background-color: #2563eb;
}

.toggle::before {
  content: '';
  display: inline-block;
  height: 1rem;
  width: 1rem;
  border-radius: 50%;
  background-color: white;
  transition: transform 0.2s;
  transform: translateX(2px);
}

.toggle:checked::before {
  transform: translateX(22px);
}

/* 徽章组件 */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 9999px;
}

.badge-primary {
  background-color: #dbeafe;
  color: #1e40af;
}

/* 抽屉组件 */
.drawer {
  position: fixed;
  top: 0;
  height: 100vh;
  background-color: white;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  z-index: 10100;
}

.drawer-left {
  left: 0;
}

.drawer-right {
  right: 0;
}

/* 防止拖拽时选中文字 - 针对 vue-draggable-plus */
.bookmarks-container,
.bookmarks-container *,
.draggable-item,
.draggable-item *,
.bookmark-card,
.bookmark-card *,
.folder-item,
.folder-item *,
[draggable="true"],
[draggable="true"] *,
.sortable-chosen,
.sortable-chosen *,
.sortable-ghost,
.sortable-ghost *,
.sortable-drag,
.sortable-drag * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* 拖拽时的光标样式 */
.bookmarks-container:active,
.draggable-item:active,
.bookmark-card:active,
[draggable="true"]:active,
.sortable-chosen,
.sortable-drag {
  cursor: grabbing !important;
}

/* 拖拽容器样式 */
.bookmarks-container {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
}

/* 拖拽时禁用文本选择，但保持交互性 */
body.sortable-drag-active * {
  user-select: none !important;
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  -webkit-touch-callout: none !important;
}

/* 不禁用 pointer-events，保持拖拽功能正常 */

/* 强制禁用文本选择的全局样式 */
::selection {
  background: transparent;
}

::-moz-selection {
  background: transparent;
}

/* 拖拽时的额外保护 */
.sortable-chosen::selection,
.sortable-ghost::selection,
.sortable-drag::selection {
  background: transparent !important;
}

/* Z-INDEX 层级体系 */
/*
  10600+ - 抽屉内模态框 (最高优先级)
  10500+ - 主要模态框 (高优先级)
  10400  - 多选工具栏
  10300  - 搜索框
  10200  - 右侧抽屉
  10100  - 左侧抽屉
  100    - 其他固定元素
  50     - 侧边按钮
  40     - 侧边按钮组
  30     - 文件夹导航
*/

/* 全局样式重置 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
}

#app {
  height: 100vh;
  overflow-y: auto;
}

/* 拖拽相关样式 */
.sortable-ghost {
  opacity: 0.5;
}

.sortable-chosen {
  opacity: 0.8;
}

.sortable-drag {
  opacity: 0.9;
}

/* 拖拽占位符样式 */
.drag-placeholder {
  background-color: rgba(59, 130, 246, 0.1);
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.5rem;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(59, 130, 246, 0.6);
  font-size: 0.875rem;
  margin: 0.25rem 0;
}

/* 选择状态样式 */
.bookmark-selected {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
}

/* 删除倒计时样式 */
.bookmark-deleting {
  opacity: 0.5;
  background-color: rgba(239, 68, 68, 0.1);
  transition: all 0.3s ease;
}

/* 动画类 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.slide-enter-active, .slide-leave-active {
  transition: transform 0.3s ease;
}

.slide-enter-from, .slide-leave-to {
  transform: translateX(100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .bookmark-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  }
}

/* 搜索框样式 - 始终使用fixed定位 */
.search-container {
  position: fixed;
  top: 2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 50%;
  max-width: 600px;
  z-index: 10300; /* 确保在左侧抽屉(z-index: 10201)之上 */
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(12px);
  border-radius: 1rem;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 暗色主题 */
[data-theme="dark"] .search-container {
  background: rgba(15, 23, 42, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

/* 使用CSS变量控制搜索框推移 */
.search-container {
  left: calc(50% + var(--drawer-offset, 0px));
}

/* 移动端使用不同的推移距离 */
@media (max-width: 768px) {
  .search-container {
    left: calc(50% + var(--drawer-offset-mobile, 0px));
  }
}

/* 搜索框隐藏状态 */
.search-container.search-hidden {
  transform: translateX(-50%) translateY(-120%);
  opacity: 0;
  pointer-events: none;
  scale: 0.95;
}

/* 搜索框显示状态 */
.search-container.search-visible {
  transform: translateX(-50%) translateY(0);
  opacity: 1;
  pointer-events: auto;
  scale: 1;
  top: 1rem;
}

/* 搜索框交互效果 */
.search-container:hover {
  scale: 1.02;
}

[data-theme="dark"] .search-container:hover {
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

/* 搜索框聚焦状态 */
.search-container:focus-within {
  scale: 1.02;
}



/* 多选工具栏单行显示 */
.selection-toolbar {
  position: fixed;
  top: 1rem;
  left: calc(50% + var(--drawer-offset, 0px));
  transform: translateX(-50%);
  z-index: 10400; /* 确保在搜索框之上 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
  border-radius: 0.75rem;
  padding: 0.75rem 1.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  gap: 1rem;
  white-space: nowrap;
}

[data-theme="dark"] .selection-toolbar {
  background: rgba(0, 0, 0, 0.95);
  border-color: rgba(255, 255, 255, 0.1);
}

/* 移动端多选工具栏推移调整 */
@media (max-width: 768px) {
  .selection-toolbar {
    left: calc(50% + var(--drawer-offset-mobile, 0px));
  }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
