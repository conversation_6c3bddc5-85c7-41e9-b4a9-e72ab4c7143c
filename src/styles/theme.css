/* 主题变量定义 */
:root {
  /* 自定义背景色 - 温和护眼色调 */
  --bg-primary: #f5f5f5bf;



  /* 组件背景透明度和模糊效果 */
  --component-bg-opacity: 1;
  --component-blur: 0px;
}



/* 暗色模式 */
[data-theme="dark"] {
  --bg-primary: #1a1d29;



  /* 组件背景透明度和模糊效果 */
  --component-bg-opacity: 1;
  --component-blur: 0px;
}



/* 基础样式类 */
.bg-theme {
  background: var(--bg-primary);
}




/* 自定义模态框样式 - 配合daisyUI使用 */
.modal {
  z-index: 10500 !important; /* 确保在所有其他元素之上 */
  backdrop-filter: blur(8px);
  background: rgba(0, 0, 0, 0.4);
  transition: transform 0.3s ease-out;
}

/* 模态框推移样式 - 已移除，模态框现在在main外部 */



/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease-out;
}
