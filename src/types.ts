export interface BookmarkWithMeta {
  // Chrome 书签原生字段
  id: string
  parentId?: string
  index?: number
  url?: string
  title: string
  dateAdded?: number
  dateGroupModified?: number
  children?: BookmarkWithMeta[]
  type?: 'bookmark' | 'folder'
  expanded?: boolean
  canEdit?: boolean

  // 本地扩展字段
  displayTitle?: string // 显示用的标题（不包含标签）
  icon?: string
  customIcon?: string // 用于编辑模态框的临时图标字段
  tags?: string[]
  tagsInput?: string

  // 可拓展字段支持
  [key: string]: any
}

export interface BookmarkGroup {
  id: string
  title: string
  parentId?: string
  path?: string[]
}
