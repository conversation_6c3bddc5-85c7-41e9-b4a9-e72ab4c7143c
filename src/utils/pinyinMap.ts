// 简单的拼音映射表，用于基本的拼音搜索功能
export const pinyinMap: Record<string, string[]> = {
  // 常用汉字的拼音映射
  '固': ['gu'],
  '态': ['tai'],
  '硬': ['ying'],
  '盘': ['pan'],
  '知': ['zhi'],
  '乎': ['hu'],
  '微': ['wei'],
  '信': ['xin'],
  '淘': ['tao'],
  '宝': ['bao'],
  '京': ['jing'],
  '东': ['dong'],
  '开': ['kai'],
  '发': ['fa'],
  '工': ['gong'],
  '具': ['ju'],
  '设': ['she'],
  '计': ['ji'],
  '资': ['zi'],
  '源': ['yuan'],
  '学': ['xue'],
  '习': ['xi'],
  '资': ['zi'],
  '料': ['liao'],
  '娱': ['yu'],
  '乐': ['le'],
  '购': ['gou'],
  '物': ['wu'],
  '代': ['dai'],
  '码': ['ma'],
  '问': ['wen'],
  '答': ['da'],
  '文': ['wen'],
  '档': ['dang'],
  '构': ['gou'],
  '建': ['jian'],
  '调': ['tiao'],
  '试': ['shi'],
  '包': ['bao'],
  '管': ['guan'],
  '理': ['li'],
  '在': ['zai'],
  '线': ['xian'],
  '编': ['bian'],
  '辑': ['ji'],
  '器': ['qi'],
  '灵': ['ling'],
  '感': ['gan'],
  '作': ['zuo'],
  '品': ['pin'],
  '集': ['ji'],
  '图': ['tu'],
  '片': ['pian'],
  '标': ['biao'],
  '字': ['zi'],
  '体': ['ti'],
  '配': ['pei'],
  '色': ['se'],
  '课': ['ke'],
  '程': ['cheng'],
  '视': ['shi'],
  '频': ['pin'],
  '电': ['dian'],
  '影': ['ying'],
  '路': ['lu'],
  '由': ['you'],
  '状': ['zhuang'],
  '框': ['kuang'],
  '架': ['jia'],
  '脚': ['jiao'],
  '手': ['shou'],
  '库': ['ku'],
  '迁': ['qian'],
  '移': ['yi'],
  '测': ['ce'],
  '社': ['she'],
  '交': ['jiao'],
  '硬': ['ying'],
  '件': ['jian']
}

// 获取汉字的拼音
export function getCharPinyin(char: string): string[] {
  return pinyinMap[char] || [char]
}

// 获取文本的完整拼音
export function getTextPinyin(text: string): string {
  return text.split('').map(char => {
    const pinyins = getCharPinyin(char)
    return pinyins[0] || char
  }).join(' ')
}

// 获取文本的拼音首字母
export function getTextInitials(text: string): string {
  return text.split('').map(char => {
    const pinyins = getCharPinyin(char)
    return pinyins[0] ? pinyins[0].charAt(0) : char
  }).join('')
}

// 检查文本是否匹配搜索关键词（支持拼音搜索）
export function matchesPinyin(text: string, keyword: string): boolean {
  const textLower = text.toLowerCase()
  const keywordLower = keyword.toLowerCase()
  
  // 1. 直接匹配
  if (textLower.includes(keywordLower)) return true
  
  // 2. 拼音全拼匹配
  const textPinyin = getTextPinyin(text).toLowerCase()
  const textPinyinNoSpace = textPinyin.replace(/\s+/g, '')
  if (textPinyin.includes(keywordLower) || textPinyinNoSpace.includes(keywordLower)) return true
  
  // 3. 拼音首字母匹配
  const textInitials = getTextInitials(text).toLowerCase()
  if (textInitials.includes(keywordLower)) return true
  
  // 4. 部分匹配
  const pinyinWords = textPinyin.split(/\s+/)
  for (const word of pinyinWords) {
    if (word.startsWith(keywordLower) || keywordLower.startsWith(word)) {
      return true
    }
  }
  
  return false
}
