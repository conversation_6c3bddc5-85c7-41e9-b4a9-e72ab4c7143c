<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼音搜索功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .test-input {
            width: 100%;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 16px;
        }
        .test-results {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 4px;
            min-height: 50px;
        }
        .bookmark-item {
            padding: 8px;
            margin: 5px 0;
            background: #e8f4fd;
            border-radius: 4px;
            border-left: 3px solid #007acc;
        }
        .highlight {
            background: #ffeb3b;
            padding: 2px 4px;
            border-radius: 2px;
            font-weight: bold;
        }
        .test-data {
            font-size: 12px;
            color: #666;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <h1>拼音搜索功能测试</h1>
    
    <div class="test-section">
        <h3>测试数据</h3>
        <div class="test-data">
            <p>书签数据：</p>
            <ul>
                <li>固态硬盘 (SSD)</li>
                <li>知乎 - 有问题，就会有答案</li>
                <li>微信网页版</li>
                <li>淘宝网 - 亚洲较大的网上交易平台</li>
                <li>京东商城</li>
                <li>GitHub - 代码托管平台</li>
                <li>Stack Overflow - 程序员问答社区</li>
                <li>Vue.js 官方文档</li>
                <li>React 中文文档</li>
                <li>MDN Web Docs</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h3>搜索测试</h3>
        <input type="text" class="test-input" id="searchInput" placeholder="输入搜索关键词（支持拼音首字母/全拼/英文混合搜索）">
        <div class="test-results" id="searchResults">
            <p>请输入搜索关键词进行测试...</p>
        </div>
    </div>

    <div class="test-section">
        <h3>测试用例建议</h3>
        <ul>
            <li><strong>拼音首字母：</strong> gt (固态), zh (知乎), wx (微信)</li>
            <li><strong>全拼：</strong> gutai (固态), zhihu (知乎), weixin (微信)</li>
            <li><strong>英文：</strong> github, stack, vue, react</li>
            <li><strong>混合搜索：</strong> gt硬盘, zh问答, vue文档</li>
            <li><strong>模糊匹配：</strong> guta (固态), zhi (知乎), wei (微信)</li>
        </ul>
    </div>

    <script type="module">
        import { pinyin, match } from 'pinyin-pro';
        
        // 测试书签数据
        const testBookmarks = [
            { id: '1', title: '固态硬盘', url: 'https://example.com/ssd', tags: ['硬件', '存储'] },
            { id: '2', title: '知乎', url: 'https://zhihu.com', tags: ['问答', '社区'] },
            { id: '3', title: '微信网页版', url: 'https://wx.qq.com', tags: ['聊天', '社交'] },
            { id: '4', title: '淘宝网', url: 'https://taobao.com', tags: ['购物', '电商'] },
            { id: '5', title: '京东商城', url: 'https://jd.com', tags: ['购物', '电商'] },
            { id: '6', title: 'GitHub', url: 'https://github.com', tags: ['代码', '开发'] },
            { id: '7', title: 'Stack Overflow', url: 'https://stackoverflow.com', tags: ['问答', '编程'] },
            { id: '8', title: 'Vue.js 官方文档', url: 'https://vuejs.org', tags: ['前端', '框架'] },
            { id: '9', title: 'React 中文文档', url: 'https://react.dev', tags: ['前端', '框架'] },
            { id: '10', title: 'MDN Web Docs', url: 'https://developer.mozilla.org', tags: ['文档', '前端'] }
        ];

        // 检查文本是否包含中文字符
        function containsChinese(text) {
            return /[\u4e00-\u9fa5]/.test(text);
        }

        // 拼音匹配函数
        function matchWithPinyin(text, query, options = {}) {
            if (!text || !query) return false;
            
            const normalizedQuery = query.toLowerCase().trim();
            const normalizedText = text.toLowerCase();
            
            // 1. 直接文本匹配
            if (normalizedText.includes(normalizedQuery)) {
                return true;
            }
            
            // 如果查询不包含中文，且文本也不包含中文，只进行英文匹配
            if (!containsChinese(query) && !containsChinese(text)) {
                return normalizedText.includes(normalizedQuery);
            }
            
            // 如果文本不包含中文，跳过拼音匹配
            if (!containsChinese(text)) {
                return false;
            }
            
            try {
                // 2. 拼音首字母匹配
                if (options.enableInitials !== false) {
                    const result = match(text, normalizedQuery, { 
                        precision: 'first',
                        continuous: false,
                        space: 'ignore'
                    });
                    if (result !== null) return true;
                }
                
                // 3. 全拼匹配
                if (options.enableFullPinyin !== false) {
                    const result = match(text, normalizedQuery, { 
                        precision: 'every',
                        continuous: false,
                        space: 'ignore'
                    });
                    if (result !== null) return true;
                }
                
                // 4. 模糊匹配
                if (options.enableFuzzy !== false) {
                    const startResult = match(text, normalizedQuery, { 
                        precision: 'start',
                        continuous: false,
                        space: 'ignore'
                    });
                    if (startResult !== null) return true;
                    
                    const anyResult = match(text, normalizedQuery, { 
                        precision: 'any',
                        continuous: false,
                        space: 'ignore'
                    });
                    if (anyResult !== null) return true;
                }
                
            } catch (error) {
                console.warn('拼音匹配出错:', error);
                return normalizedText.includes(normalizedQuery);
            }
            
            return false;
        }

        // 高亮匹配文本
        function highlightMatch(text, query) {
            if (!text || !query) return text;
            
            const normalizedQuery = query.toLowerCase().trim();
            
            // 直接文本匹配高亮
            const regex = new RegExp(`(${normalizedQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
            let highlighted = text.replace(regex, '<span class="highlight">$1</span>');
            
            // 如果没有直接匹配，尝试拼音匹配高亮
            if (!highlighted.includes('<span class="highlight">') && containsChinese(text)) {
                try {
                    const matchResult = match(text, normalizedQuery, { 
                        precision: 'first',
                        continuous: false,
                        space: 'ignore'
                    });
                    
                    if (matchResult !== null && matchResult.length > 0) {
                        const chars = text.split('');
                        matchResult.forEach(index => {
                            if (index < chars.length) {
                                chars[index] = `<span class="highlight">${chars[index]}</span>`;
                            }
                        });
                        highlighted = chars.join('');
                    }
                } catch (error) {
                    console.warn('拼音高亮出错:', error);
                }
            }
            
            return highlighted;
        }

        // 搜索函数
        function searchBookmarks(bookmarks, query) {
            if (!query.trim()) return [];
            
            return bookmarks.filter(bookmark => {
                // 搜索标题
                if (matchWithPinyin(bookmark.title, query)) {
                    return true;
                }
                
                // 搜索URL
                if (bookmark.url && bookmark.url.toLowerCase().includes(query.toLowerCase())) {
                    return true;
                }
                
                // 搜索标签
                if (bookmark.tags && bookmark.tags.length > 0) {
                    for (const tag of bookmark.tags) {
                        if (matchWithPinyin(tag, query)) {
                            return true;
                        }
                    }
                }
                
                return false;
            });
        }

        // 渲染搜索结果
        function renderResults(results, query) {
            const resultsContainer = document.getElementById('searchResults');
            
            if (results.length === 0) {
                resultsContainer.innerHTML = '<p>没有找到匹配的书签</p>';
                return;
            }
            
            const html = results.map(bookmark => {
                const highlightedTitle = highlightMatch(bookmark.title, query);
                const tagsHtml = bookmark.tags.map(tag => 
                    `<span style="background: #007acc; color: white; padding: 2px 6px; border-radius: 10px; font-size: 12px; margin-right: 5px;">${highlightMatch(tag, query)}</span>`
                ).join('');
                
                return `
                    <div class="bookmark-item">
                        <div><strong>${highlightedTitle}</strong></div>
                        <div style="font-size: 12px; color: #666; margin: 5px 0;">${bookmark.url}</div>
                        <div>${tagsHtml}</div>
                    </div>
                `;
            }).join('');
            
            resultsContainer.innerHTML = html;
        }

        // 搜索输入事件
        const searchInput = document.getElementById('searchInput');
        searchInput.addEventListener('input', (e) => {
            const query = e.target.value;
            const results = searchBookmarks(testBookmarks, query);
            renderResults(results, query);
        });

        console.log('拼音搜索测试页面已加载');
    </script>
</body>
</html>
