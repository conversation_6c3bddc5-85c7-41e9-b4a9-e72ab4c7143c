<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拼音转换测试</title>
</head>
<body>
    <h1>拼音转换测试</h1>
    <div id="results"></div>
    
    <script type="module">
        import { pinyin } from './node_modules/pinyin-pro/dist/index.esm.js';
        
        const results = document.getElementById('results');
        
        function testPinyin(text) {
            try {
                const fullPinyin = pinyin(text, { toneType: 'none', type: 'array' });
                const initials = pinyin(text, { pattern: 'first', toneType: 'none', type: 'array' });
                
                results.innerHTML += `
                    <div>
                        <h3>${text}</h3>
                        <p>全拼: ${fullPinyin.join(' ')}</p>
                        <p>首字母: ${initials.join('')}</p>
                        <p>连续拼音: ${fullPinyin.join('')}</p>
                    </div>
                `;
            } catch (error) {
                results.innerHTML += `<div>错误: ${error.message}</div>`;
            }
        }
        
        // 测试各种中文词汇
        testPinyin('固态');
        testPinyin('固态硬盘');
        testPinyin('知乎');
        testPinyin('微信');
        testPinyin('淘宝');
        testPinyin('京东');
    </script>
</body>
</html>
